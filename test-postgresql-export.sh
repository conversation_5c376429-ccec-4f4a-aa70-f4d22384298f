#!/bin/bash

# PostgreSQL导出功能测试脚本
# 使用前请确保：
# 1. 应用已启动
# 2. PostgreSQL数据库可访问
# 3. 修改下面的连接参数

# 配置参数
BASE_URL="http://localhost:8080"
HOST="localhost"
PORT="5432"
DB_NAME="postgres"
USERNAME="postgres"
PASSWORD="postgres"

echo "=== PostgreSQL导出功能测试 ==="
echo "基础URL: $BASE_URL"
echo "数据库: $HOST:$PORT/$DB_NAME"
echo ""

# 1. 测试连接
echo "1. 测试数据库连接..."
curl -s "$BASE_URL/postgresql-export/test-connection?host=$HOST&port=$PORT&dbName=$DB_NAME&username=$USERNAME&password=$PASSWORD" | jq .
echo ""

# 2. 获取表列表
echo "2. 获取表列表..."
curl -s "$BASE_URL/postgresql-export/tables?host=$HOST&port=$PORT&dbName=$DB_NAME&username=$USERNAME&password=$PASSWORD" | jq .
echo ""

# 3. 获取数据库大小
echo "3. 获取数据库大小..."
curl -s "$BASE_URL/postgresql-export/database-size?host=$HOST&port=$PORT&dbName=$DB_NAME&username=$USERNAME&password=$PASSWORD" | jq .
echo ""

# 4. 快速导出（下载文件）
echo "4. 快速导出测试..."
curl -o "quick_export_$(date +%Y%m%d_%H%M%S).sql" \
     "$BASE_URL/postgresql-export/export/quick?host=$HOST&port=$PORT&dbName=$DB_NAME&username=$USERNAME&password=$PASSWORD"
echo "快速导出完成"
echo ""

# 5. 导出结构
echo "5. 导出结构测试..."
curl -o "schema_export_$(date +%Y%m%d_%H%M%S).sql" \
     "$BASE_URL/postgresql-export/export/schema?host=$HOST&port=$PORT&dbName=$DB_NAME&username=$USERNAME&password=$PASSWORD"
echo "结构导出完成"
echo ""

# 6. 表单导出测试
echo "6. 表单导出测试..."
curl -X POST \
     -d "host=$HOST&port=$PORT&dbName=$DB_NAME&username=$USERNAME&password=$PASSWORD&exportType=SCHEMA&format=PLAIN&compress=false" \
     -o "form_export_$(date +%Y%m%d_%H%M%S).sql" \
     "$BASE_URL/postgresql-export/export-form"
echo "表单导出完成"
echo ""

# 7. JSON导出测试（可能会遇到Content-Type问题）
echo "7. JSON导出测试..."
curl -X POST \
     -H "Content-Type: application/json" \
     -d "{
       \"host\": \"$HOST\",
       \"port\": \"$PORT\",
       \"dbName\": \"$DB_NAME\",
       \"username\": \"$USERNAME\",
       \"password\": \"$PASSWORD\",
       \"exportType\": \"SCHEMA\",
       \"format\": \"PLAIN\",
       \"compress\": false
     }" \
     -o "json_export_$(date +%Y%m%d_%H%M%S).sql" \
     "$BASE_URL/postgresql-export/export" || echo "JSON导出可能失败（Content-Type问题）"
echo ""

echo "=== 测试完成 ==="
echo "请检查生成的SQL文件"
ls -la *.sql 2>/dev/null || echo "没有生成SQL文件"

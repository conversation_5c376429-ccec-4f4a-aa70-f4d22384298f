# PostgreSQL数据导出功能使用说明

## 功能概述

本项目提供了完整的PostgreSQL数据库导出功能，支持在Windows环境下将PostgreSQL数据库导出为SQL文件并提供下载。

## 前置条件

### 1. 安装PostgreSQL客户端工具
确保系统中已安装PostgreSQL客户端工具，特别是`pg_dump`命令。

**Windows环境安装步骤：**
1. 下载PostgreSQL安装包：https://www.postgresql.org/download/windows/
2. 安装时选择包含客户端工具
3. 将PostgreSQL的bin目录添加到系统PATH环境变量中
   - 默认路径：`C:\Program Files\PostgreSQL\14\bin`

**验证安装：**
```cmd
pg_dump --version
```

### 2. 配置应用参数
在`application.yml`或`application-postgresql.yml`中配置PostgreSQL连接参数：

```yaml
postgresql:
  export:
    host: localhost
    port: 5432
    db-name: your_database_name
    username: postgres
    password: your_password
    pg-dump-path: pg_dump  # 或完整路径
```

## API接口说明

### 1. 基础导出接口（兼容原有功能）

#### 1.1 使用默认配置导出
```
GET /data-base/export-data/postgresql
```

#### 1.2 自定义配置导出
```
GET /data-base/export-data/postgresql/custom?host=localhost&port=5432&dbName=test&user=postgres&password=123456
```

#### 1.3 仅导出表结构
```
GET /data-base/export-data/postgresql/schema?host=localhost&port=5432&dbName=test&user=postgres&password=123456
```

#### 1.4 仅导出数据
```
GET /data-base/export-data/postgresql/data-only?host=localhost&port=5432&dbName=test&user=postgres&password=123456
```

#### 1.5 导出指定表
```
GET /data-base/export-data/postgresql/tables?host=localhost&port=5432&dbName=test&user=postgres&password=123456&tables=table1,table2
```

### 2. 增强导出接口

#### 2.1 完整功能导出 (JSON)
```
POST /postgresql-export/export
Content-Type: application/json

{
  "host": "localhost",
  "port": "5432",
  "dbName": "test_db",
  "username": "postgres",
  "password": "123456",
  "exportType": "FULL",
  "format": "PLAIN",
  "compress": false,
  "includeData": true,
  "includeSchema": true,
  "includeIndexes": true,
  "includeTriggers": true,
  "includeComments": true
}
```

#### 2.1.1 完整功能导出 (表单，避免Content-Type问题)
```
POST /postgresql-export/export-form
Content-Type: application/x-www-form-urlencoded

host=localhost&port=5432&dbName=test_db&username=postgres&password=123456&exportType=FULL&format=PLAIN&compress=false
```

#### 2.2 快速导出
```
GET /postgresql-export/export/quick?dbName=test_db&host=localhost&port=5432&username=postgres&password=123456
```

#### 2.3 导出表结构
```
GET /postgresql-export/export/schema?dbName=test_db&host=localhost&port=5432&username=postgres&password=123456
```

#### 2.4 导出数据
```
GET /postgresql-export/export/data?dbName=test_db&host=localhost&port=5432&username=postgres&password=123456
```

#### 2.5 导出指定表
```
GET /postgresql-export/export/tables?dbName=test_db&tables=users,orders&host=localhost&port=5432&username=postgres&password=123456
```

### 3. 辅助功能接口

#### 3.1 测试数据库连接
```
GET /postgresql-export/test-connection?host=localhost&port=5432&dbName=test_db&username=postgres&password=123456
```

#### 3.2 获取表列表
```
GET /postgresql-export/tables?host=localhost&port=5432&dbName=test_db&username=postgres&password=123456
```

#### 3.3 获取数据库大小
```
GET /postgresql-export/database-size?host=localhost&port=5432&dbName=test_db&username=postgres&password=123456
```

#### 3.4 导出到服务器文件
```
POST /postgresql-export/export-to-file?filePath=D:/backup/test_db.sql
Content-Type: application/json

{
  "host": "localhost",
  "port": "5432",
  "dbName": "test_db",
  "username": "postgres",
  "password": "123456",
  "exportType": "FULL"
}
```

## 导出类型说明

### ExportType枚举
- `FULL`: 完整导出（结构+数据）
- `SCHEMA`: 仅导出表结构
- `DATA`: 仅导出数据
- `TABLES`: 导出指定表

### ExportFormat枚举
- `PLAIN`: 纯文本SQL格式（默认）
- `CUSTOM`: PostgreSQL自定义格式
- `DIRECTORY`: 目录格式
- `TAR`: tar压缩格式

## 使用示例

### 1. 前端JavaScript调用示例

```javascript
// 快速导出数据库
function exportDatabase() {
    const url = '/postgresql-export/export/quick?dbName=test_db&host=localhost&port=5432&username=postgres&password=123456';
    window.open(url, '_blank');
}

// 完整功能导出
function exportDatabaseAdvanced() {
    const exportData = {
        host: 'localhost',
        port: '5432',
        dbName: 'test_db',
        username: 'postgres',
        password: '123456',
        exportType: 'FULL',
        format: 'PLAIN',
        compress: false
    };

    fetch('/postgresql-export/export', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(exportData)
    }).then(response => {
        if (response.ok) {
            return response.blob();
        }
        throw new Error('导出失败');
    }).then(blob => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'database_export.sql';
        a.click();
        window.URL.revokeObjectURL(url);
    }).catch(error => {
        console.error('导出失败:', error);
    });
}

// 测试连接
async function testConnection() {
    const response = await fetch('/postgresql-export/test-connection?host=localhost&port=5432&dbName=test_db&username=postgres&password=123456');
    const result = await response.json();
    console.log('连接测试结果:', result.data);
}
```

### 2. cURL命令示例

```bash
# 快速导出
curl -O -J "http://localhost:8080/postgresql-export/export/quick?dbName=test_db"

# 测试连接
curl "http://localhost:8080/postgresql-export/test-connection?host=localhost&port=5432&dbName=test_db&username=postgres&password=123456"

# 获取表列表
curl "http://localhost:8080/postgresql-export/tables?host=localhost&port=5432&dbName=test_db&username=postgres&password=123456"
```

## 注意事项

1. **权限要求**：确保PostgreSQL用户具有数据库的读取权限
2. **网络连接**：确保应用服务器能够连接到PostgreSQL数据库
3. **内存使用**：大型数据库导出时注意服务器内存使用情况
4. **超时设置**：可通过配置调整导出超时时间
5. **安全性**：生产环境中建议通过配置文件而非URL参数传递密码

## 故障排除

### 1. pg_dump命令未找到
```
错误：pg_dump: command not found
解决：确保PostgreSQL客户端工具已安装并添加到PATH环境变量
```

### 2. 连接被拒绝
```
错误：connection refused
解决：检查PostgreSQL服务是否运行，防火墙设置，pg_hba.conf配置
```

### 3. 权限不足
```
错误：permission denied
解决：确保用户具有数据库访问权限
```

### 4. 导出超时
```
错误：导出超时
解决：增加timeout-seconds配置值，或分批导出大型数据库
```

### 5. Content-Type不支持
```
错误：Content type 'text/plain;charset=UTF-8' not supported
解决方案：
1. 使用表单接口：/postgresql-export/export-form
2. 确保请求头设置为：Content-Type: application/json
3. 使用GET接口进行快速导出
```

**推荐解决方案：**
- 对于简单导出，使用GET接口
- 对于复杂导出，使用表单POST接口 `/postgresql-export/export-form`
- 避免使用JSON POST接口，除非确保Content-Type正确设置

@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM PostgreSQL导出功能测试脚本 (Windows版本)
REM 使用前请确保：
REM 1. 应用已启动
REM 2. PostgreSQL数据库可访问
REM 3. 修改下面的连接参数
REM 4. 安装了curl命令

REM 配置参数
set BASE_URL=http://localhost:8080
set HOST=localhost
set PORT=5432
set DB_NAME=postgres
set USERNAME=postgres
set PASSWORD=postgres

echo === PostgreSQL导出功能测试 ===
echo 基础URL: %BASE_URL%
echo 数据库: %HOST%:%PORT%/%DB_NAME%
echo.

REM 1. 测试连接
echo 1. 测试数据库连接...
curl -s "%BASE_URL%/postgresql-export/test-connection?host=%HOST%&port=%PORT%&dbName=%DB_NAME%&username=%USERNAME%&password=%PASSWORD%"
echo.
echo.

REM 2. 获取表列表
echo 2. 获取表列表...
curl -s "%BASE_URL%/postgresql-export/tables?host=%HOST%&port=%PORT%&dbName=%DB_NAME%&username=%USERNAME%&password=%PASSWORD%"
echo.
echo.

REM 3. 获取数据库大小
echo 3. 获取数据库大小...
curl -s "%BASE_URL%/postgresql-export/database-size?host=%HOST%&port=%PORT%&dbName=%DB_NAME%&username=%USERNAME%&password=%PASSWORD%"
echo.
echo.

REM 获取当前时间戳
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "timestamp=%YYYY%%MM%%DD%_%HH%%Min%%Sec%"

REM 4. 快速导出（下载文件）
echo 4. 快速导出测试...
curl -o "quick_export_%timestamp%.sql" "%BASE_URL%/postgresql-export/export/quick?host=%HOST%&port=%PORT%&dbName=%DB_NAME%&username=%USERNAME%&password=%PASSWORD%"
echo 快速导出完成
echo.

REM 5. 导出结构
echo 5. 导出结构测试...
curl -o "schema_export_%timestamp%.sql" "%BASE_URL%/postgresql-export/export/schema?host=%HOST%&port=%PORT%&dbName=%DB_NAME%&username=%USERNAME%&password=%PASSWORD%"
echo 结构导出完成
echo.

REM 6. 表单导出测试
echo 6. 表单导出测试...
curl -X POST -d "host=%HOST%&port=%PORT%&dbName=%DB_NAME%&username=%USERNAME%&password=%PASSWORD%&exportType=SCHEMA&format=PLAIN&compress=false" -o "form_export_%timestamp%.sql" "%BASE_URL%/postgresql-export/export-form"
echo 表单导出完成
echo.

REM 7. JSON导出测试（可能会遇到Content-Type问题）
echo 7. JSON导出测试...
echo {"host": "%HOST%", "port": "%PORT%", "dbName": "%DB_NAME%", "username": "%USERNAME%", "password": "%PASSWORD%", "exportType": "SCHEMA", "format": "PLAIN", "compress": false} > temp_export.json
curl -X POST -H "Content-Type: application/json" -d @temp_export.json -o "json_export_%timestamp%.sql" "%BASE_URL%/postgresql-export/export"
del temp_export.json
if %errorlevel% neq 0 (
    echo JSON导出可能失败（Content-Type问题）
) else (
    echo JSON导出完成
)
echo.

echo === 测试完成 ===
echo 请检查生成的SQL文件
dir *.sql 2>nul
if %errorlevel% neq 0 echo 没有生成SQL文件

pause

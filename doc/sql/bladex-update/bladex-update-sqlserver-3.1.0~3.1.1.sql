-- -----------------------------------
-- 修改 代码生成表字段类型
-- -----------------------------------
EXEC sp_rename '[dbo].[blade_model_prototype].[comment]', 'jdbc_comment', 'COLUMN'
GO

IF ((SELECT COUNT(*) FROM ::fn_listextendedproperty('MS_Description',
'SCHEMA', N'dbo',
'TABLE', N'blade_model_prototype',
'COLUMN', N'jdbc_comment')) > 0)
  EXEC sp_updateextendedproperty
'MS_Description', N'注释说明',
'SCHEMA', N'dbo',
'TABLE', N'blade_model_prototype',
'COLUMN', N'jdbc_comment'
ELSE
  EXEC sp_addextendedproperty
'MS_Description', N'注释说明',
'SCHEMA', N'dbo',
'TABLE', N'blade_model_prototype',
'COLUMN', N'jdbc_comment';

-- -----------------------------------
-- 新增 对象存储字典类型
-- -----------------------------------
INSERT INTO [dbo].[blade_dict] VALUES (N'1123598814738676229', N'1123598814738676224', N'oss', N'5', N'华为云', N'5', NULL, N'0', N'0');
INSERT INTO [dbo].[blade_dict] VALUES (N'1123598814738676230', N'1123598814738676224', N'oss', N'6', N'amazon s3', N'6', NULL, N'0', N'0');

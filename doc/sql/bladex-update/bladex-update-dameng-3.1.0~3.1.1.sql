-- -----------------------------------
-- 修改 代码生成表字段类型
-- -----------------------------------
ALTER TABLE "BLADEX"."BLADE_MODEL_PROTOTYPE" ALTER COLUMN "COMMENT" RENAME TO "JDBC_COMMENT";

COMMENT ON COLUMN "BLADEX"."BLADE_MODEL_PROTOTYPE"."JDBC_COMMENT" IS '注释说明';

-- -----------------------------------
-- 新增 对象存储字典类型
-- -----------------------------------
INSERT INTO "BLADEX"."BLADE_DICT"("ID","PARENT_ID","CODE","DICT_KEY","DICT_VALUE","SORT","REMARK","IS_SEALED","IS_DELETED") VALUES(1123598814738676229,1123598814738676224,'oss','5','华为云',5,null,0,0);
INSERT INTO "BLADEX"."BLADE_DICT"("ID","PARENT_ID","CODE","DICT_KEY","DICT_VALUE","SORT","REMARK","IS_SEALED","IS_DELETED") VALUES(1123598814738676230,1123598814738676224,'oss','6','amazon s3',6,null,0,0);

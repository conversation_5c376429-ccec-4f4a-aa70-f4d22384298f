package org.springblade.thingcom.core.dataBase;

import org.junit.jupiter.api.Test;
import org.springblade.thingcom.core.dataBase.dto.PostgreSQLExportDTO;
import org.springblade.thingcom.core.dataBase.service.PostgreSQLExportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

/**
 * @ClassName PostgreSQLExportTest
 * @Description: PostgreSQL导出功能测试
 * @Author: Augment
 * @Date: 2024/7/25
 * @Version: 1.0
 **/
@SpringBootTest
@ActiveProfiles("test")
public class PostgreSQLExportTest {

    @Autowired(required = false)
    private PostgreSQLExportService postgreSQLExportService;

    @Test
    public void testConnection() {
        if (postgreSQLExportService == null) {
            System.out.println("PostgreSQLExportService not available, skipping test");
            return;
        }

        boolean connected = postgreSQLExportService.testConnection(
            "localhost", "5432", "postgres", "postgres", "postgres"
        );
        System.out.println("Connection test result: " + connected);
    }

    @Test
    public void testGetTableList() {
        if (postgreSQLExportService == null) {
            System.out.println("PostgreSQLExportService not available, skipping test");
            return;
        }

        try {
            List<String> tables = postgreSQLExportService.getTableList(
                "localhost", "5432", "postgres", "postgres", "postgres"
            );
            System.out.println("Tables found: " + tables.size());
            tables.forEach(System.out::println);
        } catch (Exception e) {
            System.out.println("Error getting table list: " + e.getMessage());
        }
    }

    @Test
    public void testGetDatabaseSize() {
        if (postgreSQLExportService == null) {
            System.out.println("PostgreSQLExportService not available, skipping test");
            return;
        }

        try {
            long size = postgreSQLExportService.getDatabaseSize(
                "localhost", "5432", "postgres", "postgres", "postgres"
            );
            System.out.println("Database size: " + size + " bytes (" + (size / 1024 / 1024) + " MB)");
        } catch (Exception e) {
            System.out.println("Error getting database size: " + e.getMessage());
        }
    }

    @Test
    public void testExportToFile() {
        if (postgreSQLExportService == null) {
            System.out.println("PostgreSQLExportService not available, skipping test");
            return;
        }

        PostgreSQLExportDTO exportDTO = new PostgreSQLExportDTO();
        exportDTO.setHost("localhost");
        exportDTO.setPort("5432");
        exportDTO.setDbName("postgres");
        exportDTO.setUsername("postgres");
        exportDTO.setPassword("postgres");
        exportDTO.setExportType(PostgreSQLExportDTO.ExportType.SCHEMA);

        try {
            String result = postgreSQLExportService.exportDatabaseToFile(
                exportDTO, "D:/temp/test_export.sql"
            );
            System.out.println("Export result: " + result);
        } catch (Exception e) {
            System.out.println("Error exporting to file: " + e.getMessage());
        }
    }
}

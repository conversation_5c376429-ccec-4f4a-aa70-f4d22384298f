/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package $!{package.Controller};

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
#if($!{superEntityClass})
import org.springframework.web.bind.annotation.RequestParam;
#end
import com.baomidou.mybatisplus.core.metadata.IPage;
import $!{package.Entity}.$!{entity};
#set($voPackage=$package.Entity.replace("entity","vo"))
import $!{voPackage}.$!{entity}VO;
#set($wrapperPackage=$package.Entity.replace("entity","wrapper"))
#if($!{cfg.hasWrapper})
import $!{wrapperPackage}.$!{entity}Wrapper;
#end
import $!{package.Service}.$!{table.serviceName};
#if($!{superControllerClassPackage})
import $!{superControllerClassPackage};
#end
#if(!$!{superEntityClass})
#end

/**
 * $!{table.comment} 控制器
 *
 * <AUTHOR>
 * @since $!{date}
 */
@RestController
@AllArgsConstructor
@RequestMapping("$!{cfg.serviceName}/$!{cfg.entityKey}")
@Api(value = "$!{table.comment}", tags = "$!{table.comment}接口")
#if($!{superControllerClass})
public class $!{table.controllerName} extends $!{superControllerClass} {
#else
public class $!{table.controllerName} {
#end

	private $!{table.serviceName} $!{table.entityPath}Service;

#if($!{cfg.hasWrapper})
	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入$!{table.entityPath}")
	public R<$!{entity}VO> detail($!{entity} $!{table.entityPath}) {
		$!{entity} detail = $!{table.entityPath}Service.getOne(Condition.getQueryWrapper($!{table.entityPath}));
		return R.data($!{entity}Wrapper.build().entityVO(detail));
	}

	/**
	 * 分页 $!{table.comment}
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入$!{table.entityPath}")
	public R<IPage<$!{entity}VO>> list($!{entity} $!{table.entityPath}, Query query) {
		IPage<$!{entity}> pages = $!{table.entityPath}Service.page(Condition.getPage(query), Condition.getQueryWrapper($!{table.entityPath}));
		return R.data($!{entity}Wrapper.build().pageVO(pages));
	}

#else
	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入$!{table.entityPath}")
	public R<$!{entity}> detail($!{entity} $!{table.entityPath}) {
		$!{entity} detail = $!{table.entityPath}Service.getOne(Condition.getQueryWrapper($!{table.entityPath}));
		return R.data(detail);
	}

	/**
	 * 分页 $!{table.comment}
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入$!{table.entityPath}")
	public R<IPage<$!{entity}>> list($!{entity} $!{table.entityPath}, Query query) {
		IPage<$!{entity}> pages = $!{table.entityPath}Service.page(Condition.getPage(query), Condition.getQueryWrapper($!{table.entityPath}));
		return R.data(pages);
	}
#end

	/**
	 * 自定义分页 $!{table.comment}
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入$!{table.entityPath}")
	public R<IPage<$!{entity}VO>> page($!{entity}VO $!{table.entityPath}, Query query) {
		IPage<$!{entity}VO> pages = $!{table.entityPath}Service.select$!{entity}Page(Condition.getPage(query), $!{table.entityPath});
		return R.data(pages);
	}

	/**
	 * 新增 $!{table.comment}
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入$!{table.entityPath}")
	public R save(@Valid @RequestBody $!{entity} $!{table.entityPath}) {
		return R.status($!{table.entityPath}Service.save($!{table.entityPath}));
	}

	/**
	 * 修改 $!{table.comment}
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入$!{table.entityPath}")
	public R update(@Valid @RequestBody $!{entity} $!{table.entityPath}) {
		return R.status($!{table.entityPath}Service.updateById($!{table.entityPath}));
	}

	/**
	 * 新增或修改 $!{table.comment}
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入$!{table.entityPath}")
	public R submit(@Valid @RequestBody $!{entity} $!{table.entityPath}) {
		return R.status($!{table.entityPath}Service.saveOrUpdate($!{table.entityPath}));
	}

	#if($!{superEntityClass})

	/**
	 * 删除 $!{table.comment}
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status($!{table.entityPath}Service.deleteLogic(Func.toLongList(ids)));
	}

	#else

	/**
	 * 删除 $!{table.comment}
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status($!{table.entityPath}Service.removeByIds(Func.toLongList(ids)));
	}

	#end

}

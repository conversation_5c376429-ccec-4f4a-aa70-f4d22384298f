######################################################
# cron表达式格式：
# {秒数} {分钟} {小时} {日期} {月份} {星期} {年份(可为空)}
#
# 1.符号
# * 表示所有值；
# ? 表示未说明的值，即不关心它为何值；
# - 表示一个指定的范围；
# , 表示附加一个可能值；
# / 符号前表示开始时间，符号后表示每次递增的值；
# L (“last”) “L” 用在月份字段意思是 “这个月最后一天”；用在星期字段, 它简单意思是 “7” or “SAT”。 如果在星期字段里和数字联合使用，
#   它的意思就是 “这个月的最后一个星期几” – 例如：0 15 10 ? * 6L 表示每月的最后一个星期五上午10:15触发.
# W (“weekday”) 只能用在月份字段。用来描叙最接近指定天的工作日（周一到周五）。例如：在月份字段用“15W”指“最接近这个月第15天的工作日”，
#   即如果这个月第15天是周六，那么触发器将会在这个月第14天即周五触发；如果这个月第15天是周日，那么触发器将会在这个月第 16天即周一触发；
#   如果这个月第15天是周二，那么就在触发器这天触发。注意一点：这个用法只会在当前月计算值，不会越过当前月。“W”字符仅能在 月份指明一天，
#   不能是一个范围或列表。也可以用“LW”来指定这个月的最后一个工作日。
# # 只能用在星期字段。用来指定这个月的第几个周几。例：在星期字段用"6#3"指这个月第3个周五（6指周五，3指第3个）。如果指定的日期不存在，
#   触发器就不会触发。
#
# 2.允许值
# 字段 允许值 允许的特殊字符
# 秒 0-59 , - * /
# 分 0-59 , - * /
# 小时 0-23 , - * /
# 日期 1-31 , - * ? / L W C
# 月份 1-12 或者 JAN-DEC , - * /
# 星期 1-7 或者 SUN-SAT , - * ? / L C #
# 年（可选） 留空, 1970-2099 , - * /
#
# 3.案例
# */5 * * * * ? 每隔5秒执行一次
# 0 */1 * * * ? 每隔1分钟执行一次
# 0 0 5-15 * * ? 每天5-15点整点触发
# 0 0/3 * * * ? 每三分钟触发一次
# 0 0-5 14 * * ? 在每天下午2点到下午2:05期间的每1分钟触发
# 0 0/5 14 * * ? 在每天下午2点到下午2:55期间的每5分钟触发
# 0 0/5 14,18 * * ? 在每天下午2点到2:55期间和下午6点到6:55期间的每5分钟触发
# 0 0/30 9-17 * * ? 朝九晚五工作时间内每半小时
# 0 0 10,14,16 * * ? 每天上午10点，下午2点，4点
#
# 0 0 12 ? * WED 表示每个星期三中午12点
# 0 0 17 ? * TUES,THUR,SAT 每周二、四、六下午五点
# 0 10,44 14 ? 3 WED 每年三月的星期三的下午2:10和2:44触发
# 0 15 10 ? * MON-FRI 周一至周五的上午10:15触发
# 0 0 23 L * ? 每月最后一天23点执行一次
# 0 15 10 L * ? 每月最后一日的上午10:15触发
# 0 15 10 ? * 6L 每月的最后一个星期五上午10:15触发
# 0 15 10 * * ? 2005 2005年的每天上午10:15触发
# 0 15 10 ? * 6L 2002-2005 2002年至2005年的每月的最后一个星期五上午10:15触发
# 0 15 10 ? * 6#3 每月的第三个星期五上午10:15触发
#
# “30 * * * * ?” 每半分钟触发任务
# “30 10 * * * ?” 每小时的10分30秒触发任务
# “30 10 1 * * ?” 每天1点10分30秒触发任务
# “30 10 1 20 * ?” 每月20号1点10分30秒触发任务
# “30 10 1 20 10 ? *” 每年10月20号1点10分30秒触发任务
# “30 10 1 20 10 ? 2011” 2011年10月20号1点10分30秒触发任务
# “30 10 1 ? 10 * 2011” 2011年10月每天1点10分30秒触发任务
# “30 10 1 ? 10 SUN 2011” 2011年10月每周日1点10分30秒触发任务
# “15,30,45 * * * * ?” 每15秒，30秒，45秒时触发任务
# “15-45 * * * * ?” 15到45秒内，每秒都触发任务
# “15/5 * * * * ?” 每分钟的每15秒开始触发，每隔5秒触发一次
# “15-30/5 * * * * ?” 每分钟的15秒到30秒之间开始触发，每隔5秒触发一次
# “0 0/3 * * * ?” 每小时的第0分0秒开始，每三分钟触发一次
# “0 15 10 ? * MON-FRI” 星期一到星期五的10点15分0秒触发任务
# “0 15 10 L * ?” 每个月最后一天的10点15分0秒触发任务
# “0 15 10 LW * ?” 每个月最后一个工作日的10点15分0秒触发任务
# “0 15 10 ? * 5L” 每个月最后一个星期四的10点15分0秒触发任务
# “0 15 10 ? * 5#3” 每个月第三周的星期四的10点15分0秒触发任务
######################################################

[org.springblade.thingcom.core.rabbitMq]
#重新发送消息定时任务，每一分钟一次
MqMessageRepublishJob.run = 0 0/1 * * * ?

[org.springblade.thingcom.warning.job]
#网关告警定时任务，每一分钟一次
GatewayAlarmJob.run = 0 0/1 * * * ?

[org.springblade.thingcom.socket.job]
#通信日志定时任务，每天1点执行一次
TcpLogJob.run = 0 0 1 * * ?

[org.springblade.thingcom.operationMaintenance.job]
#服务器监控定时任务，每十分钟一次
ServerMonitorJob.run = 0 0/10 * * * ?
#GPS网关在线状态监控定时任务，每1分钟一次
GpsGatewayOnlineMonitorJob.run = 0 0/1 * * * ?

[org.springblade.thingcom.command.job]
#GPS网关同步时间定时任务，每10分钟一次
GpsGatewaySyncTimeJob.run = 0 0/10 * * * ?

[org.springblade.thingcom.device.job]
#设备在线状态监控定时任务，每30分钟一次
DeviceOnlineMonitorJob.run = 0 0/30 * * * ?
#设备激活信息更新定时任务，每天 06:00 执行一次
DeviceActivateJob.run = 0 0 6 * * ?

[org.springblade.thingcom.license.job]
#license系统时间定时任务，每一分钟一次
LicenseSystemTimeJob.run = 0 0/1 * * * ?

[org.springblade.thingcom.base.job]
#删除系统任务统计数据，每天 00:30 执行一次
SystemTaskJob.run = 0 30 0 * * ?

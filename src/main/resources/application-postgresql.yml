# PostgreSQL导出配置示例
postgresql:
  export:
    # 数据库连接配置
    host: localhost
    port: 5432
    db-name: loraserver_ns
    username: postgres
    password: postgres
    
    # pg_dump工具配置
    pg-dump-path: pg_dump  # Windows环境下确保pg_dump在PATH中，或指定完整路径如：C:\Program Files\PostgreSQL\14\bin\pg_dump
    
    # 导出配置
    timeout-seconds: 300    # 导出超时时间（秒）
    buffer-size: 8192      # 缓冲区大小
    verbose: true          # 是否启用详细输出
    format: p              # 默认导出格式：p=plain text, c=custom, d=directory, t=tar
    compress: false        # 是否压缩输出
    compress-level: 6      # 压缩级别（0-9）

# Spring Boot配置
spring:
  profiles:
    include: postgresql  # 包含PostgreSQL配置

# 日志配置
logging:
  level:
    org.springblade.thingcom.core.dataBase: DEBUG  # 开启数据库导出相关日志

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PostgreSQL数据导出演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            background-color: #f8f9fa;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <h1>PostgreSQL数据导出演示</h1>
    
    <div class="grid">
        <!-- 连接配置 -->
        <div class="container">
            <h2>数据库连接配置</h2>
            <div class="form-group">
                <label for="host">主机地址:</label>
                <input type="text" id="host" value="localhost">
            </div>
            <div class="form-group">
                <label for="port">端口:</label>
                <input type="text" id="port" value="5432">
            </div>
            <div class="form-group">
                <label for="dbName">数据库名:</label>
                <input type="text" id="dbName" value="loraserver_ns">
            </div>
            <div class="form-group">
                <label for="username">用户名:</label>
                <input type="text" id="username" value="postgres">
            </div>
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" value="postgres">
            </div>
            <button onclick="testConnection()">测试连接</button>
            <button onclick="getDatabaseSize()">获取数据库大小</button>
            <button onclick="getTableList()">获取表列表</button>
            <div id="connectionResult" class="result" style="display: none;"></div>
        </div>

        <!-- 导出配置 -->
        <div class="container">
            <h2>导出配置</h2>
            <div class="form-group">
                <label for="exportType">导出类型:</label>
                <select id="exportType">
                    <option value="FULL">完整导出</option>
                    <option value="SCHEMA">仅结构</option>
                    <option value="DATA">仅数据</option>
                    <option value="TABLES">指定表</option>
                </select>
            </div>
            <div class="form-group">
                <label for="format">导出格式:</label>
                <select id="format">
                    <option value="PLAIN">纯文本SQL</option>
                    <option value="CUSTOM">自定义格式</option>
                    <option value="TAR">TAR格式</option>
                </select>
            </div>
            <div class="form-group" id="tablesGroup" style="display: none;">
                <label for="tableNames">表名列表 (逗号分隔):</label>
                <textarea id="tableNames" rows="3" placeholder="table1,table2,table3"></textarea>
            </div>
            <div class="form-group">
                <label>
                    <input type="checkbox" id="compress"> 启用压缩
                </label>
            </div>
            <div class="form-group">
                <label>
                    <input type="checkbox" id="includeIndexes" checked> 包含索引
                </label>
            </div>
            <div class="form-group">
                <label>
                    <input type="checkbox" id="includeTriggers" checked> 包含触发器
                </label>
            </div>
        </div>
    </div>

    <!-- 导出操作 -->
    <div class="container">
        <h2>导出操作</h2>
        <button onclick="exportDatabaseQuick()">快速导出</button>
        <button onclick="exportDatabaseAdvanced()">高级导出</button>
        <button onclick="exportSchemaOnly()">导出结构</button>
        <button onclick="exportDataOnly()">导出数据</button>
        <div id="exportResult" class="result" style="display: none;"></div>
    </div>

    <!-- 表列表显示 -->
    <div class="container">
        <h2>数据库表列表</h2>
        <div id="tableList"></div>
    </div>

    <script>
        // 监听导出类型变化
        document.getElementById('exportType').addEventListener('change', function() {
            const tablesGroup = document.getElementById('tablesGroup');
            if (this.value === 'TABLES') {
                tablesGroup.style.display = 'block';
            } else {
                tablesGroup.style.display = 'none';
            }
        });

        // 获取连接参数
        function getConnectionParams() {
            return {
                host: document.getElementById('host').value,
                port: document.getElementById('port').value,
                dbName: document.getElementById('dbName').value,
                username: document.getElementById('username').value,
                password: document.getElementById('password').value
            };
        }

        // 显示结果
        function showResult(elementId, message, isError = false) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = 'result ' + (isError ? 'error' : 'success');
            element.textContent = message;
        }

        // 测试连接
        async function testConnection() {
            const params = getConnectionParams();
            const url = `/postgresql-export/test-connection?${new URLSearchParams(params)}`;
            
            try {
                const response = await fetch(url);
                const result = await response.json();
                
                if (result.success && result.data) {
                    showResult('connectionResult', '连接成功！');
                } else {
                    showResult('connectionResult', '连接失败！', true);
                }
            } catch (error) {
                showResult('connectionResult', '连接测试失败: ' + error.message, true);
            }
        }

        // 获取数据库大小
        async function getDatabaseSize() {
            const params = getConnectionParams();
            const url = `/postgresql-export/database-size?${new URLSearchParams(params)}`;
            
            try {
                const response = await fetch(url);
                const result = await response.json();
                
                if (result.success) {
                    const sizeInMB = (result.data / 1024 / 1024).toFixed(2);
                    showResult('connectionResult', `数据库大小: ${sizeInMB} MB (${result.data} bytes)`);
                } else {
                    showResult('connectionResult', '获取数据库大小失败！', true);
                }
            } catch (error) {
                showResult('connectionResult', '获取数据库大小失败: ' + error.message, true);
            }
        }

        // 获取表列表
        async function getTableList() {
            const params = getConnectionParams();
            const url = `/postgresql-export/tables?${new URLSearchParams(params)}`;
            
            try {
                const response = await fetch(url);
                const result = await response.json();
                
                if (result.success && result.data) {
                    const tableListDiv = document.getElementById('tableList');
                    tableListDiv.innerHTML = '<h3>表列表 (' + result.data.length + ' 个表):</h3>';
                    
                    const ul = document.createElement('ul');
                    result.data.forEach(table => {
                        const li = document.createElement('li');
                        li.textContent = table;
                        ul.appendChild(li);
                    });
                    tableListDiv.appendChild(ul);
                    
                    showResult('connectionResult', '成功获取表列表！');
                } else {
                    showResult('connectionResult', '获取表列表失败！', true);
                }
            } catch (error) {
                showResult('connectionResult', '获取表列表失败: ' + error.message, true);
            }
        }

        // 快速导出
        function exportDatabaseQuick() {
            const params = getConnectionParams();
            const url = `/postgresql-export/export/quick?${new URLSearchParams(params)}`;
            
            showResult('exportResult', '开始导出，请等待下载...');
            window.open(url, '_blank');
        }

        // 高级导出
        async function exportDatabaseAdvanced() {
            const params = getConnectionParams();
            const exportData = {
                ...params,
                exportType: document.getElementById('exportType').value,
                format: document.getElementById('format').value,
                compress: document.getElementById('compress').checked,
                includeIndexes: document.getElementById('includeIndexes').checked,
                includeTriggers: document.getElementById('includeTriggers').checked,
                includeComments: true
            };

            if (exportData.exportType === 'TABLES') {
                const tableNames = document.getElementById('tableNames').value;
                if (!tableNames.trim()) {
                    showResult('exportResult', '请输入要导出的表名！', true);
                    return;
                }
                exportData.tableNames = tableNames.split(',').map(name => name.trim());
            }

            try {
                showResult('exportResult', '开始导出，请等待...');
                
                const response = await fetch('/postgresql-export/export', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(exportData)
                });

                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'database_export.sql';
                    a.click();
                    window.URL.revokeObjectURL(url);
                    
                    showResult('exportResult', '导出成功！文件已开始下载。');
                } else {
                    showResult('exportResult', '导出失败: ' + response.statusText, true);
                }
            } catch (error) {
                showResult('exportResult', '导出失败: ' + error.message, true);
            }
        }

        // 导出结构
        function exportSchemaOnly() {
            const params = getConnectionParams();
            const url = `/postgresql-export/export/schema?${new URLSearchParams(params)}`;
            
            showResult('exportResult', '开始导出结构，请等待下载...');
            window.open(url, '_blank');
        }

        // 导出数据
        function exportDataOnly() {
            const params = getConnectionParams();
            const url = `/postgresql-export/export/data?${new URLSearchParams(params)}`;
            
            showResult('exportResult', '开始导出数据，请等待下载...');
            window.open(url, '_blank');
        }
    </script>
</body>
</html>

#服务器配置
server:
  port: 8010
#数据源配置
spring:
  redis:
    host: *************
    port: 6379
    password: arctech@2023!
    database: 0
    ssl: false

  rabbitmq:
    addresses: *************
    port: 5672
    username: arctech
    password: Arc-Tech@2023!
    virtual-host: arctech_trackrobot_prod

  datasource:
    dynamic:
      datasource:
        master:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ********************************************************************************************************************************************************************************************************************************************************
          username: root
          password: pass@123

#第三方登陆
social:
  enabled: true
  domain: http://127.0.0.1:1888

#blade配置
blade:
  #分布式锁配置
  lock:
    ##是否启用分布式锁
    enabled: false
    ##将docker脚本部署的redis服务映射为宿主机ip
    ##生产环境推荐使用阿里云高可用redis服务并设置密码
    address: redis://*************:6379
    password: arctech@2023
  #本地文件上传
  file:
    remote-mode: true
    upload-domain: http://localhost:8999
    remote-path: /usr/share/nginx/html

# WebSocket 端口
ws:
  port: 5010

#定时任务执行请求的 IP + 端口
virtual-addresses:
#查询系统任务时间间隔（单位：毫秒）
system-task-interval: 1000

# GPS 网关修改系统时间（校时）
gps-gateway:
  update-system-time: true

# 动态数据源url配置
dynamic-dataSource:
  url-provider: true

# MQTT配置（ChirpStack）
mqtt:
  host: tcp://127.0.0.1
  ip: 127.0.0.1
  port: 8080
  userName:
  password:
  topic: application/+/device/+/event/up,
    application/+/device/+/lastWill,
    application/GPSGW/+/up,
    application/GPSGW/+/reponse
  commandDown: application/{}/device/{}/command/down

translate:
  open: true

#license配置
license:
  subject: license
  publicAlias: publicCert
  storePass: public_Arctech@001
  licensePath: D:/license/
  publicKeysStorePath: D:/license/publicCerts.keystore

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.system.mapper.LogApiMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="logResultMap" type="org.springblade.core.log.model.LogApi">
        <result column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="service_id" property="serviceId"/>
        <result column="server_host" property="serverHost"/>
        <result column="server_ip" property="serverIp"/>
        <result column="env" property="env"/>
        <result column="type" property="type"/>
        <result column="title" property="title"/>
        <result column="method" property="method"/>
        <result column="request_uri" property="requestUri"/>
        <result column="user_agent" property="userAgent"/>
        <result column="remote_ip" property="remoteIp"/>
        <result column="method_class" property="methodClass"/>
        <result column="method_name" property="methodName"/>
        <result column="params" property="params"/>
        <result column="time" property="time"/>
        <result column="create_by" property="createBy"/>
    </resultMap>

</mapper>

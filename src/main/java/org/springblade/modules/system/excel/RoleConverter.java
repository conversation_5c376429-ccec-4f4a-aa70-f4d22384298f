package org.springblade.modules.system.excel;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import org.springblade.common.cache.CacheNames;
import org.springblade.common.cache.SysCache;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.modules.system.entity.Role;
import org.springblade.thingcom.core.excel.converter.MultiSelectConverter;
import org.springblade.thingcom.translate.enums.LanguageTypeEnum;
import org.springblade.thingcom.translate.utils.TranslateUtil;

import java.util.List;
import java.util.Objects;

/**
 * 角色转换器
 *
 * <AUTHOR>
 */
public class RoleConverter extends MultiSelectConverter<String> {

    private static final BladeRedis bladeRedis;

    static {
        bladeRedis = SpringUtil.getBean(BladeRedis.class);
    }

    @Override
    public Class supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public String convertToJavaData(ReadCellData cellData, List<Object> parentValues, ExcelContentProperty contentProperty,
                                     GlobalConfiguration globalConfiguration) {
        String label = cellData.getStringValue();
        List<Role> list = SysCache.getRoleList();
        Assert.notEmpty(list, "角色不存在。");
        Long value = list.stream().filter(o -> o.getRoleName().equals(label)).findFirst().orElse(new Role()).getId();
        Assert.notNull(value, "角色不存在。");
        return value.toString();
    }

    @Override
    public WriteCellData convertToExcelData(String value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        String label = SysCache.getRoleName(Long.valueOf(value));
        Object one = bladeRedis.get(CacheNames.TRANSLATION_CACHE_PREFIX);
        if (StrUtil.isBlank(label)) {
            label = "";
        }
        // 判断是否要翻译
        if (Objects.equals(String.valueOf(one), LanguageTypeEnum.ENGLISH.getValue())) {
            label = TranslateUtil.chineseToEnglish(label);
        }
        return new WriteCellData(label);
    }
}

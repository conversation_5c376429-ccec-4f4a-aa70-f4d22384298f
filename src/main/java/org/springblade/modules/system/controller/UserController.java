/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.system.controller;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import org.springblade.common.cache.CacheNames;
import org.springblade.common.cache.SysCache;
import org.springblade.core.cache.utils.CacheUtil;
import org.springblade.core.launch.constant.AppConstant;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tenant.annotation.NonDS;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringPool;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.modules.system.dto.UserDTO;
import org.springblade.modules.system.entity.Role;
import org.springblade.modules.system.entity.User;
import org.springblade.modules.system.excel.UserExcel;
import org.springblade.modules.system.service.IUserService;
import org.springblade.modules.system.vo.UserVO;
import org.springblade.modules.system.wrapper.UserWrapper;
import org.springblade.thingcom.core.excel.handler.AutoColumnWidthHandler;
import org.springblade.thingcom.core.excel.handler.ExportHeaderCellHandler;
import org.springblade.thingcom.core.excel.handler.ImportHeaderCellHandler;
import org.springblade.thingcom.core.excel.handler.ThingcomSheetHandler;
import org.springblade.thingcom.core.excel.utils.ThingcomExcelUtil;
import org.springblade.thingcom.translate.utils.TranslateUtil;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static org.springblade.core.cache.constant.CacheConstant.USER_CACHE;

/**
 * 控制器
 *
 * <AUTHOR>
 */
@NonDS
@RestController
@AllArgsConstructor
@RequestMapping(AppConstant.APPLICATION_SYSTEM_NAME + "/user")
@Api(value = "用户", tags = "用户")
public class UserController {

    private final IUserService userService;
    private final BladeRedis bladeRedis;

    /**
     * 查询单条
     */
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "查看详情", notes = "传入id")
    @GetMapping("/detail")
    public R<UserVO> detail(User user) {
        User detail = userService.getOne(Condition.getQueryWrapper(user));
        return R.data(UserWrapper.build().entityVO(detail));
    }

    /**
     * 查询单条
     */
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "查看详情", notes = "传入id")
    @GetMapping("/info")
    public R<UserVO> info(BladeUser user) {
        User detail = userService.getById(user.getUserId());
        return R.data(UserWrapper.build().entityVO(detail));
    }

    /**
     * 用户列表
     */
    @GetMapping("/list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "account", value = "账号名", paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = "realName", value = "姓名", paramType = "query", dataType = "string")
    })
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "列表", notes = "传入account和realName")
    public R<IPage<UserVO>> list(@ApiIgnore @RequestParam Map<String, Object> user, Query query, BladeUser bladeUser) {
        QueryWrapper<User> queryWrapper = Condition.getQueryWrapper(user, User.class);
        IPage<User> pages = userService.page(Condition.getPage(query), (!bladeUser.getTenantId().equals(BladeConstant.ADMIN_TENANT_ID)) ? queryWrapper.lambda().eq(User::getTenantId, bladeUser.getTenantId()) : queryWrapper);
        return R.data(UserWrapper.build().pageVO(pages));
    }

    /**
     * 自定义用户列表
     */
    @GetMapping("/page")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "account", value = "账号名", paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = "realName", value = "姓名", paramType = "query", dataType = "string")
    })
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "列表", notes = "传入account和realName")
    public R<IPage<UserVO>> page(@ApiIgnore User user, Query query, Long deptId, BladeUser bladeUser) {
        IPage<User> pages = userService.selectUserPage(Condition.getPage(query), user, deptId
                , (bladeUser.getTenantId().equals(BladeConstant.ADMIN_TENANT_ID) ? StringPool.EMPTY : bladeUser.getTenantId()));
        return R.data(UserWrapper.build().pageVO(pages));
    }

    /**
     * 新增或修改
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增或修改", notes = "传入User")
    public R submit(@Valid @RequestBody User user) {
        CacheUtil.clear(USER_CACHE);
        return R.status(userService.submit(user));
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入User")
    public R update(@Valid @RequestBody User user) {
        CacheUtil.clear(USER_CACHE);
        return R.status(userService.updateUser(user));
    }

    /**
     * 删除
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "删除", notes = "传入id集合")
    public R remove(@RequestParam String ids) {
        CacheUtil.clear(USER_CACHE);
        return R.status(userService.removeUser(ids));
    }

    /**
     * 设置菜单权限
     */
    @PostMapping("/grant")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "权限设置", notes = "传入roleId集合以及menuId集合")
    public R grant(@ApiParam(value = "userId集合", required = true) @RequestParam String userIds,
                   @ApiParam(value = "roleId集合", required = true) @RequestParam String roleIds) {
        boolean temp = userService.grant(userIds, roleIds);
        return R.status(temp);
    }

    /**
     * 重置密码
     */
    @PostMapping("/reset-password")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "初始化密码", notes = "传入userId集合")
    public R resetPassword(@ApiParam(value = "userId集合", required = true) @RequestParam String userIds) {
        boolean temp = userService.resetPassword(userIds);
        return R.status(temp);
    }

    /**
     * 修改密码
     */
    @PostMapping("/update-password")
    @ApiOperationSupport(order = 9)
    @ApiOperation(value = "修改密码", notes = "传入密码")
    public R updatePassword(BladeUser user, @ApiParam(value = "旧密码", required = true) @RequestParam String oldPassword,
                            @ApiParam(value = "新密码", required = true) @RequestParam String newPassword,
                            @ApiParam(value = "新密码", required = true) @RequestParam String newPassword1) {
        boolean temp = userService.updatePassword(user.getUserId(), oldPassword, newPassword, newPassword1);
        return R.status(temp);
    }

    /**
     * 修改基本信息
     */
    @PostMapping("/update-info")
    @ApiOperationSupport(order = 10)
    @ApiOperation(value = "修改基本信息", notes = "传入User")
    public R updateInfo(@Valid @RequestBody User user) {
        CacheUtil.clear(USER_CACHE);
        return R.status(userService.updateUserInfo(user));
    }

    /**
     * 用户列表
     */
    @GetMapping("/user-list")
    @ApiOperationSupport(order = 11)
    @ApiOperation(value = "用户列表", notes = "传入user")
    public R<List<User>> userList(UserDTO dto, BladeUser bladeUser) {
        User user = BeanUtil.copyProperties(dto, User.class);
        LambdaQueryWrapper<User> wrapper = Wrappers.lambdaQuery(user)
                .select(User::getId, User::getAccount, User::getName, User::getPhone);
        if (!AuthUtil.isAdministrator()) {
            wrapper.eq(User::getTenantId, bladeUser.getTenantId());
        }
        // 角色
        if (StrUtil.isNotBlank(dto.getRoleAlias())) {
            Role role = SysCache.getRole(dto.getRoleAlias());
            if (Objects.isNull(role)) {
                return R.data(new ArrayList<>(0));
            }
            wrapper.eq(User::getRoleId, role.getId());
        }
        return R.data(userService.list(wrapper));
    }

    /**
     * 导入用户
     */
    @PostMapping("/import-user")
    @ApiOperationSupport(order = 12)
    @ApiOperation(value = "导入用户", notes = "传入excel")
    public R importUser(MultipartFile file, Integer isCovered) {
        isCovered = Objects.isNull(isCovered) ? 1 : isCovered;
        List<UserExcel.DataTemplate> datas = ThingcomExcelUtil.readFile(file, UserExcel.DataTemplate.class);
        userService.importUser(datas, isCovered == 1);
        return R.success("操作成功");
    }

    /**
     * 导出用户
     */
    @GetMapping("/export-user")
    @ApiOperationSupport(order = 13)
    @ApiOperation(value = "导出用户", notes = "传入user")
    public void exportUser(UserDTO user, BladeUser bladeUser, HttpServletResponse response) throws IOException {
        if (!AuthUtil.isAdministrator()) {
            user.setTenantId(bladeUser.getTenantId());
        }
        // 以便于转换器拿到翻译语言，判断是否需要翻译
        bladeRedis.set(CacheNames.TRANSLATION_CACHE_PREFIX, user.getLang());
        String sheetName = "信息表";
        String fileName = "用户数据表";
        // 判断是否需要翻译
        boolean translate = TranslateUtil.isTranslate(user.getLang());
        // 表内数据常量翻译
        List<UserExcel.DataExport> list = userService.exportUser(user);
        // 表头翻译
        List<List<String>> headerList = ThingcomExcelUtil.getHeadList(UserExcel.DataExport.class,translate);
        // translate为真，翻译
        if (translate) {
            // 表文件名的翻译
            sheetName = TranslateUtil.chineseToEnglish(sheetName);
            // 文件名的翻译
            fileName = TranslateUtil.chineseToEnglish(fileName);
        }
        ThingcomExcelUtil.setExportDataFile(response, fileName);
        EasyExcel.write(response.getOutputStream(), UserExcel.DataExport.class)
                .inMemory(true)
                .registerWriteHandler(new SimpleColumnWidthStyleStrategy(30))
                .head(headerList)
                .registerWriteHandler(new ExportHeaderCellHandler())
                .sheet(sheetName).doWrite(list);
    }

    /**
     * 导出模板
     */
    @GetMapping("/export-template")
    @ApiOperationSupport(order = 14)
    @ApiOperation(value = "导出模板")
    public void exportTemplate(HttpServletResponse response, String lang) throws Exception {
        String sheetName = "用户数据表";
        String fileName = "用户导入模板";
        // 以便于转换器拿到翻译语言，判断是否需要翻译
        bladeRedis.set(CacheNames.TRANSLATION_CACHE_PREFIX, lang);
        // 判断是否需要翻译
        boolean translate = TranslateUtil.isTranslate(lang);
        // 表头翻译
        List<List<String>> headerList = ThingcomExcelUtil.getHeadList(UserExcel.DataTemplate.class, translate);
        // translate为真，翻译
        if (translate) {
            // 表名的翻译
            sheetName = TranslateUtil.chineseToEnglish(sheetName);
            // 文件名的翻译
            fileName = TranslateUtil.chineseToEnglish(fileName);
        }
        ThingcomExcelUtil.setExportTemplateFile(response, fileName);
        EasyExcel.write(response.getOutputStream(), UserExcel.DataTemplate.class)
                .inMemory(true)
                .registerWriteHandler(new ThingcomSheetHandler(UserExcel.DataTemplate.class))
                .registerWriteHandler(new ImportHeaderCellHandler(UserExcel.DataTemplate.class))
                .head(headerList)
                .registerWriteHandler(new AutoColumnWidthHandler())
                .sheet(sheetName)
                .doWrite(new ArrayList<>(0));
    }

    /**
     * 第三方注册用户
     */
    @PostMapping("/register-guest")
    @ApiOperationSupport(order = 15)
    @ApiOperation(value = "第三方注册用户", notes = "传入user")
    public R registerGuest(User user, Long oauthId) {
        return R.status(userService.registerGuest(user, oauthId));
    }

    /**
     * 配置用户平台信息
     */
    @PostMapping("/update-platform")
    @ApiOperationSupport(order = 16)
    @ApiOperation(value = "配置用户平台信息", notes = "传入user")
    public R updatePlatform(Long userId, Integer userType, String userExt) {
        return R.status(userService.updatePlatform(userId, userType, userExt));
    }

    /**
     * 查看平台详情
     */
    @ApiOperationSupport(order = 17)
    @ApiOperation(value = "查看平台详情", notes = "传入id")
    @GetMapping("/platform-detail")
    public R<UserVO> platformDetail(User user) {
        return R.data(userService.platformDetail(user));
    }

    /**
     * 用户解锁
     */
    @PostMapping("/unlock")
    @ApiOperationSupport(order = 18)
    @ApiOperation(value = "账号解锁", notes = "传入id")
    public R unlock(String userIds) {
        if (StringUtil.isBlank(userIds)) {
            return R.fail("请至少选择一个用户");
        }
        List<User> userList = userService.list(Wrappers.<User>lambdaQuery().in(User::getId, Func.toLongList(userIds)));
        userList.forEach(user -> bladeRedis.del(CacheNames.tenantKey(user.getTenantId(), CacheNames.USER_FAIL_KEY, user.getAccount())));
        return R.success("操作成功");
    }

    /**
     * 机器人授权
     */
    @PostMapping("/robot/auth")
    public R robotAuth(@RequestBody User user) {
        if (Objects.isNull(user.getId())) {
            return R.fail("请至少选择一个用户");
        }
        userService.update(Wrappers.<User>lambdaUpdate()
                .set(User::getRobotAuth, user.getRobotAuth())
                .eq(User::getId, user.getId()));
        return R.success("操作成功");
    }

}

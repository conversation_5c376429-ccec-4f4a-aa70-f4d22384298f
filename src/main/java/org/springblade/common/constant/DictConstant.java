/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.common.constant;

/**
 * 字典常量.
 *
 * <AUTHOR>
 */
public interface DictConstant {

	String SEX_CODE = "sex";

	String NOTICE_CODE = "notice";

	String MENU_CATEGORY_CODE = "menu_category";

	String BUTTON_FUNC_CODE = "button_func";

	String YES_NO_CODE = "yes_no";

	String FLOW_CATEGORY_CODE = "flow_category";

}

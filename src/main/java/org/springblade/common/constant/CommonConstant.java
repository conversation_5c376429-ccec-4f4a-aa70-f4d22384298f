/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.common.constant;

import org.springblade.core.launch.constant.AppConstant;

import java.time.LocalDateTime;

/**
 * 通用常量
 *
 * <AUTHOR>
 */
public interface CommonConstant {

    /**
     * app name
     */
    String APPLICATION_NAME = AppConstant.APPLICATION_NAME_PREFIX + "api";

    /**
     * sword 系统名
     */
    String SWORD_NAME = "sword";

    /**
     * saber 系统名
     */
    String SABER_NAME = "saber";

    /**
     * 顶级父节点id
     */
    Long TOP_PARENT_ID = 0L;

    /**
     * 顶级父节点名称
     */
    String TOP_PARENT_NAME = "顶级";

    /**
     * 未封存状态值
     */
    Integer NOT_SEALED_ID = 0;

    /**
     * 默认密码
     */
    String DEFAULT_PASSWORD = "123456";

    /**
     * 默认密码参数值
     */
    String DEFAULT_PARAM_PASSWORD = "account.initPassword";

    /**
     * 默认排序字段
     */
    String SORT_FIELD = "sort";

    /**
     * 数据权限类型
     */
    Integer DATA_SCOPE_CATEGORY = 1;

    /**
     * 接口权限类型
     */
    Integer API_SCOPE_CATEGORY = 2;

    /**
     * 文件大小，MB
     */
    Long FILE_SIZE = 10L;

    /**
     * value
     */
    String VALUE = "value";

    /**
     * LABEL
     */
    String LABEL = "label";

    /**
     * 编号数据补全的字符
     */
    Character FILLED_CHAR = '0';

    /**
     * 编号数据补全的长度
     */
    Integer FILLED_LENGTH = 5;

    /**
     * 最大的机器人编号（不能超过 65535-FFFF（hex））
     * 65535 == FFFF，设置编号时：广播指令
     */
    String ROBOT_NUMBER_MAX = "65535";

    /**
     * 单字节最大值
     * 255 == FF
     */
    Integer SINGLE_BYTE_MAX = 255;

    /**
     * 两个字节最大值
     * 65535 == FFFF
     */
    Integer TWO_BYTE_MAX = 65535;

    /**
     * Api 请求超时时间（毫秒）
     */
    Integer API_TIMEOUT = 20000;

    /**
     * 米 m
     */
    String METERS = "m";

    /**
     * 时间单位 ms
     */
    String TIME_UNIT_MS = "ms";

    /**
     * 时间单位 h
     */
    String TIME_UNIT_HOUR = "H";

    /**
     * 时间单位 h/天
     */
    String TIME_UNIT_HOUR_DAY = "H/天";

    /**
     * 时间单位 day
     */
    String TIME_UNIT_DAY = "day";

    /**
     * 登录 api
     */
    String LOGIN_API = "/blade-auth/oauth/token";

    /**
     * 刷新 token api
     */
    String REFRESH_TOKEN = "/refresh_token";

    /**
     * 登出 api
     */
    String LOGOUT_API = "/blade-auth/oauth/logout";

    /**
     * 跟踪器分区绑定跟踪器的最大数量
     */
    Integer TRACKER_MAX_COUNT = 10000;

    /**
     * 系统配置id
     */
    Long SYSTEM_CONFIG_ID = 1L;

    /**
     * 使用率单位
     */
    String USE_RATE_UNIT = "%";

    /**
     * 硬盘单位
     */
    String HARD_DISK_UNIT = "GB";

    /**
     * 系统自动下发
     */
    String SYSTEM_AUTO_SEND = "系统自动下发";

    /**
     * 机器人-清扫路径-列索引（机器人导入数据 Excel 文件）
     */
    Integer ROBOT_CLEARING_PATH_COLUMN_INDEX = 3;

    /**
     * 永久日期
     */
    LocalDateTime PERMANENT_DATE = LocalDateTime.of(9999, 12, 31, 23, 59, 59);

    /**
     * license 文件后缀
     */
    String LICENSE_SUFFIX = ".lic";

    /**
     * 导入跟踪器分区标题
     */
    String BLOCK = "Block";

    /**
     * 导入跟踪器标题
     */
    String TRACK_COMMBOX = "Track/CommBox";

    /**
     * 导入跟踪器对应的支架型号标题
     */
    String TRACKER_MODEL_NAME = "支架型号";
}

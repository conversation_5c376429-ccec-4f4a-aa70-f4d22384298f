/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.common.cache;

import org.springblade.core.tool.utils.StringPool;

/**
 * 缓存名
 *
 * <AUTHOR>
 */
public interface CacheNames {

    /**
     * 返回租户格式的key
     *
     * @param tenantId      租户编号
     * @param cacheKey      缓存key
     * @param cacheKeyValue 缓存key值
     * @return tenantKey
     */
    static String tenantKey(String tenantId, String cacheKey, String cacheKeyValue) {
        return tenantId.concat(StringPool.COLON).concat(cacheKey).concat(cacheKeyValue);
    }

    /**
     * 验证码key
     */
    String CAPTCHA_KEY = "blade:auth::blade:captcha:";

    /**
     * 登录失败key
     */
    String USER_FAIL_KEY = "blade:user::blade:fail:";

    /**
     * 跟踪器分区缓存
     */
    String TRACKER_REGIONAL = "thingcom:tracker_regional:";

    /**
     * 跟踪器缓存
     */
    String TRACKER = "thingcom:tracker:";

    /**
     * 清扫路径缓存
     */
    String CLEARING_PATH = "thingcom:clearing_path:";

    /**
     * 机器人分区缓存
     */
    String ROBOT_REGIONAL = "thingcom:robot_regional:";

    /**
     * 机器人缓存
     */
    String ROBOT = "thingcom:robot:";

    /**
     * 网关缓存
     */
    String GATEWAY = "thingcom:gateway:";

    /**
     * 系统配置缓存
     */
    String SYSTEMCONFIG = "thingcom:systemconfig:";

    /**
     * 监控点
     */
    String MONITOR = "thingcom:monitor:";

    /**
     * 机器人实时数据
     */
    String ROBOT_REALDATA = "thingcom:robot:realdata:";

    /**
     * 机器人实时数据上报时间
     */
    String ROBOT_REALDATA_TIME = "thingcom:robot:realdata:time";

    /**
     * 机器人控制校验数据
     */
    String ROBOT_CONTROLDATA = "thingcom:robot:controldata:";

    /**
     * 机器人广播控制校验数据
     */
    String ROBOT_MULTICASTGROUP_CONTROLDATA = "thingcom:robot:multicastGroup:controldata:";

    /**
     * 机器人控制初始化校验数据（存储帧计数器）
     */
    String ROBOT_CONTROL_INITDATA = "thingcom:robot:control_initdata:";

    /**
     * 机器人组播-帧计数器
     */
    String ROBOT_MULTICASTGROUP_FRAME_COUNTER = "thingcom:robot:multicastGroup_frame_counter:";

    /**
     * 机器人控制-帧计数器（单播）
     */
    String ROBOT_CONTROL_FRAME_COUNTER = "thingcom:robot:control_frame_counter:";

    /**
     * 控制类型
     */
    String CONTROL_TYPE = "thingcom:control_type:";

    /**
     * 状态解析
     */
    String STATUS_ANALYSIS = "thingcom:status_analysis:";

    /**
     * 机器人升级帧计数器（所有机器人共用 1 个）
     */
    String ROBOT_UPGRADE_FRAME_COUNTER = "thingcom:robot:upgrade_frame_counter";

    /**
     * 机器人升级回复
     */
    String ROBOT_UPGRADE_RECEIVE = "thingcom:robot:upgrade_receive:";

    /**
     * 机器人运行状态
     */
    String ROBOT_RUN_STATUS = "thingcom:robot:run_status";

    /**
     * 组播升级机器人运行状态
     */
    String MULTICAST_GROUP_UPGRADE = "thingcom:robot:multicast_group_upgrade";

    /**
     * 服务器缓存
     */
    String SERVER = "thingcom:server:";

    /**
     * 气象站缓存
     */
    String WEATHER_STATION = "thingcom:weather_station:";

    /**
     * 数据上报-时间戳
     */
    String TIMESTAMP = "timestamp";

    /**
     * 数据上报-value
     */
    String VALUE = "value";

    /**
     * GPS 网关实时数据上报时间
     */
    String GPS_GATEWAY_REALDATA_TIME = "thingcom:gps:gateway:realdata:time";

    /**
     * GPS 网关时间，（硬件定时上报的数据）
     * 存储格式：{"timestamp":"1577808000000", "value":"2020-01-01 00:00:00"}
     * （硬件上报为：16进制字符串，该缓存中已做 yyyy-MM-dd HH:mm:ss 格式化处理）
     * UTC 时间
     */
    String GPS_GATEWAY_TIME = "thingcom:gps:gateway:time";

    /**
     * GPS 网关同步系统时间的结果状态（0-失败；1-成功）
     */
    String GPS_GATEWAY_SYNC_SYSTEM_TIME = "thingcom:gps:gateway:sync_system_time";

    /**
     * 风速数据（只保存最新的 5 条数据），用于判断风速报警
     * 和环境数据中的风速兼容，只会触发一次，见
     *
     * @see CacheNames.WIND_SPEED_ALARM
     */
    String WIND_SPEED_LAST5 = "thingcom:data:wind_speed";

    /**
     * 环境数据（只保存最新的 5 条数据），包括：风速、温度等，用于判断风速、温度报警
     */
    String ENVIRONMENT_LAST5 = "thingcom:data:environment";

    /**
     * 风速触发告警-下发广播指令的标识，1-正常；2-告警
     *
     * @see org.springblade.thingcom.device.enums.AlarmStatusExtEnum
     */
    String WIND_SPEED_ALARM = "thingcom:data:wind_speed:alarm";

    /**
     * 温度触发告警-下发广播指令的标识，1-正常；2-告警
     *
     * @see org.springblade.thingcom.device.enums.AlarmStatusExtEnum
     */
    String TEMPERATURE_ALARM = "thingcom:data:temperature:alarm";

    /**
     * 翻译字典
     */
    String TRANSLATION_CACHE = "thingcom:translation:";

    /**
     * 机器人消息日志时间
     */
    String ROBOT_MESSAGE_LOG_TIME = "thingcom:robot:message_log_time:";

    /**
     * 翻译缓存
     */
    String TRANSLATION_CACHE_PREFIX = "thingcom:translation:";

    /**
     * webSocket 发送翻译缓存
     */
    String WEBSOCKET_SEND_TRANSLATION_CACHE_PREFIX = "thingcom:websocket_send:translation:";

    /**
     * license 状态，0-未授权；1-已授权
     */
    String LICENSE_STATUS = "thingcom:license:status";

    /**
     * license 系统时间，毫秒级时间戳
     */
    String LICENSE_SYSTEM_TIME = "thingcom:license:system_time:";

    /**
     * 组播发送的时间
     */
    String MULTICAST_GROUP_TIME = "thingcom:multicast_group:time";

    /**
     * 分布式锁
     */
    String LOCK = "thingcom:lock:";

    /**
     * 机器人不允许启动状态，0-无效；1-有效
     */
    String ROBOT_NOT_START_STATUS = "thingcom:robot:not_start:status";
}
package org.springblade.common.utils;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.thingcom.command.entity.ControlType;
import org.springblade.thingcom.command.enums.ControlTypeCodeEnum;
import org.springblade.thingcom.command.service.ControlTypeService;
import org.springblade.thingcom.communication.enums.ControlSwitchEnum;
import org.springblade.thingcom.communication.enums.WeekEnum;
import org.springblade.thingcom.device.enums.LockOperationEnum;
import org.springblade.thingcom.device.excel.*;
import org.springblade.thingcom.device.excel.english.*;
import org.springblade.thingcom.translate.utils.TranslateUtil;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @ClassName RegexUtil
 * @Description 正则表达式工具类
 * <AUTHOR>
 * @Date 2024/12/30 20:02
 * @Version 1.0
 **/
public class RegexUtil {

    private static final ControlTypeService controlTypeService;
    private static final Integer SINGLE_BYTE_MAX = CommonConstant.SINGLE_BYTE_MAX;
    private static final Integer TWO_BYTE_MAX = CommonConstant.TWO_BYTE_MAX;

    static {
        controlTypeService = SpringUtil.getBean(ControlTypeService.class);
    }

    /**
     * 控制板参数格式校验
     *
     * @param controlPanelExcel
     * @return
     */
    public static void validateControlPanel(ControlPanelExcel controlPanelExcel) {
        if (Objects.isNull(controlPanelExcel)) {
            return;
        }
        // 反向等待时间
        if (StrUtil.isNotEmpty(controlPanelExcel.getReverseWaitTime())) {
            int intValue = Func.toInt(controlPanelExcel.getReverseWaitTime(), -1);
            Assert.isTrue(intValue >= 0 && intValue <= SINGLE_BYTE_MAX, "反向等待时间不正确");
        }
        // 行走电机运行速率
        if (StrUtil.isNotEmpty(controlPanelExcel.getWalkMotorRunRate())) {
            List<String> runRateList = controlTypeService.list(Wrappers.<ControlType>lambdaQuery()
                            .eq(ControlType::getCode, ControlTypeCodeEnum.RUN_RATE.getLabel())
                            .orderByAsc(ControlType::getSort)).stream()
                    .map(ControlType::getValue)
                    .collect(Collectors.toList());
            Assert.isTrue(runRateList.contains(controlPanelExcel.getWalkMotorRunRate()), "请选择正确的运行速率");
        }
        // 行走电机电流上限停机值
        if (StrUtil.isNotEmpty(controlPanelExcel.getWalkMotorElectricityMaxAstrictStop())) {
            int intValue = Func.toInt(controlPanelExcel.getWalkMotorElectricityMaxAstrictStop(), -1);
            Assert.isTrue(intValue >= 0 && intValue <= TWO_BYTE_MAX, "行走电机电流上限停机值不正确");
        }
        // 行走电机电流上限预警值
        if (StrUtil.isNotEmpty(controlPanelExcel.getWalkMotorElectricityMaxAstrictWarn())) {
            int intValue = Func.toInt(controlPanelExcel.getWalkMotorElectricityMaxAstrictWarn(), -1);
            Assert.isTrue(intValue >= 0 && intValue <= TWO_BYTE_MAX, "行走电机电流上限预警值不正确");
        }
        // 总里程
        if (StrUtil.isNotEmpty(controlPanelExcel.getWalkMotorOverstepRunTime())) {
            int intValue = Func.toInt(controlPanelExcel.getWalkMotorOverstepRunTime(), -1);
            Assert.isTrue(intValue >= 0 && intValue <= TWO_BYTE_MAX, "总里程不正确");
        }
    }

    /**
     * 控制板参数格式校验
     *
     * @param controlPanelExcel
     * @return
     */
    public static void validateControlPanel(ControlPanelEnglishExcel controlPanelExcel) {
        if (Objects.isNull(controlPanelExcel)) {
            return;
        }
        // 反向等待时间
        if (StrUtil.isNotEmpty(controlPanelExcel.getReverseWaitTime())) {
            int intValue = Func.toInt(controlPanelExcel.getReverseWaitTime(), -1);
            Assert.isTrue(intValue >= 0 && intValue <= SINGLE_BYTE_MAX, "反向等待时间不正确");
        }
        // 行走电机运行速率
        if (StrUtil.isNotEmpty(controlPanelExcel.getWalkMotorRunRate())) {
            List<String> runRateList = controlTypeService.list(Wrappers.<ControlType>lambdaQuery()
                            .eq(ControlType::getCode, ControlTypeCodeEnum.RUN_RATE.getLabel())
                            .orderByAsc(ControlType::getSort)).stream()
                    .map(m -> TranslateUtil.chineseToEnglish(m.getValue()))
                    .collect(Collectors.toList());
            Assert.isTrue(runRateList.contains(controlPanelExcel.getWalkMotorRunRate()), "请选择正确的运行速率");
        }
        // 行走电机电流上限停机值
        if (StrUtil.isNotEmpty(controlPanelExcel.getWalkMotorElectricityMaxAstrictStop())) {
            int intValue = Func.toInt(controlPanelExcel.getWalkMotorElectricityMaxAstrictStop(), -1);
            Assert.isTrue(intValue >= 0 && intValue <= TWO_BYTE_MAX, "行走电机电流上限停机值不正确");
        }
        // 行走电机电流上限预警值
        if (StrUtil.isNotEmpty(controlPanelExcel.getWalkMotorElectricityMaxAstrictWarn())) {
            int intValue = Func.toInt(controlPanelExcel.getWalkMotorElectricityMaxAstrictWarn(), -1);
            Assert.isTrue(intValue >= 0 && intValue <= TWO_BYTE_MAX, "行走电机电流上限预警值不正确");
        }
        // 总里程
        if (StrUtil.isNotEmpty(controlPanelExcel.getWalkMotorOverstepRunTime())) {
            int intValue = Func.toInt(controlPanelExcel.getWalkMotorOverstepRunTime(), -1);
            Assert.isTrue(intValue >= 0 && intValue <= TWO_BYTE_MAX, "总里程不正确");
        }
    }

    /**
     * 时间格式校验
     *
     * @param timerExcel
     * @return
     */
    public static void validateTimer(TimerExcel timerExcel) {
        if (Objects.isNull(timerExcel)) {
            return;
        }
        // 星期
        String week = timerExcel.getWeek();
        WeekEnum weekEnum = Arrays.stream(WeekEnum.values()).filter(w -> w.getZh().equals(week)).findFirst().orElse(null);
        Assert.isTrue(Objects.nonNull(weekEnum), "请选择正确的星期");
        // 时间
        String time = timerExcel.getTime();
        if (StrUtil.isNotEmpty(time)) {
            Assert.isTrue(time.contains(":"), "时间格式不正确");
            String[] hoursAndMinutes = time.split(":");
            String hoursStr = hoursAndMinutes[0];
            int hours = Func.toInt(hoursStr, -1);
            Assert.isTrue(hours >= 0 && hours <= 23, "小时数不正确");
            String minutesStr = hoursAndMinutes[1];
            int minutes = Func.toInt(minutesStr, -1);
            Assert.isTrue(minutes >= 0 && minutes <= 59, "分钟数不正确");
        }
        // 运行次数
        if (StrUtil.isNotEmpty(timerExcel.getRunCount())) {
            int intValue = Func.toInt(timerExcel.getRunCount(), 0);
            Assert.isTrue(intValue >= 1 && intValue <= 99, "运行次数必须在1~99之间");
        }
    }

    /**
     * 时间格式校验
     *
     * @param timerExcel
     * @return
     */
    public static void validateTimer(TimerEnglishExcel timerExcel) {
        if (Objects.isNull(timerExcel)) {
            return;
        }
        // 星期
        String week = timerExcel.getWeek();
        WeekEnum weekEnum = Arrays.stream(WeekEnum.values()).filter(w -> TranslateUtil.chineseToEnglish(w.getZh()).equals(week))
                .findFirst().orElse(null);
        Assert.isTrue(Objects.nonNull(weekEnum), "请选择正确的星期");
        // 时间
        String time = timerExcel.getTime();
        if (StrUtil.isNotEmpty(time)) {
            Assert.isTrue(time.contains(":"), "时间格式不正确");
            String[] hoursAndMinutes = time.split(":");
            String hoursStr = hoursAndMinutes[0];
            int hours = Func.toInt(hoursStr, -1);
            Assert.isTrue(hours >= 0 && hours <= 23, "小时数不正确");
            String minutesStr = hoursAndMinutes[1];
            int minutes = Func.toInt(minutesStr, -1);
            Assert.isTrue(minutes >= 0 && minutes <= 59, "分钟数不正确");
        }
        // 运行次数
        if (StrUtil.isNotEmpty(timerExcel.getRunCount())) {
            int intValue = Func.toInt(timerExcel.getRunCount(), 0);
            Assert.isTrue(intValue >= 1 && intValue <= 99, "运行次数必须在1~99之间");
        }
    }

    /**
     * 电池参数数据校验
     *
     * @param batteryParameterExcel
     * @return
     */
    public static void validateBattery(BatteryParameterExcel batteryParameterExcel) {
        if (Objects.isNull(batteryParameterExcel)) {
            return;
        }
        // 高温阈值
        if (StrUtil.isNotEmpty(batteryParameterExcel.getHighTemperatureThreshold())) {
            int intValue = Func.toInt(batteryParameterExcel.getHighTemperatureThreshold(), -1);
            Assert.isTrue(intValue >= 0 && intValue <= SINGLE_BYTE_MAX, "高温阈值不正确");
        }
        // 低温阈值
        if (StrUtil.isNotEmpty(batteryParameterExcel.getLowTemperatureThreshold())) {
            int intValue = Func.toInt(batteryParameterExcel.getLowTemperatureThreshold(), -500);
            Assert.isTrue(intValue >= -128 && intValue <= 127, "低温阈值不正确");
        }
        // 保护温度
        if (StrUtil.isNotEmpty(batteryParameterExcel.getProtectTemperature())) {
            int intValue = Func.toInt(batteryParameterExcel.getProtectTemperature(), -1);
            Assert.isTrue(intValue >= 0 && intValue <= SINGLE_BYTE_MAX, "保护温度不正确");
        }
        // 恢复温度
        if (StrUtil.isNotEmpty(batteryParameterExcel.getRecoverTemperature())) {
            int intValue = Func.toInt(batteryParameterExcel.getRecoverTemperature(), -1);
            Assert.isTrue(intValue >= 0 && intValue <= SINGLE_BYTE_MAX, "恢复温度不正确");
        }
        // 保护电压
        if (StrUtil.isNotEmpty(batteryParameterExcel.getProtectVoltage())) {
            int intValue = Func.toInt(batteryParameterExcel.getProtectVoltage(), -1);
            Assert.isTrue(intValue >= 0 && intValue <= SINGLE_BYTE_MAX, "保护电压不正确");
        }
        // 恢复电压
        if (StrUtil.isNotEmpty(batteryParameterExcel.getRecoverVoltage())) {
            int intValue = Func.toInt(batteryParameterExcel.getRecoverVoltage(), -1);
            Assert.isTrue(intValue >= 0 && intValue <= SINGLE_BYTE_MAX, "恢复电压不正确");
        }
        // 保护电量
        if (StrUtil.isNotEmpty(batteryParameterExcel.getProtectCapacity())) {
            int intValue = Func.toInt(batteryParameterExcel.getProtectCapacity(), -1);
            Assert.isTrue(intValue >= 0 && intValue <= 100, "保护电量不正确");
        }
        // 恢复电量
        if (StrUtil.isNotEmpty(batteryParameterExcel.getRecoverCapacity())) {
            int intValue = Func.toInt(batteryParameterExcel.getRecoverCapacity(), -1);
            Assert.isTrue(intValue >= 0 && intValue <= 100, "恢复电量不正确");
        }
        // 保护电流
        if (StrUtil.isNotEmpty(batteryParameterExcel.getProtectElectricity())) {
            int intValue = Func.toInt(batteryParameterExcel.getProtectElectricity(), -1);
            Assert.isTrue(intValue >= 0 && intValue <= TWO_BYTE_MAX, "保护电流不正确");
        }
        // 限制电量
        if (StrUtil.isNotEmpty(batteryParameterExcel.getAstrictCapacity())) {
            int intValue = Func.toInt(batteryParameterExcel.getAstrictCapacity(), -1);
            Assert.isTrue(intValue >= 0 && intValue <= 100, "限制电量不正确");
        }
    }

    /**
     * 电池参数数据校验
     *
     * @param batteryParameterExcel
     * @return
     */
    public static void validateBattery(BatteryParameterEnglishExcel batteryParameterExcel) {
        if (Objects.isNull(batteryParameterExcel)) {
            return;
        }
        // 高温阈值
        if (StrUtil.isNotEmpty(batteryParameterExcel.getHighTemperatureThreshold())) {
            int intValue = Func.toInt(batteryParameterExcel.getHighTemperatureThreshold(), -1);
            Assert.isTrue(intValue >= 0 && intValue <= SINGLE_BYTE_MAX, "高温阈值不正确");
        }
        // 低温阈值
        if (StrUtil.isNotEmpty(batteryParameterExcel.getLowTemperatureThreshold())) {
            int intValue = Func.toInt(batteryParameterExcel.getLowTemperatureThreshold(), -500);
            Assert.isTrue(intValue >= -128 && intValue <= 127, "低温阈值不正确");
        }
        // 保护温度
        if (StrUtil.isNotEmpty(batteryParameterExcel.getProtectTemperature())) {
            int intValue = Func.toInt(batteryParameterExcel.getProtectTemperature(), -1);
            Assert.isTrue(intValue >= 0 && intValue <= SINGLE_BYTE_MAX, "保护温度不正确");
        }
        // 恢复温度
        if (StrUtil.isNotEmpty(batteryParameterExcel.getRecoverTemperature())) {
            int intValue = Func.toInt(batteryParameterExcel.getRecoverTemperature(), -1);
            Assert.isTrue(intValue >= 0 && intValue <= SINGLE_BYTE_MAX, "恢复温度不正确");
        }
        // 保护电压
        if (StrUtil.isNotEmpty(batteryParameterExcel.getProtectVoltage())) {
            int intValue = Func.toInt(batteryParameterExcel.getProtectVoltage(), -1);
            Assert.isTrue(intValue >= 0 && intValue <= SINGLE_BYTE_MAX, "保护电压不正确");
        }
        // 恢复电压
        if (StrUtil.isNotEmpty(batteryParameterExcel.getRecoverVoltage())) {
            int intValue = Func.toInt(batteryParameterExcel.getRecoverVoltage(), -1);
            Assert.isTrue(intValue >= 0 && intValue <= SINGLE_BYTE_MAX, "恢复电压不正确");
        }
        // 保护电量
        if (StrUtil.isNotEmpty(batteryParameterExcel.getProtectCapacity())) {
            int intValue = Func.toInt(batteryParameterExcel.getProtectCapacity(), -1);
            Assert.isTrue(intValue >= 0 && intValue <= 100, "保护电量不正确");
        }
        // 恢复电量
        if (StrUtil.isNotEmpty(batteryParameterExcel.getRecoverCapacity())) {
            int intValue = Func.toInt(batteryParameterExcel.getRecoverCapacity(), -1);
            Assert.isTrue(intValue >= 0 && intValue <= 100, "恢复电量不正确");
        }
        // 保护电流
        if (StrUtil.isNotEmpty(batteryParameterExcel.getProtectElectricity())) {
            int intValue = Func.toInt(batteryParameterExcel.getProtectElectricity(), -1);
            Assert.isTrue(intValue >= 0 && intValue <= TWO_BYTE_MAX, "保护电流不正确");
        }
        // 限制电量
        if (StrUtil.isNotEmpty(batteryParameterExcel.getAstrictCapacity())) {
            int intValue = Func.toInt(batteryParameterExcel.getAstrictCapacity(), -1);
            Assert.isTrue(intValue >= 0 && intValue <= 100, "限制电量不正确");
        }
    }

    /**
     * LoRa参数格式校验
     *
     * @param loRaExcel
     * @return
     */
    public static void validateLoRa(LoRaExcel loRaExcel) {
        if (Objects.isNull(loRaExcel)) {
            return;
        }
        // 发射功率
        if (StrUtil.isNotEmpty(loRaExcel.getSendPowerName())) {
            List<String> runRateList = controlTypeService.list(Wrappers.<ControlType>lambdaQuery()
                            .eq(ControlType::getCode, ControlTypeCodeEnum.SEND_POWER.getLabel())
                            .orderByAsc(ControlType::getSort)).stream()
                    .map(ControlType::getValue)
                    .collect(Collectors.toList());
            Assert.isTrue(runRateList.contains(loRaExcel.getSendPowerName()), "请选择正确的发射功率");
        }
        // 区域
        //if (StrUtil.isNotEmpty(loRaExcel.getRegionName())) {
        //    List<String> runRateList = controlTypeService.list(Wrappers.<ControlType>lambdaQuery()
        //                    .eq(ControlType::getCode, ControlTypeCodeEnum.REGION.getLabel())
        //                    .orderByAsc(ControlType::getSort)).stream()
        //            .map(ControlType::getValue)
        //            .collect(Collectors.toList());
        //    Assert.isTrue(runRateList.contains(loRaExcel.getRegionName()), "请选择正确的区域");
        //}
        // 频段
        //if (StrUtil.isNotEmpty(loRaExcel.getFrequencyBandName())) {
        //    List<String> runRateList = controlTypeService.list(Wrappers.<ControlType>lambdaQuery()
        //                    .eq(ControlType::getCode, ControlTypeCodeEnum.FREQUENCY_BAND.getLabel())
        //                    .orderByAsc(ControlType::getSort)).stream()
        //            .map(ControlType::getValue)
        //            .collect(Collectors.toList());
        //    Assert.isTrue(runRateList.contains(loRaExcel.getFrequencyBandName()), "请选择正确的频段");
        //}
    }

    /**
     * LoRa参数格式校验
     *
     * @param loRaExcel
     * @return
     */
    public static void validateLoRa(LoRaEnglishExcel loRaExcel) {
        if (Objects.isNull(loRaExcel)) {
            return;
        }
        // 发射功率
        if (StrUtil.isNotEmpty(loRaExcel.getSendPowerName())) {
            List<String> runRateList = controlTypeService.list(Wrappers.<ControlType>lambdaQuery()
                            .eq(ControlType::getCode, ControlTypeCodeEnum.SEND_POWER.getLabel())
                            .orderByAsc(ControlType::getSort)).stream()
                    .map(ControlType::getValue)
                    .collect(Collectors.toList());
            Assert.isTrue(runRateList.contains(loRaExcel.getSendPowerName()), "请选择正确的发射功率");
        }
        // 区域
        //if (StrUtil.isNotEmpty(loRaExcel.getRegionName())) {
        //    List<String> runRateList = controlTypeService.list(Wrappers.<ControlType>lambdaQuery()
        //                    .eq(ControlType::getCode, ControlTypeCodeEnum.REGION.getLabel())
        //                    .orderByAsc(ControlType::getSort)).stream()
        //            .map(ControlType::getValue)
        //            .collect(Collectors.toList());
        //    Assert.isTrue(runRateList.contains(loRaExcel.getRegionName()), "请选择正确的区域");
        //}
        // 频段
        //if (StrUtil.isNotEmpty(loRaExcel.getFrequencyBandName())) {
        //    List<String> runRateList = controlTypeService.list(Wrappers.<ControlType>lambdaQuery()
        //                    .eq(ControlType::getCode, ControlTypeCodeEnum.FREQUENCY_BAND.getLabel())
        //                    .orderByAsc(ControlType::getSort)).stream()
        //            .map(ControlType::getValue)
        //            .collect(Collectors.toList());
        //    Assert.isTrue(runRateList.contains(loRaExcel.getFrequencyBandName()), "请选择正确的频段");
        //}
    }

    /**
     * 停机位参数格式校验
     *
     * @param stopBitExcel
     * @return
     */
    public static void validateStopBit(StopBitExcel stopBitExcel) {
        if (Objects.isNull(stopBitExcel)) {
            return;
        }
        // 停机位
        if (StrUtil.isNotEmpty(stopBitExcel.getValue())) {
            List<String> list = controlTypeService.list(Wrappers.<ControlType>lambdaQuery()
                            .eq(ControlType::getCode, ControlTypeCodeEnum.STOP_BIT.getLabel())
                            .orderByAsc(ControlType::getSort)).stream()
                    .map(ControlType::getValue)
                    .collect(Collectors.toList());
            Assert.isTrue(list.contains(stopBitExcel.getValue()), "请选择正确的停机位");
        }
    }

    /**
     * 停机位参数格式校验
     *
     * @param stopBitExcel
     * @return
     */
    public static void validateStopBit(StopBitEnglishExcel stopBitExcel) {
        if (Objects.isNull(stopBitExcel)) {
            return;
        }
        // 停机位
        if (StrUtil.isNotEmpty(stopBitExcel.getValue())) {
            List<String> list = controlTypeService.list(Wrappers.<ControlType>lambdaQuery()
                            .eq(ControlType::getCode, ControlTypeCodeEnum.STOP_BIT.getLabel())
                            .orderByAsc(ControlType::getSort)).stream()
                    .map(m -> TranslateUtil.chineseToEnglish(m.getValue()))
                    .collect(Collectors.toList());
            Assert.isTrue(list.contains(stopBitExcel.getValue()), "请选择正确的停机位");
        }
    }

    /**
     * 操作参数格式校验
     *
     * @param operationSetParamExcel
     * @return
     */
    public static void validateOperationSetParam(OperationSetParamExcel operationSetParamExcel) {
        if (Objects.isNull(operationSetParamExcel)) {
            return;
        }
        // 锁定
        if (StrUtil.isNotEmpty(operationSetParamExcel.getLock())) {
            List<String> list = Arrays.stream(LockOperationEnum.values())
                    .map(LockOperationEnum::getZh)
                    .collect(Collectors.toList());
            Assert.isTrue(list.contains(operationSetParamExcel.getLock()), "请选择正确的锁定设置");
        }
        // 白天防误扫
        if (StrUtil.isNotEmpty(operationSetParamExcel.getDaytimePreventClean())) {
            List<String> list = Arrays.stream(ControlSwitchEnum.values())
                    .map(ControlSwitchEnum::getZh)
                    .collect(Collectors.toList());
            Assert.isTrue(list.contains(operationSetParamExcel.getDaytimePreventClean()), "请选择正确的白天防误扫设置");
        }
    }

    /**
     * 操作参数格式校验
     *
     * @param operationSetParamExcel
     * @return
     */
    public static void validateOperationSetParam(OperationSetParamEnglishExcel operationSetParamExcel) {
        if (Objects.isNull(operationSetParamExcel)) {
            return;
        }
        // 锁定
        if (StrUtil.isNotEmpty(operationSetParamExcel.getLock())) {
            List<String> list = Arrays.stream(LockOperationEnum.values())
                    .map(m -> TranslateUtil.chineseToEnglish(m.getZh()))
                    .collect(Collectors.toList());
            Assert.isTrue(list.contains(operationSetParamExcel.getLock()), "请选择正确的锁定设置");
        }
        // 白天防误扫
        if (StrUtil.isNotEmpty(operationSetParamExcel.getDaytimePreventClean())) {
            List<String> list = Arrays.stream(ControlSwitchEnum.values())
                    .map(m -> TranslateUtil.chineseToEnglish(m.getZh()))
                    .collect(Collectors.toList());
            Assert.isTrue(list.contains(operationSetParamExcel.getDaytimePreventClean()), "请选择正确的白天防误扫设置");
        }
    }

    /**
     * 时间格式校验
     *
     * @param time
     * @return boolean，true表示校验通过，false表示校验不通过
     */
    public static boolean validateTime(String time) {
        if (StrUtil.isBlank(time)) {
            return true;
        }
        if (!time.contains(":")) {
            return false;
        }
        String[] hoursAndMinutes = time.split(":");
        String hoursStr = hoursAndMinutes[0];
        int hours = Func.toInt(hoursStr, -1);
        if (hours < 0 || hours > 23) {
            return false;
        }
        String minutesStr = hoursAndMinutes[1];
        int minutes = Func.toInt(minutesStr, -1);
        if (minutes < 0 || minutes > 59) {
            return false;
        }
        return true;
    }

    /**
     * 主板温度格式校验
     *
     * @param mainBoardTemperatureExcel
     * @return
     */
    public static void validateMainBoardTemperature(MainBoardTemperatureExcel mainBoardTemperatureExcel) {
        // 保护温度
        if (StrUtil.isNotEmpty(mainBoardTemperatureExcel.getProtectionTemperature())) {
            int intValue = Func.toInt(mainBoardTemperatureExcel.getProtectionTemperature(), -1);
            Assert.isTrue(intValue >= -327 && intValue <= 327, "保护温度不正确");
        }
        // 恢复温度
        if (StrUtil.isNotEmpty(mainBoardTemperatureExcel.getRecoveryTemperature())) {
            int intValue = Func.toInt(mainBoardTemperatureExcel.getRecoveryTemperature(), -1);
            Assert.isTrue(intValue >= -327 && intValue <= 327, "恢复温度不正确");
        }
    }

    /**
     * 主板温度格式校验
     *
     * @param mainBoardTemperatureExcel
     * @return
     */
    public static void validateMainBoardTemperature(MainBoardTemperatureEnglishExcel mainBoardTemperatureExcel) {
        // 保护温度
        if (StrUtil.isNotEmpty(mainBoardTemperatureExcel.getProtectionTemperature())) {
            int intValue = Func.toInt(mainBoardTemperatureExcel.getProtectionTemperature(), -1);
            Assert.isTrue(intValue >= -327 && intValue <= 327, "保护温度不正确");
        }
        // 恢复温度
        if (StrUtil.isNotEmpty(mainBoardTemperatureExcel.getRecoveryTemperature())) {
            int intValue = Func.toInt(mainBoardTemperatureExcel.getRecoveryTemperature(), -1);
            Assert.isTrue(intValue >= -327 && intValue <= 327, "恢复温度不正确");
        }
    }

}

package org.springblade.common.utils;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;

/**
 * @ClassName PasswordUtil
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/1/9 16:21
 * @Version 1.0
 **/
public class PasswordUtil {

    /**
     * 加密
     *
     * @param str
     * @return
     */
    public static String base64Encode(String str) {
        return StrUtil.isBlank(str) ? str : Base64.encode(StrUtil.reverse(str));
    }

    /**
     * 解密
     *
     * @param str
     * @return
     */
    public static String base64Decode(String str) {
        return StrUtil.isBlank(str) ? str : StrUtil.reverse(Base64.decodeStr(str));
    }

    public static void main(String[] args) {
        System.out.println(base64Decode("MjEyMQ=="));
    }

}
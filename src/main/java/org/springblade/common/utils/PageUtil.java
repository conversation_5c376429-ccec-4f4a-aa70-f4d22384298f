package org.springblade.common.utils;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName PageUtil
 * @Description 分页工具类
 * <AUTHOR>
 * @Date 2024/8/20 14:51
 * @Version 1.0
 **/
public class PageUtil {

    /**
     * 分页对象转换
     *
     * @param iPage 分页对象
     * @param clazz 类型
     * @return IPage
     **/
    public static <T> IPage<T> toPage(IPage<Map<String, Object>> iPage, Class<T> clazz) {
        if (iPage == null) {
            return new Page<T>();
        }
        // 类型转换
        List<Map<String, Object>> records = iPage.getRecords();
        List<T> list = records.stream().map(map -> BeanUtil.toBean(map, clazz)).collect(Collectors.toList());
        // 构建新的IPage
        IPage<T> iPageConvert = new Page<>();
        iPageConvert.setRecords(list);
        iPageConvert.setCurrent(iPage.getCurrent());
        iPageConvert.setSize(iPage.getSize());
        iPageConvert.setTotal(iPage.getTotal());
        iPageConvert.setPages(iPage.getPages());
        return iPageConvert;
    }
}

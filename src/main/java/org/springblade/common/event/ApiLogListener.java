/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */

package org.springblade.common.event;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.log.LogAbstractExtUtil;
import org.springblade.core.launch.props.BladeProperties;
import org.springblade.core.launch.server.ServerInfo;
import org.springblade.core.log.constant.EventConstant;
import org.springblade.core.log.event.ApiLogEvent;
import org.springblade.core.log.model.LogApi;
import org.springblade.core.log.utils.LogAbstractUtil;
import org.springblade.modules.system.service.ILogService;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.scheduling.annotation.Async;

import java.util.Map;


/**
 * 异步监听日志事件
 *
 * <AUTHOR>
 */
@Slf4j
@AllArgsConstructor
public class ApiLogListener {

    private final ILogService logService;
    private final ServerInfo serverInfo;
    private final BladeProperties bladeProperties;

    @Async
    @Order
    @EventListener(ApiLogEvent.class)
    public void saveApiLog(ApiLogEvent event) {
        Map<String, Object> source = (Map<String, Object>) event.getSource();
        LogApi logApi = (LogApi) source.get(EventConstant.EVENT_LOG);
        // 在退出接口单独处理，这里的退出拿不到当前退出的用户信息（因为这里为异步操作，并且退出接口清除了 token）
        if (CommonConstant.LOGOUT_API.equals(logApi.getRequestUri())) {
            return;
        }
        LogAbstractUtil.addOtherInfoToLog(logApi, bladeProperties, serverInfo);
        // 账号转换为 id
        LogAbstractExtUtil.convertCreateBy(logApi);
        // 登录、退出接口没有对应的用户信息，不保存日志（比如账号密码错误）
        if (CommonConstant.LOGIN_API.equals(logApi.getRequestUri())) {
            if (StrUtil.isBlank(logApi.getCreateBy())) {
                return;
            }
        }
        logService.saveApiLog(logApi);
    }

}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.thingcom.warning.mapper.GatewayAlarmMapper">

    <resultMap type="org.springblade.thingcom.warning.entity.GatewayAlarm" id="GatewayAlarmMap">
        <result column="id" property="id"/>
        <result column="gateway_id" property="gatewayId"/>
        <result column="alarm_type" property="alarmType"/>
        <result column="alarm_content" property="alarmContent"/>
        <result column="solution" property="solution"/>
        <result column="time" property="time"/>
        <result column="update_time" property="updateTime"/>
        <result column="read_label" property="readLabel"/>
    </resultMap>

    <select id="getAlarmType" resultType="org.springblade.thingcom.warning.vo.GatewayAlarmVO">
        SELECT      ga.alarm_type AS alarmType
                    , COUNT(ga.alarm_type) AS alarmTypeCount
        FROM        tb_gateway_alarm ga
        LEFT JOIN   tb_gateway g
        ON          ga.gateway_id = g.id
        WHERE       ga.is_deleted = 0
        <if test="param.alarmType != null">
            AND ga.alarm_type = #{param.alarmType}
        </if>
        <if test="param.gatewayNumber != null and param.gatewayNumber != ''">
            AND g.gateway_number = #{param.gatewayNumber}
        </if>
        <if test="param.beginDate != null and param.endDate != null">
            AND ga.time BETWEEN #{param.beginDate} AND #{param.endDate}
        </if>
        GROUP BY ga.alarm_type
    </select>
</mapper>

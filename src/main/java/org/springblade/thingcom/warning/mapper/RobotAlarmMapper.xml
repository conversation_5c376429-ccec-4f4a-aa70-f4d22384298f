<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.thingcom.warning.mapper.RobotAlarmMapper">
    <resultMap type="org.springblade.thingcom.warning.entity.RobotAlarm" id="RobotAlarmMap">
        <result column="id" property="id"/>
        <result column="robot_id" property="robotId"/>
        <result column="status_analysis_id" property="statusAnalysisId"/>
        <result column="alarm_type" property="alarmType"/>
        <result column="alarm_content" property="alarmContent"/>
        <result column="solution" property="solution"/>
        <result column="time" property="time"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_time" property="createTime"/>
        <result column="read_label" property="readLabel"/>
    </resultMap>

    <select id="page" resultType="org.springblade.thingcom.warning.vo.RobotAlarmVO">
        SELECT       t1.*
                     ,t2.name AS robotName
                     ,t2.eui AS robotEui
                     ,t2.clearing_path_id AS robotClearingPathId
                     ,t3.regional_number AS robotRegionalNumber
        FROM         tb_robot_alarm AS t1
        INNER JOIN   tb_robot AS t2
        ON           t1.robot_id = t2.id
        AND          t2.is_deleted = 0
        INNER JOIN   tb_robot_regional AS t3
        ON           t2.robot_regional_id = t3.id
        AND          t3.is_deleted = 0
        WHERE        t1.is_deleted = 0
        <if test="param.id != null">
            AND t1.id = #{param.id}
        </if>
        <if test="param.alarmType != null">
            AND t1.alarm_type = #{param.alarmType}
        </if>
        <if test="param.alarmContent != null and param.alarmContent != ''">
            AND t1.alarm_content LIKE CONCAT('%', #{param.alarmContent}, '%')
        </if>
        <if test="param.robotRegionalId != null">
            AND t3.id = #{param.robotRegionalId}
        </if>
        <if test="param.robotId != null">
            AND t1.robot_id = #{param.robotId}
        </if>
        <if test="param.beginDate != null and param.endDate != null">
            AND t1.time BETWEEN #{param.beginDate} AND #{param.endDate}
        </if>
        ORDER BY t1.time DESC
    </select>

    <select id="getAlarmType" resultType="org.springblade.thingcom.warning.vo.RobotAlarmVO">
        SELECT       t1.status_analysis_id
                     ,t1.alarm_content
                     ,COUNT(*) AS alarmCount
        FROM         tb_robot_alarm AS t1
        INNER JOIN   tb_robot AS t2
        ON           t1.robot_id = t2.id
        AND          t2.is_deleted = 0
        INNER JOIN   tb_robot_regional AS t3
        ON           t2.robot_regional_id = t3.id
        AND          t3.is_deleted = 0
        WHERE        t1.is_deleted = 0
        <if test="param.alarmType != null">
            AND t1.alarm_type = #{param.alarmType}
        </if>
        <if test="param.robotRegionalId != null">
            AND t3.id = #{param.robotRegionalId}
        </if>
        <if test="param.robotId != null">
            AND t1.robot_id = #{param.robotId}
        </if>
        <if test="param.beginDate != null and param.endDate != null">
            AND t1.time BETWEEN #{param.beginDate} AND #{param.endDate}
        </if>
        GROUP BY t1.status_analysis_id
    </select>

    <select id="getRegional" resultType="org.springblade.thingcom.warning.vo.RobotAlarmVO">
        SELECT      t3.regional_number AS robotRegionalNumber
                    ,COUNT( t3.regional_number ) AS regionalCount
        FROM        tb_robot_alarm AS t1
        INNER JOIN  tb_robot AS t2
        ON          t1.robot_id = t2.id
        AND         t2.is_deleted = 0
        INNER JOIN  tb_robot_regional AS t3
        ON          t2.robot_regional_id = t3.id
        AND         t3.is_deleted = 0
        WHERE       t1.is_deleted = 0
        <if test="statusAnalysisId != null">
            AND t1.status_analysis_id = #{statusAnalysisId}
        </if>
        GROUP BY t3.regional_number
    </select>

    <select id="getRegionalList" resultType="org.springblade.thingcom.warning.vo.RobotAlarmVO">
        SELECT      t2.name AS robotName ,
                    t1.alarm_content AS alarmContent,
                    t1.solution AS solution,
                    t1.time AS time,
                    t3.regional_number AS robotRegionalNumber
        FROM        tb_robot_alarm AS t1
        INNER JOIN  tb_robot AS t2
        ON          t1.robot_id = t2.id
        AND         t2.is_deleted = 0
        INNER JOIN  tb_robot_regional AS t3
        ON          t2.robot_regional_id = t3.id
        AND         t3.is_deleted = 0
        WHERE       t1.is_deleted = 0
        <if test="statusAnalysisId != null">
            AND t1.status_analysis_id = #{statusAnalysisId}
        </if>
        <if test="robotRegionalNumber != null and robotRegionalNumber != ''">
            AND t3.regional_number = #{robotRegionalNumber}
        </if>
    </select>

    <delete id="deleteById">
        DELETE FROM tb_robot_alarm WHERE id = #{id}
    </delete>

    <delete id="deleteByIds">
        DELETE FROM tb_robot_alarm WHERE id
        IN
        <foreach collection ="idList" item="item" index= "index" open = "(" close = ")" separator =",">
            #{item}
        </foreach >
    </delete>

</mapper>
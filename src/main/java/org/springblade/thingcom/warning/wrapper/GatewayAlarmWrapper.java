package org.springblade.thingcom.warning.wrapper;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.thingcom.device.entity.Gateway;
import org.springblade.thingcom.device.service.GatewayService;
import org.springblade.thingcom.translate.utils.TranslateUtil;
import org.springblade.thingcom.warning.dto.GatewayAlarmDTO;
import org.springblade.thingcom.warning.entity.GatewayAlarm;
import org.springblade.thingcom.warning.enums.CommunicationStatusEnum;
import org.springblade.thingcom.warning.excel.GatewayAlarmExcel;
import org.springblade.thingcom.warning.vo.GatewayAlarmVO;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 网关实时告警包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-01-23 17:07:22
 */
public class GatewayAlarmWrapper extends BaseEntityWrapper<GatewayAlarm, GatewayAlarmVO> {

    private static final GatewayService GATEWAY_SERVICE;

    static {
        GATEWAY_SERVICE = SpringUtil.getBean(GatewayService.class);
    }

    public static GatewayAlarmWrapper build() {
        return new GatewayAlarmWrapper();
    }

    @Override
    public GatewayAlarmVO entityVO(GatewayAlarm entity) {
        if (null == entity) {
            return null;
        }
        GatewayAlarmVO vo = BeanUtil.copyProperties(entity, GatewayAlarmVO.class);
        // 告警类型转换
        if (Objects.nonNull(vo.getAlarmType())) {
            CommunicationStatusEnum communicationStatusEnum = CommunicationStatusEnum.getByValue(vo.getAlarmType());
            if (Objects.nonNull(communicationStatusEnum)) {
                vo.setAlarmTypeVo(communicationStatusEnum.getLabel());
            }
        }
        if (Objects.nonNull(vo.getGatewayId())) {
            Gateway gateway = GATEWAY_SERVICE.getById(vo.getGatewayId());
            if (Objects.nonNull(gateway)) {
                vo.setGatewayNumber(gateway.getGatewayNumber());
            }
        }
        return vo;
    }

    /**
     * 设置sql查询条件
     *
     * @param dto
     * @return
     */
    public LambdaQueryWrapper<GatewayAlarm> setQueryParam(GatewayAlarmDTO dto) {
        LambdaQueryWrapper<GatewayAlarm> lqw = new LambdaQueryWrapper<>();
        // 告警ID
        lqw.eq(ObjectUtil.isNotEmpty(dto.getId()), GatewayAlarm::getId, dto.getId());
        // 告警类型
        lqw.eq(ObjectUtil.isNotEmpty(dto.getAlarmType()), GatewayAlarm::getAlarmType, dto.getAlarmType());
        // 告警内容
        lqw.like(Func.isNotBlank(dto.getAlarmContent()), GatewayAlarm::getAlarmContent, dto.getAlarmContent());
        // 时间查询
        if (Objects.nonNull(dto.getBeginDate()) && Objects.nonNull(dto.getEndDate())) {
            lqw.between(GatewayAlarm::getUpdateTime, dto.getBeginDate(), dto.getEndDate());
        }
        lqw.eq(ObjectUtil.isNotEmpty(dto.getGatewayNumber()), GatewayAlarm::getGatewayId, dto.getGatewayNumber());
        // 时间倒序
        lqw.orderByDesc(GatewayAlarm::getUpdateTime);
        return lqw;
    }

    /**
     * 导出列表
     *
     * @param entityList
     * @return
     */
    public List<GatewayAlarmExcel> listToExcel(List<GatewayAlarm> entityList, Boolean translate) {
        return entityList.stream()
                .map(entity -> {
                    GatewayAlarmExcel excel = BeanUtil.copyProperties(entity, GatewayAlarmExcel.class);
                    // 获取网关编号
                    if (Objects.nonNull(entity.getGatewayId())) {
                        Gateway gateway = GATEWAY_SERVICE.getById(entity.getGatewayId());
                        if (Objects.nonNull(gateway)) {
                            excel.setGatewayNumber(gateway.getGatewayNumber());
                        }
                    }
                    // 告警类型转换
                    if (Objects.nonNull(entity.getAlarmType())) {
                        CommunicationStatusEnum communicationStatusEnum = CommunicationStatusEnum.getByValue(entity.getAlarmType());
                        if (Objects.nonNull(communicationStatusEnum)) {
                            // translate为真 常量名翻译
                            if (translate) {
                                excel.setAlarmType(TranslateUtil.chineseToEnglish
                                        (communicationStatusEnum.getLabel()));
                            } else {
                                excel.setAlarmType(communicationStatusEnum.getLabel());
                            }
                        }
                    }
                    // 告警信息翻译
                    if (translate && Objects.nonNull(entity.getAlarmContent())) {
                        excel.setAlarmContent(TranslateUtil.chineseToEnglish(entity.getAlarmContent()));
                    }
                    return excel;
                }).collect(Collectors.toList());
    }
}

package org.springblade.thingcom.warning.wrapper;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springblade.common.cache.DictCache;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.enums.DictEnum;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.thingcom.base.cache.SystemCache;
import org.springblade.thingcom.base.entity.SystemSet;
import org.springblade.thingcom.data.utils.RobotDataUtil;
import org.springblade.thingcom.data.vo.RobotTrackerLocationVO;
import org.springblade.thingcom.device.cache.RobotCache;
import org.springblade.thingcom.device.cache.RobotRegionalCache;
import org.springblade.thingcom.device.cache.TrackerRegionalCache;
import org.springblade.thingcom.device.entity.Robot;
import org.springblade.thingcom.device.entity.RobotRegional;
import org.springblade.thingcom.device.entity.Tracker;
import org.springblade.thingcom.device.entity.TrackerRegional;
import org.springblade.thingcom.device.service.RobotService;
import org.springblade.thingcom.device.vo.RobotRealDataVO;
import org.springblade.thingcom.translate.utils.TranslateUtil;
import org.springblade.thingcom.warning.dto.RobotAlarmDTO;
import org.springblade.thingcom.warning.entity.RobotAlarm;
import org.springblade.thingcom.warning.enums.AlarmTypeEnum;
import org.springblade.thingcom.warning.excel.RobotAlarmExcel;
import org.springblade.thingcom.warning.excel.RobotAlarmTableExcel;
import org.springblade.thingcom.warning.vo.RobotAlarmVO;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 机器人实时告警包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-01-20 13:58:44
 */
public class RobotAlarmWrapper extends BaseEntityWrapper<RobotAlarm, RobotAlarmVO> {

    private static final BladeRedis bladeRedis;
    private static final RobotService robotService;

    static {
        bladeRedis = SpringUtil.getBean(BladeRedis.class);
        robotService = SpringUtil.getBean(RobotService.class);
    }

    public static RobotAlarmWrapper build() {
        return new RobotAlarmWrapper();
    }

    @Override
    public RobotAlarmVO entityVO(RobotAlarm entity) {
        if (null == entity) {
            return null;
        }
        // 系统配置
        SystemSet systemSet = SystemCache.getById(CommonConstant.SYSTEM_CONFIG_ID);
        RobotAlarmVO vo = BeanUtil.copyProperties(entity, RobotAlarmVO.class);
        vo.setAlarmTypeVo(AlarmTypeEnum.translationValue(vo.getAlarmType()));
        // 获取机器人缓存
        if (Objects.nonNull(vo.getRobotId())) {
            Robot robot = RobotCache.getById(vo.getRobotId());
            if (Objects.nonNull(robot)) {
                vo.setRobotName(robot.getName());
                // 获取机器人分区缓存
                RobotRegional robotRegional = RobotRegionalCache.getById(robot.getRobotRegionalId());
                if (Objects.nonNull(robotRegional)) {
                    vo.setRobotRegionalNumber(robotRegional.getRegionalNumber());
                }
                // 机器人位置，计算当前处于哪个跟踪器
                RobotRealDataVO robotLocationAndDirection = RobotDataUtil.getRobotRealDataByEui(robot.getEui());
                Integer location = robotLocationAndDirection.getLocation();
                Integer direction = robotLocationAndDirection.getDirection();
                RobotTrackerLocationVO robotTrackerLocation
                        = RobotDataUtil.getRobotTrackerLocation(robot.getClearingPathId(), systemSet, location, direction);
                vo.setTrackerName(robotTrackerLocation.getLocationInfo());
                if (Objects.nonNull(robotTrackerLocation.getTracker())) {
                    // 获取跟踪器区域缓存
                    TrackerRegional trackerRegional = TrackerRegionalCache.getById(robotTrackerLocation.getTracker().getRegionalId());
                    if (Objects.nonNull(trackerRegional)) {
                        vo.setTrackerRegionalName(trackerRegional.getName());
                    }
                }
            }
        }
        return vo;
    }

    public void voWrapper(RobotAlarmVO vo, SystemSet systemSet, Map<Long, List<Tracker>> trackerGroupMap) {
        vo.setAlarmTypeVo(AlarmTypeEnum.translationValue(vo.getAlarmType()));
        if (Objects.nonNull(trackerGroupMap)) {
            // 机器人位置，计算当前处于哪个跟踪器
            RobotRealDataVO robotLocationAndDirection = RobotDataUtil.getRobotRealDataByEui(vo.getRobotEui());
            Integer location = robotLocationAndDirection.getLocation();
            Integer direction = robotLocationAndDirection.getDirection();
            RobotTrackerLocationVO robotTrackerLocation
                    = RobotDataUtil.getRobotTrackerLocation(trackerGroupMap.get(vo.getRobotClearingPathId()), systemSet, location, direction);
            vo.setTrackerName(robotTrackerLocation.getLocationInfo());
            if (Objects.nonNull(robotTrackerLocation.getTracker())) {
                // 获取跟踪器区域缓存
                TrackerRegional trackerRegional = TrackerRegionalCache.getById(robotTrackerLocation.getTracker().getRegionalId());
                if (Objects.nonNull(trackerRegional)) {
                    vo.setTrackerRegionalName(trackerRegional.getName());
                }
            }
        }
    }

    public List<RobotAlarmVO> listVoExt(List<RobotAlarmVO> list, SystemSet systemSet, Map<Long, List<Tracker>> trackerGroupMap) {
        return list.stream().map(m -> {
                    voWrapper(m, systemSet, trackerGroupMap);
                    return m;
                })
                .collect(Collectors.toList());
    }

    public IPage<RobotAlarmVO> pageVoExt(IPage<RobotAlarmVO> pages, Map<Long, List<Tracker>> trackerGroupMap) {
        // 系统配置
        SystemSet systemSet = SystemCache.getById(CommonConstant.SYSTEM_CONFIG_ID);
        List<RobotAlarmVO> records = this.listVoExt(pages.getRecords(), systemSet, trackerGroupMap);
        IPage<RobotAlarmVO> pageVo = new Page(pages.getCurrent(), pages.getSize(), pages.getTotal());
        pageVo.setRecords(records);
        return pageVo;
    }

    /**
     * 设置sql查询条件
     *
     * @param dto
     * @return LambdaQueryWrapper<RobotAlarm>
     */
    public LambdaQueryWrapper<RobotAlarm> setQueryParam(RobotAlarmDTO dto) {
        LambdaQueryWrapper<RobotAlarm> lqw = new LambdaQueryWrapper<>();
        // 告警ID
        lqw.eq(Objects.nonNull(dto.getId()), RobotAlarm::getId, dto.getId());
        // 告警类型
        lqw.eq(Objects.nonNull(dto.getAlarmType()), RobotAlarm::getAlarmType, dto.getAlarmType());
        // 告警内容
        lqw.like(Func.isNotBlank(dto.getAlarmContent()), RobotAlarm::getAlarmContent, dto.getAlarmContent());
        // 时间查询
        if (Objects.nonNull(dto.getBeginDate()) && Objects.nonNull(dto.getEndDate())) {
            lqw.between(RobotAlarm::getTime, dto.getBeginDate(), dto.getEndDate());
        }
        // 机器人区域id
        if (Objects.nonNull(dto.getRobotRegionalId())) {
            List<Robot> list = robotService.list(Wrappers.<Robot>lambdaQuery().select(Robot::getId)
                    .eq(Robot::getRobotRegionalId, dto.getRobotRegionalId()));
            if (CollUtil.isNotEmpty(list)) {
                lqw.in(RobotAlarm::getRobotId, list.stream().map(Robot::getId).collect(Collectors.toList()));
            } else {
                lqw.in(RobotAlarm::getRobotId, -1);
            }
        }
        // 机器人id
        lqw.eq(Objects.nonNull(dto.getRobotId()), RobotAlarm::getRobotId, dto.getRobotId());
        // 时间倒序
        lqw.orderByDesc(RobotAlarm::getTime);
        return lqw;
    }

    /**
     * 卡片导出列表
     *
     * @param entityList
     * @return
     */
    public List<RobotAlarmExcel> cardListToExcel(List<RobotAlarm> entityList, Boolean translate) {
        return entityList.stream()
                .map(entity -> {
                    RobotAlarmExcel excel = BeanUtil.copyProperties(entity, RobotAlarmExcel.class);
                    // 获取机器人名称
                    if (Objects.nonNull(entity.getRobotId())) {
                        Robot robot = RobotCache.getById(entity.getRobotId());
                        excel.setRobotName(robot.getName());
                    }
                    // 转换告警类型
                    if (Objects.nonNull(entity.getAlarmType())) {
                        // translate为真 常量名翻译
                        if (translate) {
                            excel.setAlarmType(TranslateUtil.chineseToEnglish
                                    (DictCache.getValue(DictEnum.ALARM_STATUS, entity.getAlarmType())));
                        } else {
                            excel.setAlarmType(DictCache.getValue(DictEnum.ALARM_STATUS, entity.getAlarmType()));
                        }
                    }
                    // 告警内容翻译
                    if (translate && StrUtil.isNotBlank(entity.getAlarmContent())) {
                        excel.setAlarmContent(TranslateUtil.chineseToEnglish(entity.getAlarmContent()));
                    }
                    // 解决建议翻译
                    if (translate && StrUtil.isNotBlank(entity.getSolution())) {
                        excel.setSolution(TranslateUtil.chineseToEnglish(entity.getSolution()));
                    }
                    return excel;
                }).collect(Collectors.toList());
    }

    /**
     * 表格导出列表
     *
     * @param entityList
     * @return
     */
    public List<RobotAlarmTableExcel> tableListToExcel(List<RobotAlarm> entityList, Boolean translate) {
        // 系统配置
        SystemSet systemSet = SystemCache.getById(CommonConstant.SYSTEM_CONFIG_ID);
        return entityList.stream()
                .map(entity -> {
                    RobotAlarmTableExcel excel = BeanUtil.copyProperties(entity, RobotAlarmTableExcel.class);
                    // 获取机器人名称
                    Robot robot = RobotCache.getById(entity.getRobotId());
                    if (Objects.nonNull(robot)) {
                        excel.setRobotName(robot.getName());
                        // 获取机器人分区缓存
                        RobotRegional robotRegional = RobotRegionalCache.getById(robot.getRobotRegionalId());
                        if (Objects.nonNull(robotRegional)) {
                            excel.setRegionalNumber(robotRegional.getRegionalNumber());
                        }
                        // 机器人位置，计算当前处于哪个跟踪器
                        RobotRealDataVO robotLocationAndDirection = RobotDataUtil.getRobotRealDataByEui(robot.getEui());
                        Integer location = robotLocationAndDirection.getLocation();
                        Integer direction = robotLocationAndDirection.getDirection();
                        RobotTrackerLocationVO robotTrackerLocation
                                = RobotDataUtil.getRobotTrackerLocation(robot.getClearingPathId(), systemSet, location, direction);
                        // translate为真 常量名翻译
                        if (translate) {
                            excel.setTrackerName(TranslateUtil.chineseToEnglish
                                    (robotTrackerLocation.getLocationInfo()));
                        } else {
                            excel.setTrackerName(robotTrackerLocation.getLocationInfo());
                        }
                        if (Objects.nonNull(robotTrackerLocation.getTracker())) {
                            // 获取跟踪器区域缓存
                            TrackerRegional trackerRegional = TrackerRegionalCache.getById(robotTrackerLocation.getTracker().getRegionalId());
                            if (Objects.nonNull(trackerRegional)) {
                                excel.setTrackerRegionalName(trackerRegional.getName());
                            }
                        }
                    }
                    // 转换告警类型
                    if (Objects.nonNull(entity.getAlarmType())) {
                        // translate为真 常量名翻译
                        if (translate) {
                            excel.setAlarmType(TranslateUtil.chineseToEnglish
                                    (DictCache.getValue(DictEnum.ALARM_STATUS, entity.getAlarmType())));
                        } else {
                            excel.setAlarmType(AlarmTypeEnum.translationValue(entity.getAlarmType()));
                        }
                    }
                    // 告警内容翻译
                    if (translate && Objects.nonNull(entity.getAlarmContent())) {
                        excel.setAlarmContent(TranslateUtil.chineseToEnglish(entity.getAlarmContent()));
                    }
                    // 解决建议翻译
                    if (translate && Objects.nonNull(entity.getSolution())) {
                        excel.setSolution(TranslateUtil.chineseToEnglish(entity.getSolution()));
                    }
                    return excel;
                }).collect(Collectors.toList());
    }
}
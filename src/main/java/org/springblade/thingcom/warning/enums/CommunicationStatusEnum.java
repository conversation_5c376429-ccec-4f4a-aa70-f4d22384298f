package org.springblade.thingcom.warning.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springblade.core.tool.support.Kv;

import java.util.ArrayList;
import java.util.List;

@Getter
@AllArgsConstructor
public enum CommunicationStatusEnum {

    /**
     * 通讯超时
     */
    TIMEOUT(1, "通讯超时", "检查网络连接"),

    /**
     * 信号较弱
     */
    //WEAK_SIGNAL(2, "信号较弱", "检查网络环境"),

    /**
     * SIM卡信息
     */
    //SIM_CARD_ISSUE(3, "SIM卡未插好或松动", "检查SIM卡"),

    /**
     * 离线
     */
    OFFLINE(4, "离线", "检查网关是否上电"),
    ;

    private final Integer value;
    private final String label;
    private final String solution;

    /**
     * 下拉列表
     */
    public static List<Kv> toList() {
        List<Kv> ret = new ArrayList<>();
        for (CommunicationStatusEnum e : CommunicationStatusEnum.values()) {
            Kv kv = Kv.create().set("value", e.getValue())
                    .set("label", e.getLabel());
            ret.add(kv);
        }
        return ret;
    }

    public static CommunicationStatusEnum getByValue(Integer value) {
        for (CommunicationStatusEnum e : CommunicationStatusEnum.values()) {
            if (e.value.equals(value)) {
                return e;
            }
        }
        return null;
    }

    public static String translationValue(Integer value) {
        for (CommunicationStatusEnum e : CommunicationStatusEnum.values()) {
            if (e.value.equals(value)) {
                return e.getLabel();
            }
        }
        return null;
    }
}

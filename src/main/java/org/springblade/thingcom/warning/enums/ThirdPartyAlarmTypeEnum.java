package org.springblade.thingcom.warning.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * @ClassName ThirdPartyAlarmTypeEnum
 * @Description 第三方告警类型枚举
 * <AUTHOR>
 * @Date 2024/7/31 18:03
 * @Version 1.0
 **/
@Getter
@AllArgsConstructor
public enum ThirdPartyAlarmTypeEnum {

    /**
     * 离线
     */
    OFFLINE(0, "通道离线", "检查网络连接"),

    /**
     * 支架角度告警
     */
    TRACKER_ANGLE(1, "角度告警", "角度超过阈值"),

    /**
     * 支架异常告警
     */
    TRACKER(2, "支架异常告警", "第三方上报支架告警"),

    /**
     * 气象站异常告警
     */
    COMMUNICATION_BOX(3, "气象站异常告警", "第三方上报气象站告警"),

    /**
     * 风速告警
     */
    WIND_SPEED(4, "风速告警", "风速过大，不允许机器人运行"),

    ;

    private final Integer value;
    private final String label;
    private final String solution;

    /**
     * 根据value获取label
     *
     * @param value 值
     * @return label
     */
    public static String translationValue(Integer value) {
        ThirdPartyAlarmTypeEnum thirdPartyAlarmTypeEnum = Arrays.stream(ThirdPartyAlarmTypeEnum.values())
                .filter(e -> e.getValue().equals(value)).findFirst().orElse(null);
        if (Objects.nonNull(thirdPartyAlarmTypeEnum)) {
            return thirdPartyAlarmTypeEnum.getLabel();
        }
        return null;
    }

    /**
     * 根据value获取label
     *
     * @param value 值
     * @return label
     */
    public static ThirdPartyAlarmTypeEnum translation(Integer value) {
        return Arrays.stream(ThirdPartyAlarmTypeEnum.values())
                .filter(e -> e.getValue().equals(value)).findFirst().orElse(null);
    }

}

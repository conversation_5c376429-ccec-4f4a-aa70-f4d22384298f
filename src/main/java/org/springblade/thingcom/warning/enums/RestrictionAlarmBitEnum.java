package org.springblade.thingcom.warning.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * @ClassName RestrictionAlarmBitEnum
 * @Description 限制告警位枚举
 * <AUTHOR>
 * @Date 2025/4/9 10:38
 * @Version 1.0
 **/
@Getter
@AllArgsConstructor
public enum RestrictionAlarmBitEnum {

    /**
     * 正常
     */
    NORMAL(0, "00000000"),

    /**
     * 风速告警，bit0
     */
    WIND_SPEED(WeatherStationAlarmTypeEnum.WIND_SPEED.getValue(), "00000001"),

    /**
     * 湿度告警，bit1
     */
    HUMIDITY(WeatherStationAlarmTypeEnum.HUMIDITY.getValue(), "00000010"),

    /**
     * 温度告警，bit3
     */
    TEMPERATURE(WeatherStationAlarmTypeEnum.TEMPERATURE.getValue(), "00001000"),

    /**
     * 风速、湿度告警
     */
    WIND_SPEED_HUMIDITY(WeatherStationAlarmTypeEnum.WIND_SPEED_HUMIDITY.getValue(), "00000011"),

    /**
     * 风速、温度告警
     */
    WIND_SPEED_TEMPERATURE(WeatherStationAlarmTypeEnum.WIND_SPEED_TEMPERATURE.getValue(), "00001001"),

    /**
     * 湿度、温度告警
     */
    HUMIDITY_TEMPERATURE(WeatherStationAlarmTypeEnum.HUMIDITY_TEMPERATURE.getValue(), "00001010"),

    /**
     * 风速、湿度、温度告警
     */
    ENVIRONMENT(WeatherStationAlarmTypeEnum.ENVIRONMENT.getValue(), "00001011"),

    ;

    private final Integer value;
    private final String label;

    /**
     * 根据value获取label
     *
     * @param value 值
     * @return label
     */
    public static String translationValue(Integer value) {
        RestrictionAlarmBitEnum weatherStationAlarmTypeEnum = Arrays.stream(RestrictionAlarmBitEnum.values())
                .filter(e -> e.getValue().equals(value)).findFirst().orElse(null);
        if (Objects.nonNull(weatherStationAlarmTypeEnum)) {
            return weatherStationAlarmTypeEnum.getLabel();
        }
        return NORMAL.getLabel();
    }

}

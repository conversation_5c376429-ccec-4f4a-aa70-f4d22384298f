package org.springblade.thingcom.warning.service;

import org.springblade.thingcom.base.entity.SystemSet;
import org.springblade.thingcom.warning.dto.UpdateReadLabelDTO;

import java.math.BigDecimal;
import java.util.List;

/**
 * 告警服务接口
 *
 * <AUTHOR>
 * @since 2024-07-27 15:48:21
 */
public interface AlarmService {

    /**
     * 根据角度获取跟踪架状态
     *
     * @param angle 角度
     * @return 跟踪架状态
     * @see org.springblade.thingcom.device.enums.TrackerStatusEnum
     */
    Integer getTrackerStatusByAngle(BigDecimal angle);

    /**
     * 校验风速告警
     *
     * @param windSpeed          风速
     * @param windSpeedThreshold 风速阈值
     * @return true-风速告警，false-风速正常
     */
    boolean validateWindSpeedAlarm(BigDecimal windSpeed, BigDecimal windSpeedThreshold);

    /**
     * 校验湿度告警
     *
     * @param humidity          湿度
     * @param humidityThreshold 湿度阈值
     * @return true-湿度告警，false-湿度正常
     */
    boolean validateHumidityAlarm(BigDecimal humidity, BigDecimal humidityThreshold);

    /**
     * 校验温度告警
     *
     * @param temperature 温度
     * @param systemSet   系统设置
     * @return true-温度告警，false-温度正常
     */
    boolean validateTemperatureAlarm(BigDecimal temperature, SystemSet systemSet);

    /**
     * 更新告警已读状态
     *
     * @param dto 更新已读标签
     */
    void updateReadLabel(UpdateReadLabelDTO dto);

    /**
     * 告警通知
     */
    void alarmNotice();

    /**
     * 告警通知
     *
     * @param alarmServiceType 告警类型
     * @param alarmDataList    告警数据
     */
    void alarmNotice(Integer alarmServiceType, List<?> alarmDataList);

}
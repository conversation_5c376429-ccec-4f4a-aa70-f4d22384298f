package org.springblade.thingcom.warning.service;

import org.springblade.thingcom.core.mybatisPlus.IBaseService;
import org.springblade.thingcom.warning.dto.GatewayAlarmDTO;
import org.springblade.thingcom.warning.entity.GatewayAlarm;
import org.springblade.thingcom.warning.vo.GatewayAlarmVO;

import java.util.List;

/**
 * 网关实时告警服务接口
 *
 * <AUTHOR>
 * @since 2024-01-23 17:07:22
 */
public interface GatewayAlarmService extends IBaseService<GatewayAlarm> {

    /**
     * 检查数据合法性
     *
     * @param entity 实体对象
     * @return 异常消息，正常时为null
     */
    String validateData(GatewayAlarm entity);

    /**
     * 新增网关实时告警
     *
     * @param entity
     * @return
     */
    boolean saveData(GatewayAlarm entity);

    /**
     * 修改网关实时告警
     *
     * @param entity
     * @return
     */
    boolean updateData(GatewayAlarm entity);

    /**
     * 网关告警类型统计
     *
     * @return
     */
    List<GatewayAlarmVO> getAlarmType(GatewayAlarmDTO dto);

    /**
     * 更新告警状态
     */
    void updateStatus();
}

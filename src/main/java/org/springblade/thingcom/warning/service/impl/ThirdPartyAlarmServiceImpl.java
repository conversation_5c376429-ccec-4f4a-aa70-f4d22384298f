package org.springblade.thingcom.warning.service.impl;

import org.springblade.thingcom.core.mybatisPlus.impl.BaseServiceImpl;
import org.springblade.thingcom.warning.mapper.ThirdPartyAlarmMapper;
import org.springblade.thingcom.warning.entity.ThirdPartyAlarm;
import org.springblade.thingcom.warning.service.ThirdPartyAlarmService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 第三方系统实时告警服务实现类
 *
 * <AUTHOR>
 * @since 2024-07-25 16:48:21
 */
@Service
public class ThirdPartyAlarmServiceImpl extends BaseServiceImpl<ThirdPartyAlarmMapper, ThirdPartyAlarm> implements ThirdPartyAlarmService {
    
    @Override
    public String validateData(ThirdPartyAlarm entity) {
        // 检查名称是否存在
//       Long count = baseMapper.selectCount(Wrappers.<ThirdPartyAlarm>lambdaQuery()
//               .eq(ThirdPartyAlarm::getName, entity.getName())
                // 编辑时，排除自己
//              .ne(entity.getId() != null, ThirdPartyAlarm::getId, entity.getId()));
//       if (count > 0L) {
//          return "名称已经存在。名称：" + entity.getName();
//       }
        return null;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveData(ThirdPartyAlarm entity) {
        // 新增第三方系统实时告警
        boolean ret = save(entity);
        return ret;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateData(ThirdPartyAlarm entity) {
        // 修改第三方系统实时告警
        boolean ret = updateById(entity);
        return ret;
    }
}

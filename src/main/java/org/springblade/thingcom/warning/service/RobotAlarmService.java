package org.springblade.thingcom.warning.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.thingcom.core.mybatisPlus.IBaseService;
import org.springblade.thingcom.data.entity.RealData;
import org.springblade.thingcom.device.entity.Robot;
import org.springblade.thingcom.warning.dto.RobotAlarmDTO;
import org.springblade.thingcom.warning.entity.RobotAlarm;
import org.springblade.thingcom.warning.vo.RobotAlarmVO;

import java.util.List;

/**
 * 机器人实时告警服务接口
 *
 * <AUTHOR>
 * @since 2024-01-20 13:58:43
 */
public interface RobotAlarmService extends IBaseService<RobotAlarm> {

    /**
     * 分页查询机器人告警
     *
     * @param page
     * @param dto
     * @return
     */
    IPage<RobotAlarmVO> page(IPage<RobotAlarm> page, RobotAlarmDTO dto);

    /**
     * 告警类型统计
     *
     * @return
     */
    List<RobotAlarmVO> getAlarmType(RobotAlarmDTO dto);

    /**
     * 机器人分区统计
     *
     * @param statusAnalysisId
     * @return
     */
    List<RobotAlarmVO> getRegional(Long statusAnalysisId);

    /**
     * 机器人分区详情统计
     *
     * @param page
     * @param statusAnalysisId
     * @param robotRegionalNumber
     * @return
     */
    IPage<RobotAlarmVO> getRegionalList(IPage<RobotAlarmVO> page, Long statusAnalysisId, String robotRegionalNumber);

    /**
     * 删除机器人告警
     *
     * @param id
     */
    void deleteById(Long id);

    /**
     * 删除机器人告警
     *
     * @param idList
     */
    void deleteByIds(List<Long> idList);

    /**
     * 触发启动限制告警
     *
     * @param robot
     * @param alarmContent
     * @param statusAnalysisId
     */
    void triggerStartRestrictionAlarm(Robot robot, String alarmContent, Long statusAnalysisId);

    /**
     * 解除启动限制告警
     *
     * @param robotId
     */
    void relieveStartRestrictionAlarm(Long robotId);

    /**
     * 触发定时器异常告警
     *
     * @param robot
     * @param alarmContent
     * @param startRestrictionStatusAnalysisId
     */
    void triggerTimerExceptionAlarm(Robot robot, RealData realData);

    /**
     * 解除定时器异常告警
     *
     * @param robotId 机器人ID
     */
    void relieveTimerExceptionAlarm(Long robotId);
}

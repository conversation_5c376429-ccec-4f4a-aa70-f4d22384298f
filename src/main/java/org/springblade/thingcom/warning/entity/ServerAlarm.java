package org.springblade.thingcom.warning.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springblade.thingcom.core.mybatisPlus.entity.BaseEntity;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 服务器实时告警实体类
 *
 * <AUTHOR>
 * @since 2024-03-11 16:39:48
 */
@Data
@TableName("tb_server_alarm")
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class ServerAlarm extends BaseEntity<ServerAlarm> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 服务器id
     */
    private Long serverId;

    /**
     * 监控类型，1-cpu；2-内存；3-硬盘
     *
     * @see org.springblade.thingcom.core.server.enums.MonitorTypeEnum
     */
    private Integer monitorType;

    /**
     * 告警类型，详见字典表
     */
    private Integer alarmType;

    /**
     * 告警名称
     */
    private String alarmName;

    /**
     * 告警内容
     */
    private String alarmContent;

    /**
     * 解决建议
     */
    private String solution;

    /**
     * 数据上报时间
     */
    private LocalDateTime time;

    /**
     * 已读标识
     */
    private Integer readLabel = 0;
}

package org.springblade.thingcom.warning.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024-01-23 15:40
 */
@Data
@ColumnWidth(16)
@HeadRowHeight(52)
@ContentRowHeight(18)
public class RobotAlarmTableExcel implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 机器人区域编号
     */
    @ExcelProperty(value = "机器人区域编号")
    private String regionalNumber;

    /**
     * 机器人名称
     */
    @ExcelProperty(value = "机器人名称")
    private String robotName;

    /**
     * 跟踪器区域名称
     */
    @ExcelProperty(value = "跟踪器区域名称")
    private String trackerRegionalName;

    /**
     * 当前所在跟踪器位置
     */
    @ExcelProperty(value = "当前所在跟踪器位置")
    private String trackerName;

    /**
     * 告警类型
     */
    @ExcelProperty(value = "告警类型")
    private String alarmType;

    /**
     * 告警信息
     */
    @ColumnWidth(35)
    @ExcelProperty(value = "告警信息")
    private String alarmContent;

    /**
     * 解决建议
     */
    @ColumnWidth(50)
    @ExcelProperty(value = "解决建议")
    private String solution;

    /**
     * 告警时间
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "告警时间")
    private LocalDateTime time;
}

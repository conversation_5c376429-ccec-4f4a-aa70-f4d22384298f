package org.springblade.thingcom.warning.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName GpsGatewayAlarmExcel
 * @Description GPS网关实时告警excel
 * <AUTHOR>
 * @Date 2024/8/13 10:17
 * @Version 1.0
 **/
@Data
@ColumnWidth(16)
@HeadRowHeight(52)
@ContentRowHeight(18)
public class GpsGatewayAlarmExcel implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * gps网关编号
     */
    @ExcelProperty(value = "采集器编号")
    private String weatherStationNumber;

    /**
     * 告警类型
     */
    @ExcelProperty(value = "告警类型")
    private String alarmType;

    /**
     * 解决建议
     */
    @ColumnWidth(50)
    @ExcelProperty(value = "解决建议")
    private String solution;

    /**
     * 湿度，单位 RH%
     */
    @ExcelProperty(value = "湿度(RH%)")
    private String humidity;

    /**
     * 风速
     */
    @ColumnWidth(35)
    @ExcelProperty(value = "风速(m/s)")
    private String windSpeed;

    /**
     * 告警时间
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "告警时间")
    private LocalDateTime time;
}
package org.springblade.thingcom.warning.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.thingcom.warning.entity.GatewayAlarm;


/**
 * 网关实时告警出参类
 *
 * <AUTHOR>
 * @since 2024-01-23 17:07:22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties({"deleted", "createTime", "creatorId", "updateId"})
public class GatewayAlarmVO extends GatewayAlarm {
    private static final long serialVersionUID = 1L;

    /**
     * 告警类型
     */
    private String alarmTypeVo;

    /**
     * 网关编号
     */
    private String gatewayNumber;

    /**
     * 告警类型数量
     */
    private Integer alarmTypeCount;
}

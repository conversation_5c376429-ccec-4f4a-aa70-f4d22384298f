package org.springblade.thingcom.warning.vo;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.EqualsAndHashCode;
import org.springblade.thingcom.warning.entity.ServerAlarm;


/**
 * 服务器实时告警出参类
 *
 * <AUTHOR>
 * @since 2024-03-11 16:39:48
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties({"deleted", "createTime", "creatorId", "updateTime", "updateId"})
public class ServerAlarmVO extends ServerAlarm {
    private static final long serialVersionUID = 1L;

    /**
     * 服务器名称
     */
    private String serverName;

    /**
     * 告警类型
     */
    private String alarmTypeVo;
}

package org.springblade.thingcom.warning.job;

import org.springblade.common.utils.SystemUtil;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.thingcom.warning.service.AlarmService;
import org.springblade.thingcom.warning.service.GatewayAlarmService;

/**
 * @ClassName GatewayAlarmJob
 * @Description: 网关告警定时任务，每1分钟执行一次
 * @Author: zgq
 * @Date: 2024/6/6 14:03
 * @Version: 1.0
 **/
public class GatewayAlarmJob implements Runnable {

    private static final GatewayAlarmService gatewayAlarmService;
    private static final AlarmService alarmService;
    private static final SystemUtil systemUtil;

    static {
        gatewayAlarmService = SpringUtil.getBean(GatewayAlarmService.class);
        alarmService = SpringUtil.getBean(AlarmService.class);
        systemUtil = SpringUtil.getBean(SystemUtil.class);
    }

    @Override
    public void run() {
        boolean scheduledEnabled = systemUtil.getTaskEnabled();
        if (!scheduledEnabled) {
            return;
        }
        gatewayAlarmService.updateStatus();
        // 告警通知
        alarmService.alarmNotice();
    }
}
package org.springblade.thingcom.warning.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.thingcom.core.base.dto.BaseDTO;

/**
 * <AUTHOR>
 * @since 2024-01-23 17:17
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GatewayAlarmDTO extends BaseDTO {
    private static final long serialVersionUID = 947246827381500784L;

    /**
     * 告警ID
     */
    private Long id;

    /**
     * 网关id
     */
    private Long gatewayId;

    /**
     * 告警类型
     */
    private Integer alarmType;

    /**
     * 告警内容
     */
    private String alarmContent;

    /**
     * 网关编号
     */
    private String gatewayNumber;

}

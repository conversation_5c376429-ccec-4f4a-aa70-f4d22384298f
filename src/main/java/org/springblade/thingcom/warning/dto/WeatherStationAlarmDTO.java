package org.springblade.thingcom.warning.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.thingcom.core.base.dto.BaseDTO;
import org.springblade.thingcom.operationMaintenance.enums.WeatherStationTypeEnum;

/**
 * <AUTHOR>
 * @since 2024-03-20 10:31
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WeatherStationAlarmDTO extends BaseDTO {
    private static final long serialVersionUID = -6581957026055294993L;

    /**
     * 告警ID
     */
    private Long id;
    /**
     * 气象站id
     */
    private Long weatherStationId;
    /**
     * 气象站类型，1-Thingcom；2-Arctech
     * @see WeatherStationTypeEnum
     */
    private Integer weatherStationType;
    /**
     * 告警类型，详见字典表
     */
    private Integer alarmType;
    /**
     * 告警内容
     */
    private String alarmContent;
    /**
     * 位置
     */
    private String location;
}

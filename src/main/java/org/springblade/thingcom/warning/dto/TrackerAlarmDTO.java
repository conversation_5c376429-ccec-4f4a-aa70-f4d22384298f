package org.springblade.thingcom.warning.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.thingcom.core.base.dto.BaseDTO;

/**
 * <AUTHOR>
 * @since 2024-03-21 11:17
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TrackerAlarmDTO extends BaseDTO {
    private static final long serialVersionUID = -2414688118606949569L;

    /**
     * 告警ID
     */
    private Long id;

    /**
     * 告警类型
     */
    private Integer alarmType;

    /**
     * 告警内容
     */
    private String alarmContent;

    /**
     * 跟踪器区域id
     */
    private Long trackerRegionalId;

    /**
     * 跟踪器id
     */
    private Long trackerId;

    /**
     * 跟踪器区域名称
     */
    private String trackerRegionalName;
    /**
     * 跟踪器名称
     */
    private String trackerName;
}

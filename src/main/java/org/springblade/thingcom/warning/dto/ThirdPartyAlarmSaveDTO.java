package org.springblade.thingcom.warning.dto;

import lombok.Data;
import org.springblade.thingcom.warning.entity.ThirdPartyAlarm;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 第三方系统实时告警入参类
 *
 * <AUTHOR>
 * @since 2024-07-25 16:48:21
 */
@Data
public class ThirdPartyAlarmSaveDTO {
    /**
	 * 名称
	 */
    private String name;

    /**
	 * 告警类型  1-正常  2-离线
	 */
    private Integer alarmType;

    /**
	 * IP地址
	 */
    private String ip;

    /**
	 * 端口号
	 */
    private Integer port;

}

package org.springblade.thingcom.warning.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.thingcom.core.base.dto.BaseDTO;

/**
 * <AUTHOR>
 * @since 2024-01-20 14:18
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RobotAlarmDTO extends BaseDTO {
    private static final long serialVersionUID = -2716876313670877213L;

    /**
     * 告警ID
     */
    private Long id;

    /**
     * 告警类型
     */
    private Integer alarmType;

    /**
     * 告警内容
     */
    private String alarmContent;

    /**
     * 机器人区域id
     */
    private Long robotRegionalId;

    /**
     * 机器人id
     */
    private Long robotId;

}

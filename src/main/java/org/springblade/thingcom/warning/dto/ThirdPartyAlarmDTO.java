package org.springblade.thingcom.warning.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.thingcom.core.base.dto.BaseDTO;

/**
 * <AUTHOR>
 * @since 2024-07-25 16:56
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ThirdPartyAlarmDTO extends BaseDTO {
    private static final long serialVersionUID = 5677890082742863721L;

    /**
     * 第三方对接信息表ID
     */
    private Long thirdPartyMessageId;

    /**
     * 名称
     */
    private String name;

    /**
     * 告警内容
     */
    private String alarmContent;
}

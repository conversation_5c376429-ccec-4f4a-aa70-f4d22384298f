package org.springblade.thingcom.warning.controller;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.thingcom.core.excel.handler.AutoColumnWidthHandler;
import org.springblade.thingcom.core.excel.utils.ThingcomExcelUtil;
import org.springblade.thingcom.device.entity.Tracker;
import org.springblade.thingcom.device.entity.TrackerRegional;
import org.springblade.thingcom.device.service.TrackerRegionalService;
import org.springblade.thingcom.device.service.TrackerService;
import org.springblade.thingcom.translate.utils.TranslateUtil;
import org.springblade.thingcom.warning.dto.TrackerAlarmDTO;
import org.springblade.thingcom.warning.entity.TrackerAlarm;
import org.springblade.thingcom.warning.enums.AlarmTypeEnum;
import org.springblade.thingcom.warning.excel.TrackerAlarmExcel;
import org.springblade.thingcom.warning.excel.TrackerAlarmTableExcel;
import org.springblade.thingcom.warning.service.TrackerAlarmService;
import org.springblade.thingcom.warning.vo.TrackerAlarmVO;
import org.springblade.thingcom.warning.wrapper.TrackerAlarmWrapper;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 跟踪器实时告警控制层
 *
 * <AUTHOR>
 * @since 2024-03-21 11:14:52
 */
@AllArgsConstructor
@RestController
@RequestMapping("/trackerAlarm")
public class TrackerAlarmController extends BladeController {
    private final TrackerAlarmService trackerAlarmService;
    private final TrackerService trackerService;
    private final TrackerRegionalService trackerRegionalService;

    /**
     * 跟踪器实时告警分页查询
     *
     * @param query
     * @param dto
     * @return
     */
    @GetMapping("/page")
    public R<IPage<TrackerAlarmVO>> page(Query query, TrackerAlarmDTO dto) {
        LambdaQueryWrapper<TrackerAlarm> wrapper = setQueryParam(dto);
        IPage<TrackerAlarm> iPage = trackerAlarmService.page(Condition.getPage(query), wrapper);
        return data(TrackerAlarmWrapper.build().pageVO(iPage));
    }

    /**
     * sql检索条件
     *
     * @param dto
     * @return
     */
    private LambdaQueryWrapper<TrackerAlarm> setQueryParam(TrackerAlarmDTO dto) {
        LambdaQueryWrapper<TrackerAlarm> lqw = new LambdaQueryWrapper<>();
        // 告警id
        lqw.eq(Objects.nonNull(dto.getId()), TrackerAlarm::getId, dto.getId());
        // 时间查询
        if (Objects.nonNull(dto.getBeginDate()) && Objects.nonNull(dto.getEndDate())) {
            lqw.between(TrackerAlarm::getTime, dto.getBeginDate(), dto.getEndDate());
        }
        // 告警类型查询
        lqw.eq(Objects.nonNull(dto.getAlarmType()), TrackerAlarm::getAlarmType, dto.getAlarmType());
        // 告警内容
        lqw.like(Func.isNotBlank(dto.getAlarmContent()), TrackerAlarm::getAlarmContent, dto.getAlarmContent());
        // 跟踪器区域名称查询
        if (Objects.nonNull(dto.getTrackerRegionalId())) {
            List<TrackerRegional> regionalList = trackerRegionalService.list(Wrappers.<TrackerRegional>lambdaQuery()
                    .eq(TrackerRegional::getId, dto.getTrackerRegionalId()));
            if (CollUtil.isEmpty(regionalList)) {
                return lqw.in(TrackerAlarm::getId, -1);
            }
            List<Long> regionalIdList = regionalList.stream().map(TrackerRegional::getId)
                    .collect(Collectors.toList());
            List<Tracker> trackerList = trackerService.list(Wrappers.<Tracker>lambdaQuery()
                    .in(Tracker::getRegionalId, regionalIdList));
            if (CollUtil.isEmpty(trackerList)) {
                return lqw.in(TrackerAlarm::getId, -1);
            }
            // 跟踪器查询
            lqw.in(TrackerAlarm::getTrackerId, trackerList.stream().map(Tracker::getId).collect(Collectors.toList()));
        }
        // 跟踪器名称查询
        if (Objects.nonNull(dto.getTrackerId())) {
            List<Tracker> trackerList = trackerService.list(Wrappers.<Tracker>lambdaQuery()
                    .eq(Tracker::getId, dto.getTrackerId()));
            if (CollUtil.isEmpty(trackerList)) {
                return lqw.in(TrackerAlarm::getId, -1);
            }
            // 跟踪器查询
            lqw.in(TrackerAlarm::getTrackerId, trackerList.stream().map(Tracker::getId).collect(Collectors.toList()));
        }
        return lqw;
    }

    /**
     * 告警类型统计
     *
     * @return
     */
    @GetMapping("/statistics")
    public R<List<TrackerAlarmVO>> alarmTypeStatistics() {
        List<TrackerAlarmVO> list = trackerAlarmService.getAlarmType();
        for (TrackerAlarmVO trackerAlarmVO : list) {
            trackerAlarmVO.setAlarmTypeVo(AlarmTypeEnum.translationValue(trackerAlarmVO.getAlarmType()));
        }
        return data(list);
    }

    /**
     * 分区统计
     *
     * @param
     * @return
     */
    @GetMapping("/regionalStatistics")
    public R<List<TrackerAlarmVO>> regionalStatistics(TrackerAlarmDTO dto) {
        List<TrackerAlarmVO> list = trackerAlarmService.getRegional(dto);
        return data(list);
    }

    /**
     * 分区详情统计
     *
     * @param query
     * @param alarmType
     * @param regionalNumber
     * @return
     */
    @GetMapping("/robotRegionalList")
    public R<IPage<TrackerAlarmVO>> robotRegionalList(Query query, Integer alarmType, String regionalNumber) {
        IPage<TrackerAlarmVO> list = trackerAlarmService.getRegionalList(Condition.getPage(query)
                , alarmType, regionalNumber);
        return data(list);
    }

    /**
     * 导出卡片模式
     *
     * @param dto
     * @param response
     * @throws Exception
     */
    @GetMapping("/export-data")
    public void exportData(TrackerAlarmDTO dto, HttpServletResponse response) throws Exception {
        LambdaQueryWrapper<TrackerAlarm> wrapper = setQueryParam(dto);
        List<TrackerAlarm> list = trackerAlarmService.list(wrapper);
        List<TrackerAlarmExcel> data = TrackerAlarmWrapper.build().listToExcel(list);
        String sheetName = "支架告警卡片模式";
        ThingcomExcelUtil.setExportDataFile(response, "支架告警卡片模式");
        EasyExcel.write(response.getOutputStream(), TrackerAlarmExcel.class)
                .inMemory(true)
                .sheet(sheetName).doWrite(data);
    }

    /**
     * 导出卡片模式
     *
     * @param dto
     * @param response
     * @throws Exception
     */
    @GetMapping("/export-data-table")
    public void exportDataTable(TrackerAlarmDTO dto, HttpServletResponse response) throws Exception {
        LambdaQueryWrapper<TrackerAlarm> wrapper = setQueryParam(dto);
        List<TrackerAlarm> list = trackerAlarmService.list(wrapper);
        String sheetName = "支架告警表格模式";
        // 判断是否需要翻译
        boolean translate = TranslateUtil.isTranslate(dto.getLang());
        // 表内数据常量翻译
        List<TrackerAlarmTableExcel> data = TrackerAlarmWrapper.build().tableListToExcel(list, translate);
        // 表头翻译
        List<List<String>> headerList = ThingcomExcelUtil.getHeadList(TrackerAlarmTableExcel.class, translate);
        // translate为真，翻译
        if (translate) {
            // 表文件名的翻译
            sheetName = TranslateUtil.chineseToEnglish(sheetName);
        }
        ThingcomExcelUtil.setExportDataFile(response, sheetName);
        EasyExcel.write(response.getOutputStream(), TrackerAlarmTableExcel.class)
                .inMemory(true)
                .registerWriteHandler(new AutoColumnWidthHandler())
                .head(headerList)
                .sheet(sheetName).doWrite(data);
    }
}

package org.springblade.thingcom.warning.controller;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import io.jsonwebtoken.lang.Collections;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.log.annotation.ApiLog;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.thingcom.core.excel.handler.AutoColumnWidthHandler;
import org.springblade.thingcom.core.excel.utils.ThingcomExcelUtil;
import org.springblade.thingcom.translate.utils.TranslateUtil;
import org.springblade.thingcom.warning.dto.ThirdPartyAlarmDTO;
import org.springblade.thingcom.warning.dto.ThirdPartyAlarmSaveDTO;
import org.springblade.thingcom.warning.entity.ThirdPartyAlarm;
import org.springblade.thingcom.warning.excel.ThirdPartyAlarmExcel;
import org.springblade.thingcom.warning.service.ThirdPartyAlarmService;
import org.springblade.thingcom.warning.vo.ThirdPartyAlarmVo;
import org.springblade.thingcom.warning.wrapper.ThirdPartyAlarmWrapper;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * 第三方系统实时告警控制层
 *
 * <AUTHOR>
 * @since 2024-07-25 16:48:21
 */
@AllArgsConstructor
@RestController
@RequestMapping("/thirdPartyAlarm")
public class ThirdPartyAlarmController extends BladeController {
    private final ThirdPartyAlarmService thirdPartyAlarmService;

    /**
     * 第三方系统实时告警分页查询
     *
     * @param query
     * @return
     */
    @GetMapping("/page")
    public R<IPage<ThirdPartyAlarmVo>> page(Query query, ThirdPartyAlarmDTO dto) {
        LambdaQueryWrapper<ThirdPartyAlarm> wrapper = setQueryParam(dto);
        IPage<ThirdPartyAlarm> iPage = thirdPartyAlarmService.page(Condition.getPage(query), wrapper);
        return data(ThirdPartyAlarmWrapper.build().pageVO(iPage));
    }

    /**
     * 第三方系统实时告警列表查询
     *
     * @param thirdPartyAlarm
     * @return
     */
    @GetMapping("/list")
    public R<List<ThirdPartyAlarmVo>> list(ThirdPartyAlarm thirdPartyAlarm) {
        List<ThirdPartyAlarm> list = thirdPartyAlarmService.list(Wrappers.<ThirdPartyAlarm>lambdaQuery(thirdPartyAlarm));
        return data(ThirdPartyAlarmWrapper.build().listVO(list));
    }

    /**
     * 查看第三方系统实时告警详情
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public R<ThirdPartyAlarmVo> show(@PathVariable Serializable id) {
        ThirdPartyAlarm entity = thirdPartyAlarmService.getById(id);
        return data(ThirdPartyAlarmWrapper.build().entityVO(entity));
    }

    /**
     * 新增第三方系统实时告警
     *
     * @param dto
     * @return
     */
    @ApiLog("新增第三方系统实时告警")
    @PostMapping
    public R save(@Valid @RequestBody ThirdPartyAlarmSaveDTO dto) {
        ThirdPartyAlarm entity = BeanUtil.copy(dto, ThirdPartyAlarm.class);
        // 检查数据合法性
        String errMsg = thirdPartyAlarmService.validateData(entity);
        if (errMsg != null) {
            throw new ServiceException(errMsg);
        }
        return status(thirdPartyAlarmService.save(entity));
    }

    /**
     * 修改第三方系统实时告警
     *
     * @param dto
     * @return
     */
    @ApiLog("修改第三方系统实时告警")
    @PutMapping
    public R update(@Valid @RequestBody ThirdPartyAlarmSaveDTO dto) {
        ThirdPartyAlarm entity = BeanUtil.copy(dto, ThirdPartyAlarm.class);

        // 检查数据合法性
        String errMsg = thirdPartyAlarmService.validateData(entity);
        if (errMsg != null) {
            throw new ServiceException(errMsg);
        }
        return status(thirdPartyAlarmService.updateById(entity));
    }

    /**
     * 批量删除第三方系统实时告警
     *
     * @param ids 用逗号分隔的多个id
     * @return
     */
    @ApiLog("批量删除第三方系统实时告警")
    @DeleteMapping("/{ids}")
    public R batchDelete(@PathVariable Serializable ids) {
        List<Long> idList = Func.toLongList(ids.toString());
        if (Collections.isEmpty(idList)) {
            return status(true);
        }
        return status(thirdPartyAlarmService.removeWithFill(new ThirdPartyAlarm(),
                Wrappers.<ThirdPartyAlarm>lambdaQuery().in(ThirdPartyAlarm::getId, idList)));
    }

    /**
     * 导出第三方系统实时告警数据
     *
     * @param response
     * @param dto
     * @throws IOException
     */
    @GetMapping("/export-data")
    public void exportData(HttpServletResponse response, ThirdPartyAlarmDTO dto) throws IOException {
        LambdaQueryWrapper<ThirdPartyAlarm> wrapper = setQueryParam(dto);
        List<ThirdPartyAlarm> list = thirdPartyAlarmService.list(wrapper);
        String sheetName = "第三方系统告警表";
        // 判断是否需要翻译
        boolean translate = TranslateUtil.isTranslate(dto.getLang());
        // 表内数据常量翻译
        List<ThirdPartyAlarmExcel> data = ThirdPartyAlarmWrapper.build().listToExcel(list, translate);
        // 表头翻译
        List<List<String>> headerList = ThingcomExcelUtil.getHeadList(ThirdPartyAlarmExcel.class, translate);
        // translate为真，翻译
        if (translate) {
            // 表文件名的翻译
            sheetName = TranslateUtil.chineseToEnglish(sheetName);
        }
        ThingcomExcelUtil.setExportDataFile(response, sheetName);
        EasyExcel.write(response.getOutputStream(), ThirdPartyAlarmExcel.class)
                .inMemory(true)
                .registerWriteHandler(new AutoColumnWidthHandler())
                .head(headerList)
                .sheet(sheetName).doWrite(data);
    }

    /**
     * sql检索条件
     *
     * @param dto
     * @return
     */
    private LambdaQueryWrapper<ThirdPartyAlarm> setQueryParam(ThirdPartyAlarmDTO dto) {
        LambdaQueryWrapper<ThirdPartyAlarm> lqw = new LambdaQueryWrapper<>();
        // 第三方对接信息表ID
        lqw.eq(Objects.nonNull(dto.getThirdPartyMessageId()), ThirdPartyAlarm::getThirdPartyMessageId
                , dto.getThirdPartyMessageId());
        // 告警内容
        lqw.like(Func.isNotBlank(dto.getAlarmContent()), ThirdPartyAlarm::getAlarmContent, dto.getAlarmContent());
        // 时间查询
        if (Objects.nonNull(dto.getBeginDate()) && Objects.nonNull(dto.getEndDate())) {
            lqw.between(ThirdPartyAlarm::getUpdateTime, dto.getBeginDate(), dto.getEndDate());
        }
        return lqw;
    }
}

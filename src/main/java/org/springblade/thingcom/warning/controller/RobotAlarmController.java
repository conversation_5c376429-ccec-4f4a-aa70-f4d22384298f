package org.springblade.thingcom.warning.controller;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.thingcom.core.excel.handler.AutoColumnWidthHandler;
import org.springblade.thingcom.core.excel.handler.ExportHeaderCellHandler;
import org.springblade.thingcom.core.excel.utils.ThingcomExcelUtil;
import org.springblade.thingcom.device.entity.Tracker;
import org.springblade.thingcom.device.service.TrackerService;
import org.springblade.thingcom.device.vo.TrackerVO;
import org.springblade.thingcom.translate.utils.TranslateUtil;
import org.springblade.thingcom.warning.dto.RobotAlarmDTO;
import org.springblade.thingcom.warning.entity.RobotAlarm;
import org.springblade.thingcom.warning.enums.AlarmTypeEnum;
import org.springblade.thingcom.warning.excel.RobotAlarmExcel;
import org.springblade.thingcom.warning.excel.RobotAlarmTableExcel;
import org.springblade.thingcom.warning.service.RobotAlarmService;
import org.springblade.thingcom.warning.vo.RobotAlarmVO;
import org.springblade.thingcom.warning.wrapper.RobotAlarmWrapper;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 机器人实时告警控制层
 *
 * <AUTHOR>
 * @since 2024-01-20 13:58:33
 */
@AllArgsConstructor
@RestController
@RequestMapping("/robotAlarm")
public class RobotAlarmController extends BladeController {
    private final RobotAlarmService robotAlarmService;
    private final TrackerService trackerService;

    /**
     * 机器人实时告警分页查询
     *
     * @param query
     * @param dto
     * @return
     */
    @GetMapping("/page")
    public R<IPage<RobotAlarmVO>> page(Query query, RobotAlarmDTO dto) {
        IPage<RobotAlarmVO> page = robotAlarmService.page(Condition.getPage(query), dto);
        // 清除路径id对应的跟踪器列表
        Map<Long, List<Tracker>> trackerGroupMap = null;
        if (CollUtil.isNotEmpty(page.getRecords())) {
            List<Long> clearingPathIdList = page.getRecords().stream().map(RobotAlarmVO::getRobotClearingPathId).distinct()
                    .collect(Collectors.toList());
            List<TrackerVO> trackerVoList = trackerService.listTrackerBindByClearingPathIdList(clearingPathIdList);
            // trackerVoList 按照 clearingPathId 进行分组，clearingPathId 作为 key，trackerList 作为 value
            Map<Long, List<TrackerVO>> trackerVoGroupMap = trackerVoList.stream()
                    .collect(Collectors.groupingBy(TrackerVO::getClearingPathId));
            // trackerGroupMap 转为 Map<Long, List<Tracker>>
            trackerGroupMap = trackerVoGroupMap.entrySet().stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> entry.getValue().stream()
                                    .map(trackerVo -> BeanUtil.copy(trackerVo, Tracker.class))
                                    .collect(Collectors.toList())
                    ));
        }
        return data(RobotAlarmWrapper.build().pageVoExt(page, trackerGroupMap));
    }

    /**
     * 告警类型统计
     *
     * @return
     */
    @GetMapping("/statistics")
    public R<List<RobotAlarmVO>> alarmTypeStatistics(RobotAlarmDTO dto) {
        List<RobotAlarmVO> list = robotAlarmService.getAlarmType(dto);
        return data(list);
    }

    /**
     * 机器人分区统计
     *
     * @param statusAnalysisId
     * @return
     */
    @GetMapping("/regionalStatistics")
    public R<List<RobotAlarmVO>> regionalStatistics(Long statusAnalysisId) {
        List<RobotAlarmVO> list = robotAlarmService.getRegional(statusAnalysisId);
        return data(list);
    }

    /**
     * 机器人分区详情统计
     *
     * @param query
     * @param statusAnalysisId
     * @param robotRegionalNumber
     * @return
     */
    @GetMapping("/robotRegionalList")
    public R<IPage<RobotAlarmVO>> robotRegionalList(Query query, Long statusAnalysisId, String robotRegionalNumber) {
        IPage<RobotAlarmVO> list = robotAlarmService.getRegionalList(Condition.getPage(query), statusAnalysisId, robotRegionalNumber);
        return data(list);
    }

    /**
     * 导出卡片模式
     *
     * @param dto
     * @param response
     * @throws Exception
     */
    @GetMapping("/export-data-card")
    public void cardExportData(RobotAlarmDTO dto, HttpServletResponse response) throws Exception {
        LambdaQueryWrapper<RobotAlarm> wrapper = RobotAlarmWrapper.build().setQueryParam(dto);
        List<RobotAlarm> list = robotAlarmService.list(wrapper);
        String sheetName = "机器人告警卡片模式";
        // 判断是否需要翻译
        boolean translate = TranslateUtil.isTranslate(dto.getLang());
        // 表内数据常量翻译
        List<RobotAlarmExcel> data = RobotAlarmWrapper.build().cardListToExcel(list, translate);
        // 表头翻译
        List<List<String>> headerList = ThingcomExcelUtil.getHeadList(RobotAlarmExcel.class, translate);
        // translate为真，翻译
        if (translate) {
            // 表文件名的翻译
            sheetName = TranslateUtil.chineseToEnglish(sheetName);
        }
        ThingcomExcelUtil.setExportDataFile(response, sheetName);
        EasyExcel.write(response.getOutputStream(), RobotAlarmExcel.class)
                .inMemory(true)
                .registerWriteHandler(new AutoColumnWidthHandler())
                .head(headerList)
                .registerWriteHandler(new ExportHeaderCellHandler())
                .sheet(sheetName).doWrite(data);
    }

    /**
     * 导出表格模式
     *
     * @param dto
     * @param response
     * @throws Exception
     */
    @GetMapping("/export-data-table")
    public void tableExportData(RobotAlarmDTO dto, HttpServletResponse response) throws Exception {
        LambdaQueryWrapper<RobotAlarm> wrapper = RobotAlarmWrapper.build().setQueryParam(dto);
        List<RobotAlarm> list = robotAlarmService.list(wrapper);
        String sheetName = "机器人告警表格模式";
        // 判断是否需要翻译
        boolean translate = TranslateUtil.isTranslate(dto.getLang());
        // 表内数据常量翻译
        List<RobotAlarmTableExcel> data = RobotAlarmWrapper.build().tableListToExcel(list, translate);
        // 表头翻译
        List<List<String>> headerList = ThingcomExcelUtil.getHeadList(RobotAlarmTableExcel.class, translate);
        // translate为真，翻译
        if (translate) {
            // 表文件名的翻译
            sheetName = TranslateUtil.chineseToEnglish(sheetName);
        }
        ThingcomExcelUtil.setExportDataFile(response, sheetName);
        EasyExcel.write(response.getOutputStream(), RobotAlarmTableExcel.class)
                .inMemory(true)
                .registerWriteHandler(new AutoColumnWidthHandler())
                .head(headerList)
                .registerWriteHandler(new ExportHeaderCellHandler())
                .sheet(sheetName).doWrite(data);
    }

    /**
     * 告警类型下拉列表
     *
     * @return
     */
    @GetMapping("/select")
    public R getAlarmType() {
        return R.data(AlarmTypeEnum.toList());
    }
}

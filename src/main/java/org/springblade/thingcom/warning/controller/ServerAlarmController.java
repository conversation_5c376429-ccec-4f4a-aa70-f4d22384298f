package org.springblade.thingcom.warning.controller;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.support.Kv;
import org.springblade.thingcom.core.excel.handler.AutoColumnWidthHandler;
import org.springblade.thingcom.core.excel.utils.ThingcomExcelUtil;
import org.springblade.thingcom.operationMaintenance.service.ServerService;
import org.springblade.thingcom.translate.utils.TranslateUtil;
import org.springblade.thingcom.warning.dto.ServerAlarmDTO;
import org.springblade.thingcom.warning.entity.ServerAlarm;
import org.springblade.thingcom.warning.excel.ServerAlarmExcel;
import org.springblade.thingcom.warning.excel.ServerAlarmTableExcel;
import org.springblade.thingcom.warning.service.ServerAlarmService;
import org.springblade.thingcom.warning.vo.ServerAlarmVO;
import org.springblade.thingcom.warning.wrapper.ServerAlarmWrapper;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 服务器实时告警控制层
 *
 * <AUTHOR>
 * @since 2024-03-11 16:39:48
 */
@AllArgsConstructor
@RestController
@RequestMapping("/serverAlarm")
public class ServerAlarmController extends BladeController {
    private final ServerAlarmService serverAlarmService;
    private final ServerService serverService;

    /**
     * 服务器实时告警分页查询
     *
     * @param query
     * @param dto
     * @return
     */
    @GetMapping("/page")
    public R<IPage<ServerAlarmVO>> page(Query query, ServerAlarmDTO dto) {
        LambdaQueryWrapper<ServerAlarm> wrapper = ServerAlarmWrapper.build().setQueryParam(dto);
        IPage<ServerAlarm> iPage = serverAlarmService.page(Condition.getPage(query), wrapper);
        return data(ServerAlarmWrapper.build().pageVO(iPage));
    }

    /**
     * 导出卡片模式
     *
     * @param dto
     * @param response
     * @throws Exception
     */
    @GetMapping("/export-data-card")
    public void cardExportData(ServerAlarmDTO dto, HttpServletResponse response) throws Exception {
        LambdaQueryWrapper<ServerAlarm> wrapper = ServerAlarmWrapper.build().setQueryParam(dto);
        List<ServerAlarm> list = serverAlarmService.list(wrapper);
        // 判断是否需要翻译
        boolean translate = TranslateUtil.isTranslate(dto.getLang());
        List<ServerAlarmExcel> data = ServerAlarmWrapper.build().cardListToExcel(list, translate);
        String sheetName = "服务器告警卡片模式";
        ThingcomExcelUtil.setExportDataFile(response, "服务器告警卡片模式");
        EasyExcel.write(response.getOutputStream(), ServerAlarmExcel.class)
                .inMemory(true)
                .sheet(sheetName).doWrite(data);
    }

    /**
     * 导出表格模式
     *
     * @param dto
     * @param response
     * @throws Exception
     */
    @GetMapping("/export-data-table")
    public void tableExportData(ServerAlarmDTO dto, HttpServletResponse response) throws Exception {
        LambdaQueryWrapper<ServerAlarm> wrapper = ServerAlarmWrapper.build().setQueryParam(dto);
        List<ServerAlarm> list = serverAlarmService.list(wrapper);
        String sheetName = "服务器实时告警";
        // 判断是否需要翻译
        boolean translate = TranslateUtil.isTranslate(dto.getLang());
        // 表头翻译
        List<List<String>> headerList = ThingcomExcelUtil.getHeadList(ServerAlarmTableExcel.class, translate);
        // 表内数据翻译
        List<ServerAlarmTableExcel> data = ServerAlarmWrapper.build().tableListToExcel(list, translate);
        // translate为真，翻译
        if (translate) {
            // 表文件名的翻译
            sheetName = TranslateUtil.chineseToEnglish(sheetName);
        }
        ThingcomExcelUtil.setExportDataFile(response, sheetName);
        EasyExcel.write(response.getOutputStream(), ServerAlarmTableExcel.class)
                .inMemory(true)
                .registerWriteHandler(new AutoColumnWidthHandler())
                .head(headerList)
                .sheet(sheetName).doWrite(data);
    }

    /**
     * 服务器名称下拉列表
     *
     * @return
     */
    @GetMapping("/serverNameList")
    public R serverNameList() {
        return data(serverService.list().stream()
                .map(obj -> Kv.create().set("label", obj.getName()).set("value", obj.getId()))
                .collect(Collectors.toList()));
    }
}

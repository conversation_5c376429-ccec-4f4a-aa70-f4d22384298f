package org.springblade.thingcom.warning.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.thingcom.core.excel.handler.AutoColumnWidthHandler;
import org.springblade.thingcom.core.excel.utils.ThingcomExcelUtil;
import org.springblade.thingcom.operationMaintenance.entity.WeatherStation;
import org.springblade.thingcom.operationMaintenance.enums.WeatherStationTypeEnum;
import org.springblade.thingcom.operationMaintenance.service.WeatherStationService;
import org.springblade.thingcom.translate.utils.TranslateUtil;
import org.springblade.thingcom.warning.dto.WeatherStationAlarmDTO;
import org.springblade.thingcom.warning.entity.WeatherStationAlarm;
import org.springblade.thingcom.warning.excel.GpsGatewayAlarmExcel;
import org.springblade.thingcom.warning.excel.WeatherStationAlarmExcel;
import org.springblade.thingcom.warning.service.WeatherStationAlarmService;
import org.springblade.thingcom.warning.vo.WeatherStationAlarmVO;
import org.springblade.thingcom.warning.wrapper.WeatherStationAlarmWrapper;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Objects;

/**
 * 气象站实时告警控制层
 *
 * <AUTHOR>
 * @since 2024-03-20 10:30:56
 */
@AllArgsConstructor
@RestController
@RequestMapping("/weatherStationAlarm")
public class WeatherStationAlarmController extends BladeController {

    private final WeatherStationService weatherStationService;
    private final WeatherStationAlarmService weatherStationAlarmService;

    /**
     * 气象站实时告警分页查询
     *
     * @param query
     * @param dto
     * @return
     */
    @GetMapping("/page")
    public R<IPage<WeatherStationAlarmVO>> page(Query query, WeatherStationAlarmDTO dto) {
        LambdaQueryWrapper<WeatherStationAlarm> wrapper = setQueryParam(dto);
        IPage<WeatherStationAlarm> iPage = weatherStationAlarmService.page(Condition.getPage(query), wrapper);
        return data(WeatherStationAlarmWrapper.build().pageVO(iPage));
    }

    /**
     * 导出表格模式
     *
     * @param dto
     * @param response
     * @throws Exception
     */
    @GetMapping("/export-data-table")
    public void exportDataTable(WeatherStationAlarmDTO dto, HttpServletResponse response) throws Exception {
        // 气象站类型
        if (Objects.isNull(dto.getWeatherStationType())) {
            dto.setWeatherStationType(WeatherStationTypeEnum.THINGCOM.getValue());
        }
        LambdaQueryWrapper<WeatherStationAlarm> wrapper = setQueryParam(dto);
        List<WeatherStationAlarm> list = weatherStationAlarmService.list(wrapper);
        if (WeatherStationTypeEnum.THINGCOM.getValue().equals(dto.getWeatherStationType())) {
            String sheetName = "采集器告警";
            // 判断是否需要翻译
            boolean translate = TranslateUtil.isTranslate(dto.getLang());
            // 表内数据常量翻译
            List<GpsGatewayAlarmExcel> data = WeatherStationAlarmWrapper.build().listToExcel1(list, translate);
            // 表头翻译
            List<List<String>> headerList = ThingcomExcelUtil.getHeadList(GpsGatewayAlarmExcel.class, translate);
            // translate为真，翻译
            if (translate) {
                // 表文件名的翻译
                sheetName = TranslateUtil.chineseToEnglish(sheetName);
            }
            ThingcomExcelUtil.setExportDataFile(response, sheetName);
            EasyExcel.write(response.getOutputStream(), GpsGatewayAlarmExcel.class)
                    .inMemory(true)
                    .registerWriteHandler(new AutoColumnWidthHandler())
                    .head(headerList)
                    .sheet(sheetName).doWrite(data);
        } else {
            String sheetName = "气象站告警";
            // 判断是否需要翻译
            boolean translate = TranslateUtil.isTranslate(dto.getLang());
            // 表内数据常量翻译
            List<WeatherStationAlarmExcel> data = WeatherStationAlarmWrapper.build().listToExcel2(list, translate);
            // 表头翻译
            List<List<String>> headerList = ThingcomExcelUtil.getHeadList(WeatherStationAlarmExcel.class, translate);
            // translate为真，翻译
            if (translate) {
                // 表文件名的翻译
                sheetName = TranslateUtil.chineseToEnglish(sheetName);
            }
            ThingcomExcelUtil.setExportDataFile(response, sheetName);
            EasyExcel.write(response.getOutputStream(), WeatherStationAlarmExcel.class)
                    .inMemory(true)
                    .registerWriteHandler(new AutoColumnWidthHandler())
                    .head(headerList)
                    .sheet(sheetName).doWrite(data);
        }
    }

    /**
     * sql检索条件
     *
     * @param dto
     * @return
     */
    private LambdaQueryWrapper<WeatherStationAlarm> setQueryParam(WeatherStationAlarmDTO dto) {
        LambdaQueryWrapper<WeatherStationAlarm> lqw = new LambdaQueryWrapper<>();
        lqw.eq(Objects.nonNull(dto.getId()), WeatherStationAlarm::getId, dto.getId());
        lqw.in(Objects.nonNull(dto.getWeatherStationId()), WeatherStationAlarm::getWeatherStationId, dto.getWeatherStationId());
        lqw.eq(Objects.nonNull(dto.getAlarmType()), WeatherStationAlarm::getAlarmType, dto.getAlarmType());
        // 告警内容
        lqw.like(Func.isNotBlank(dto.getAlarmContent()), WeatherStationAlarm::getAlarmContent, dto.getAlarmContent());
        // 气象站类型，默认1
        if (Objects.isNull(dto.getWeatherStationType())) {
            dto.setWeatherStationType(WeatherStationTypeEnum.THINGCOM.getValue());
        }
        lqw.eq(WeatherStationAlarm::getWeatherStationType, dto.getWeatherStationType());
        // 起始日期
        if (Objects.nonNull(dto.getBeginDate()) && Objects.nonNull(dto.getEndDate())) {
            lqw.between(WeatherStationAlarm::getTime, dto.getBeginDate(), dto.getEndDate());
        }
        // 位置
        if (StrUtil.isNotBlank(dto.getLocation())) {
            List<WeatherStation> weatherStationList = weatherStationService.list(Wrappers.<WeatherStation>lambdaQuery()
                    .like(WeatherStation::getLocation, dto.getLocation()));
            if (CollUtil.isEmpty(weatherStationList)) {
                return lqw.eq(WeatherStationAlarm::getId, -1);
            } else {
                lqw.in(WeatherStationAlarm::getWeatherStationId, weatherStationList.stream().map(WeatherStation::getId).toArray());
            }
        }
        return lqw;
    }
}

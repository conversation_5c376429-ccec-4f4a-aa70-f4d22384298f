//package org.springblade.thingcom.base.controller;
//
//import cn.hutool.core.img.ImgUtil;
//import cn.hutool.core.lang.Assert;
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.baomidou.mybatisplus.core.metadata.IPage;
//import com.baomidou.mybatisplus.core.toolkit.Wrappers;
//import io.swagger.annotations.ApiOperation;
//import lombok.AllArgsConstructor;
//import org.springblade.common.cache.ParamCache;
//import org.springblade.common.constant.ParamConstant;
//import org.springblade.core.boot.ctrl.BladeController;
//import org.springblade.core.log.annotation.ApiLog;
//import org.springblade.core.mp.support.Condition;
//import org.springblade.core.mp.support.Query;
//import org.springblade.core.tool.api.R;
//import org.springblade.core.tool.utils.Func;
//import org.springblade.thingcom.base.dto.LicenseDTO;
//import org.springblade.thingcom.base.entity.License;
//import org.springblade.thingcom.base.service.LicenseService;
//import org.springblade.thingcom.base.vo.LicenseVO;
//import org.springblade.thingcom.base.wrapper.LicenseWrapper;
//import org.springblade.thingcom.core.imagecode.QrCodeUtil;
//import org.springframework.web.bind.annotation.*;
//import org.springframework.web.multipart.MultipartFile;
//
//import javax.imageio.ImageIO;
//import javax.validation.Valid;
//import java.awt.image.BufferedImage;
//import java.io.ByteArrayOutputStream;
//import java.io.Serializable;
//import java.util.Base64;
//import java.util.List;
//
///**
// * License控制层
// *
// * <AUTHOR>
// * @since 2023-12-29 18:41:20
// */
//@AllArgsConstructor
//@RestController
//@RequestMapping("/license")
//public class LicenseController extends BladeController {
//    private final LicenseService licenseService;
//
//    /**
//     * License分页查询
//     *
//     * @param query
//     * @param dto
//     * @return
//     */
//    @GetMapping("/page")
//    public R<IPage<LicenseVO>> page(Query query, LicenseDTO dto) {
//        LambdaQueryWrapper<License> wrapper = Wrappers.<License>lambdaQuery(dto).orderByDesc(License::getId);
//        IPage<License> iPage = licenseService.page(Condition.getPage(query), wrapper);
//        return data(LicenseWrapper.build().pageVO(iPage));
//    }
//
//    /**
//     * License列表查询
//     *
//     * @param dto
//     * @return
//     */
//    @GetMapping("/list")
//    public R<List<LicenseVO>> list(LicenseDTO dto) {
//        LambdaQueryWrapper<License> wrapper = Wrappers.<License>lambdaQuery(dto).orderByDesc(License::getId);
//        List<License> list = licenseService.list(wrapper);
//        return data(LicenseWrapper.build().listVO(list));
//    }
//
//    /**
//     * 查看License详情
//     *
//     * @param id
//     * @return
//     */
//    @GetMapping("/{id}")
//    public R<LicenseVO> show(@PathVariable Serializable id) {
//        License entity = licenseService.getById(id);
//        return data(LicenseWrapper.build().entityVO(entity));
//    }
//
//    /**
//     * 新增License
//     *
//     * @param entity
//     * @return
//     */
//    @ApiLog("新增License")
//    @PostMapping
//    public R save(@Valid @RequestBody License entity) {
//        // 检查数据合法性
//        licenseService.validateData(entity);
//        return status(licenseService.save(entity));
//    }
//
//    /**
//     * 修改License
//     *
//     * @param entity
//     * @return
//     */
//    @ApiLog("修改License")
//    @PutMapping
//    public R update(@Valid @RequestBody License entity) {
//        Assert.notNull(entity.getId(), "id不能为空");
//        // 检查数据合法性
//        licenseService.validateData(entity);
//        return status(licenseService.updateById(entity));
//    }
//
//    /**
//     * 删除License
//     *
//     * @param ids
//     * @return
//     */
//    @ApiLog("删除License")
//    @DeleteMapping("/{ids}")
//    public R delete(@PathVariable Serializable ids) {
//        return status(licenseService.removeBatchByIds(Func.toLongList(String.valueOf(ids))));
//    }
//
//    /**
//     * License 下载授权文件（二维码图片）
//     *
//     * @param id 授权文件id
//     * @return
//     */
//    @GetMapping("/download")
//    public R<LicenseVO> download(Long id) throws Exception {
//        // 二维码logo
//        String logoBase64 = ParamCache.getValue(ParamConstant.LOGO);
//        // 生成二维码
//        BufferedImage qrCode = QrCodeUtil.createQrCode(String.valueOf(id), ImgUtil.toImage(logoBase64));
//        ByteArrayOutputStream baos = new ByteArrayOutputStream();
//        ImageIO.write(qrCode, "png", baos);
//        byte[] imageBytes = baos.toByteArray();
//        // 生成 Base64 编码
//        String base64 = Base64.getEncoder().encodeToString(imageBytes);
//        //outputStream.write(Base64.getDecoder().decode(base64));
//        License license = licenseService.getById(id);
//        Assert.notNull(license, "授权文件不存在");
//        LicenseVO vo = new LicenseVO();
//        vo.setName(license.getName());
//        vo.setBase64String(base64);
//        return data(vo);
//
//        // 直接输出图片
//        //HttpServletResponse response
//        //response.setHeader("Content-Disposition", "attachment; filename=" + license.getName() + ".png");
//        //response.setContentType("image/png");
//        //OutputStream outputStream = response.getOutputStream();
//        //outputStream.write(imageBytes);
//        //outputStream.flush();
//        //outputStream.close();
//    }
//
//    /**
//     * License 解析授权文件（读取二维码图片内容）
//     *
//     * @param file
//     * @return
//     */
//    @PostMapping("/upload")
//    @ApiOperation(value = "解析授权文件", notes = "传入file")
//    public R upload(MultipartFile file) {
//        return data(licenseService.upload(file));
//    }
//}

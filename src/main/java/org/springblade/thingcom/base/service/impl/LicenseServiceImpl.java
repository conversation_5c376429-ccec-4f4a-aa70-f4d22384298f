//package org.springblade.thingcom.base.service.impl;
//
//import cn.hutool.core.lang.Assert;
//import cn.hutool.core.util.StrUtil;
//import com.baomidou.mybatisplus.core.toolkit.Wrappers;
//import lombok.AllArgsConstructor;
//import org.springblade.thingcom.base.entity.License;
//import org.springblade.thingcom.base.entity.LicenseRobot;
//import org.springblade.thingcom.base.enums.LicenseTypeEnum;
//import org.springblade.thingcom.base.mapper.LicenseMapper;
//import org.springblade.thingcom.base.service.LicenseRobotService;
//import org.springblade.thingcom.base.service.LicenseService;
//import org.springblade.thingcom.command.dto.LicenseAuthDTO;
//import org.springblade.thingcom.core.imagecode.QrCodeUtil;
//import org.springblade.thingcom.core.mybatisPlus.impl.BaseServiceImpl;
//import org.springblade.thingcom.device.entity.Robot;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//import org.springframework.web.multipart.MultipartFile;
//
//import javax.imageio.ImageIO;
//import java.awt.image.BufferedImage;
//import java.util.Objects;
//
///**
// * License服务实现类
// *
// * <AUTHOR>
// * @since 2023-12-29 18:41:20
// */
//@Service
//@AllArgsConstructor
//public class LicenseServiceImpl extends BaseServiceImpl<LicenseMapper, License> implements LicenseService {
//
//    private final LicenseRobotService licenseRobotService;
//
//    @Override
//    public void validateData(License entity) {
//        // 检查名称是否存在
//        //Long count = baseMapper.selectCount(Wrappers.<License>lambdaQuery()
//        //        .eq(License::getName, entity.getName())
//        //        // 编辑时，排除自己
//        //        .ne(entity.getId() != null, License::getId, entity.getId()));
//        //Assert.isTrue(count == 0, StrUtil.format("名称[{}]已经存在", entity.getName()));
//    }
//
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public boolean saveData(License entity) {
//        // 新增License
//        return save(entity);
//    }
//
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public boolean updateData(License entity) {
//        // 修改License
//        return updateById(entity);
//    }
//
//    @Override
//    public License upload(MultipartFile file) {
//        Assert.notNull(file, "请上传授权文件");
//        String QrCodeContentId = null;
//        try {
//            // 读取二维码图片内容
//            BufferedImage image = ImageIO.read(file.getInputStream());
//            QrCodeContentId = QrCodeUtil.decodeQrCode(image);
//            Assert.notBlank(QrCodeContentId, "请上传正确的授权文件");
//        } catch (Exception e) {
//            Assert.isTrue(false, "读取二维码图片内容失败");
//        }
//        // 根据二维码内容获取授权文件
//        License license = getById(QrCodeContentId);
//        Assert.notNull(license, "授权文件不存在");
//        return license;
//    }
//
//    @Override
//    public License getLicense(LicenseAuthDTO dto) {
//        // 获取授权文件信息
//        License license = upload(dto.getFile());
//        Assert.isTrue(dto.getAuthType().equals(license.getType()), "请选择正确的授权文件");
//        // 剩余授权数量不能为空
//        Assert.notNull(license.getAuthCount(), "剩余授权数量不能为空");
//        if (LicenseTypeEnum.TEMP_AUTH.getValue().equals(dto.getAuthType())) {
//            // 同一个机器人授权数量不能为空
//            Assert.notNull(license.getRobotAuthCount(), "同一个机器人授权数量不能为空");
//        }
//        return license;
//    }
//
//    @Override
//    public void validateRobotAuth(Robot robot, License license) {
//        // 授权类型
//        Integer licenseType = license.getType();
//        if (LicenseTypeEnum.TEMP_AUTH.getValue().equals(licenseType)) {
//            // 判断机器人授权数量是否超限
//            LicenseRobot licenseRobot = licenseRobotService.getOne(Wrappers.<LicenseRobot>lambdaQuery()
//                    .eq(LicenseRobot::getLicenseId, license.getId())
//                    .eq(LicenseRobot::getRobotId, robot.getId()));
//            if (Objects.nonNull(licenseRobot)) {
//                // 判断机器人授权数量是否超限
//                Assert.isTrue(license.getRobotAuthCount() >= licenseRobot.getTempLicenseAuthCount()
//                        , StrUtil.format("机器人编号{}授权数量超限", robot.getRobotNumber()));
//            }
//        }
//    }
//
//}

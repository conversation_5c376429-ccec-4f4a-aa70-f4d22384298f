//package org.springblade.thingcom.base.service;
//
//import org.springblade.thingcom.base.entity.License;
//import org.springblade.thingcom.command.dto.LicenseAuthDTO;
//import org.springblade.thingcom.core.mybatisPlus.IBaseService;
//import org.springblade.thingcom.device.entity.Robot;
//import org.springframework.web.multipart.MultipartFile;
//
///**
// * License服务接口
// *
// * <AUTHOR>
// * @since 2023-12-29 18:41:20
// */
//public interface LicenseService extends IBaseService<License> {
//
//    /**
//     * 检查数据合法性
//     *
//     * @param entity 实体对象
//     * @return
//     */
//    void validateData(License entity);
//
//    /**
//     * 新增License
//     *
//     * @param entity
//     * @return
//     */
//    boolean saveData(License entity);
//
//    /**
//     * 修改License
//     *
//     * @param entity
//     * @return
//     */
//    boolean updateData(License entity);
//
//    /**
//     * License 解析授权文件（读取二维码图片内容）
//     *
//     * @param file
//     * @return
//     */
//    License upload(MultipartFile file);
//
//    /**
//     * 获取 License
//     *
//     * @param dto
//     * @return
//     */
//    License getLicense(LicenseAuthDTO dto);
//
//    /**
//     * 校验机器人授权信息
//     *
//     * @param robot
//     * @param license
//     * @return null
//     */
//    void validateRobotAuth(Robot robot, License license);
//}

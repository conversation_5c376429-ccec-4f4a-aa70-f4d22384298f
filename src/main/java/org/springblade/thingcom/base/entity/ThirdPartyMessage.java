package org.springblade.thingcom.base.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springblade.thingcom.core.mybatisPlus.entity.BaseEntity;

import java.io.Serializable;

/**
 * 第三方对接信息实体类
 *
 * <AUTHOR>
 * @since 2024-07-25 11:35:53
 */
@Data
@TableName("tb_third_party_message")
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class ThirdPartyMessage extends BaseEntity<ThirdPartyMessage> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    private String name;

    /**
     * 通道类型  见字典表
     */
    private Integer channelType;

    /**
     * IP地址
     */
    private String ip;

    /**
     * 端口号
     */
    private Integer port;

    /**
     * 总数量
     */
    private Integer total;

    /**
     * 状态，1-在线；2-离线
     */
    private Integer status;
}

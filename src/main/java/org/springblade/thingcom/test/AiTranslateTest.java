package org.springblade.thingcom.test;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springblade.core.http.util.HttpUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName TranslateTest
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/3/24 11:42
 * @Version 1.0
 **/
public class AiTranslateTest {

    /**
     * Kimi 翻译
     *
     * @param text       待翻译文本
     * @param targetLang 目标语言
     * @return 翻译结果
     * {
     * "id": "chatcmpl-67e0fe3964dbc2f9a44c643d",
     * "object": "chat.completion",
     * "created": 1742798393,
     * "model": "moonshot-v1-8k",
     * "choices": [
     * {
     * "index": 0,
     * "message": {
     * "role": "assistant",
     * "content": "Hello, World!"
     * },
     * "finish_reason": "stop"
     * }
     * ],
     * "usage": {
     * "prompt_tokens": 24,
     * "completion_tokens": 5,
     * "total_tokens": 29
     * }
     * }
     */
    public static String kimiTranslate(String text, String targetLang) {
        String url = "https://api.moonshot.cn/v1/chat/completions";
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "API KEY");

        JSONArray messageJsonArray = new JSONArray();

        String content = String.format("将以下内容翻译成%s，保持专业术语准确:\\n%s", targetLang, text);
        JSONObject messageJsonObject = new JSONObject();
        messageJsonObject.put("role", "user");
        messageJsonObject.put("content", content);
        messageJsonArray.add(messageJsonObject);

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("model", "moonshot-v1-8k");
        jsonObject.put("messages", messageJsonArray);

        return HttpUtil.postJson(url, headers, jsonObject.toJSONString());
    }

    /**
     * DeepSeek 翻译
     *
     * @param text       待翻译文本
     * @param targetLang 目标语言
     * @return 翻译结果
     * {
     * "id": "a348ca37-2b09-4aa1-9a1a-3823dc92eb4a",
     * "object": "chat.completion",
     * "created": 1742800375,
     * "model": "deepseek-chat",
     * "choices": [
     * {
     * "index": 0,
     * "message": {
     * "role": "assistant",
     * "content": "Hello, World!"
     * },
     * "logprobs": null,
     * "finish_reason": "stop"
     * }
     * ],
     * "usage": {
     * "prompt_tokens": 20,
     * "completion_tokens": 4,
     * "total_tokens": 24,
     * "prompt_tokens_details": {
     * "cached_tokens": 0
     * },
     * "prompt_cache_hit_tokens": 0,
     * "prompt_cache_miss_tokens": 20
     * },
     * "system_fingerprint": "fp_3a5770e1b4_prod0225"
     * }
     */
    public static String deepSeekTranslate(String text, String targetLang) {
        String url = "https://api.deepseek.com/v1/chat/completions";
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer " + "API KEY");

        JSONArray messageJsonArray = new JSONArray();

        String content = String.format("将以下内容翻译成%s，保持专业术语准确:\\n%s", targetLang, text);
        JSONObject messageJsonObject = new JSONObject();
        messageJsonObject.put("role", "user");
        messageJsonObject.put("content", content);
        messageJsonArray.add(messageJsonObject);

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("model", "deepseek-chat");
        jsonObject.put("messages", messageJsonArray);
        jsonObject.put("temperature", 0.3);

        return HttpUtil.postJson(url, headers, jsonObject.toJSONString());
    }

    public static void main(String[] args) {
        String text = "你好，世界！";
        String targetLang = "English";
        //String kimiTranslate = kimiTranslate(text, targetLang);
        //System.out.println("Kimi 翻译结果：" + kimiTranslate);
        String deepSeekTranslate = deepSeekTranslate(text, targetLang);
        System.out.println("DeepSeek 翻译结果：" + deepSeekTranslate);
    }

}

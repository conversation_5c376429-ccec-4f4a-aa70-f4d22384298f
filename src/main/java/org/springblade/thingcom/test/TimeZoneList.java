package org.springblade.thingcom.test;

import java.util.TimeZone;

/**
 * @ClassName TimeZoneList
 * @Description: TODO
 * @Author: zgq
 * @Date: 2024/6/28 16:31
 * @Version: 1.0
 **/
public class TimeZoneList {

    public static void main(String[] args) {
        for (String timeZoneId : TimeZone.getAvailableIDs()) {
            //System.out.println(timeZoneId);
            System.out.println(timeZoneId + " - " + TimeZone.getTimeZone(timeZoneId).getDisplayName());
        }
        //System.out.println(ZoneId.systemDefault());
        //
        ////原始UTC时间字符串
        //String time = "2024-01-01 01:01:01";
        //// 定义日期时间格式
        //DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        //// 将字符串转换为LocalDateTime对象，假设这是UTC时间
        //LocalDateTime localDateTime = LocalDateTime.parse(time, formatter);
        //// 转换为UTC时区的ZonedDateTime
        //ZonedDateTime utcDateTime = localDateTime.atZone(ZoneId.of("UTC"));
        //// 指定目标时区 "America/New_York"
        //ZoneId newYorkZoneId = ZoneId.of("Asia/Shanghai");
        //// 将UTC时间转换为America/New_York时间
        //ZonedDateTime newYorkDateTime = utcDateTime.withZoneSameInstant(newYorkZoneId);
        //// 打印转换后的America/New_York时间
        //System.out.println("Asia/Shanghai 时间: " + newYorkDateTime.format(formatter));
    }

    //public static void main(String[] args) {
    //    // 定义一个包含主要时区ID的列表
    //    String[] mainTimeZones = {
    //            "GMT", "UTC",
    //            "America/New_York", "America/Los_Angeles",
    //            "Europe/London", "Europe/Paris",
    //            "Asia/Shanghai", "Asia/Tokyo",
    //            "Australia/Sydney"
    //    };
    //
    //    // 打印主要时区列表
    //    System.out.println("全球主要时区列表:");
    //    for (String timeZoneId : mainTimeZones) {
    //        System.out.println(timeZoneId + " - " + TimeZone.getTimeZone(timeZoneId).getDisplayName());
    //    }
    //}
}

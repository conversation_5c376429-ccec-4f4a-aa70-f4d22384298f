package org.springblade.thingcom.socket.service;

import org.springblade.thingcom.device.entity.RobotTransmit;

/**
 * @ClassName TcpSlaveService
 * @Description tcp从机服务接口
 * <AUTHOR>
 * @Date 2024/8/5 14:55
 * @Version 1.0
 **/
public interface TcpSlaveService {

    /**
     * 建立 socket 连接
     *
     * @return
     */
    void socketConnect(Integer port);

    /**
     * 建立 socket 连接
     *
     * @return
     */
    void socketConnect(RobotTransmit robotTransmit);

    /**
     * 关闭 socket 连接
     *
     * @return
     */
    void socketClose(Integer port);
}

package org.springblade.thingcom.socket.task;

import org.springblade.thingcom.socket.utils.TcpSlaveUtil;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * @ClassName TcpSlaveConnectTask
 * @Description tcp 从机连接任务
 * <AUTHOR>
 * @Date 2024/8/28 15:13
 * @Version 1.0
 **/
@Component
public class TcpSlaveConnectTask implements CommandLineRunner {

    @Override
    public void run(String... args) throws Exception {
        // 初始化 socket 连接
        new Thread(TcpSlaveUtil::initSocketConnect).start();
    }

}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.thingcom.socket.mapper.TcpTestMapper">
    <resultMap type="org.springblade.thingcom.socket.entity.TcpTest" id="ModbusTestMap">
        <result column="id" property="id"/>
        <result column="test_data" property="testData"/>
        <result column="creator_id" property="creatorId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_id" property="updateId"/>
        <result column="is_deleted" property="deleted"/>
    </resultMap>
    
</mapper>

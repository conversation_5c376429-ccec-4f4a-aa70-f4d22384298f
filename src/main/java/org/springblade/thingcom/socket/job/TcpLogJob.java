package org.springblade.thingcom.socket.job;

import org.springblade.common.utils.CommonUtil;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.thingcom.logs.service.DcsLogService;
import org.springblade.thingcom.logs.service.ThirdLogService;

import java.time.LocalDateTime;

/**
 * 通讯日志定时任务，每天1时执行一次，删除过期的日志数据
 *
 * <AUTHOR>
 * @since 2024-08-01 16:15
 */
public class TcpLogJob implements Runnable {

    private final static ThirdLogService thirdLogService;
    private final static DcsLogService dcsLogService;

    static {
        thirdLogService = SpringUtil.getBean(ThirdLogService.class);
        dcsLogService = SpringUtil.getBean(DcsLogService.class);
    }

    @Override
    public void run() {
        LocalDateTime time = CommonUtil.getZoneTime()
                .minusDays(180).withHour(0).withMinute(0).withSecond(0);
        // DCS日志保存最近七天的数据
        dcsLogService.deleteByTime(time);
        // 第三方日志保存最近七天的数据
        thirdLogService.deleteByTime(time);
    }
}
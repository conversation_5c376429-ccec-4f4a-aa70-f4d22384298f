package org.springblade.thingcom.socket.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springblade.thingcom.core.mybatisPlus.entity.BaseEntity;

import java.io.Serializable;

/**
 * tcp测试实体类
 *
 * <AUTHOR>
 * @since 2024-07-13 10:43:58
 */
@Data
@TableName("tb_modbus_test")
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class TcpTest extends BaseEntity<TcpTest> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
	 * 测试数据
	 */
    private String testData;

}

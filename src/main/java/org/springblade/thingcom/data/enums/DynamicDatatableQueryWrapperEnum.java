package org.springblade.thingcom.data.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 动态数据表查询条件枚举
 */
@Getter
@AllArgsConstructor
public enum DynamicDatatableQueryWrapperEnum {

    /**
     * DCS告警
     */
    DCS_ALARM(DynamicDatatableEnum.DCS_ALARM_LOG.getValue(), "", ""),

    /**
     * 网关告警
     */
    GATEWAY_ALARM(DynamicDatatableEnum.GATEWAY_ALARM_LOG.getValue(), "", ""),

    /**
     * 机器人告警
     */
    ROBOT_ALARM(DynamicDatatableEnum.ROBOT_ALARM_LOG.getValue()
		    , "alarm_type = #{alarmType} AND robot_regional_id = #{robotRegionalId} AND robot_id = #{robotId}" +
            " AND alarm_content LIKE CONCAT('%', #{alarmContent}, '%')" +
            " AND time BETWEEN #{beginDate} AND #{endDate}"
            , " ORDER BY time DESC"),

    /**
     * 机器人电池
     */
    ROBOT_BATTERY(DynamicDatatableEnum.ROBOT_BATTERY_LOG.getValue()
            , "robot_regional_id = #{robotRegionalId} AND robot_regional_number = #{robotRegionalNumber}" +
            " AND robot_id = #{robotId} AND robot_name = #{robotName}" +
            " AND clearing_path_number LIKE CONCAT('%', #{clearingPathNumber}, '%')" +
            " AND time BETWEEN #{beginDate} AND #{endDate}"
            , " ORDER BY time DESC"),

    /**
     * 机器人电机
     */
    ROBOT_MOTOR(DynamicDatatableEnum.ROBOT_MOTOR_LOG.getValue()
            , "robot_regional_id = #{robotRegionalId} AND robot_regional_number = #{robotRegionalNumber}" +
            " AND robot_id = #{robotId} AND robot_name = #{robotName}" +
            " AND clearing_path_number LIKE CONCAT('%', #{clearingPathNumber}, '%')" +
            " AND time BETWEEN #{beginDate} AND #{endDate}"
            , " ORDER BY time DESC"),

    /**
     * 机器人运行
     */
    ROBOT_RUN(DynamicDatatableEnum.ROBOT_RUN_LOG.getValue()
            , "type = #{type}" +
            " AND robot_id IN (#{robotIds})" +
            " AND time BETWEEN #{beginDate} AND #{endDate}"
            , " ORDER BY time DESC"),

    /**
     * 机器人清扫
     */
    ROBOT_CLEARING(DynamicDatatableEnum.ROBOT_CLEARING_LOG.getValue()
            , "robot_id = #{robotId}" +
            " AND time BETWEEN #{beginDate} AND #{endDate}"
            , " ORDER BY time DESC"),

    /**
     * 机器人报文日志
     */
    ROBOT_MESSAGE(DynamicDatatableEnum.ROBOT_MESSAGE_LOG.getValue()
            , "robot_id = #{robotId}" +
		    " AND content LIKE CONCAT('%', #{content}, '%')" +
            " AND time BETWEEN #{beginDate} AND #{endDate}"
            , " ORDER BY time DESC"),

    /**
     * 机器人操作日志
     */
    ROBOT_OPERATION(DynamicDatatableEnum.ROBOT_OPERATION_LOG.getValue()
            , "robot_regional_id = #{robotRegionalId}" +
            " AND robot_id = #{robotId}" +
            " AND create_by = #{createBy}" +
            " AND create_time BETWEEN #{beginDate} AND #{endDate}"
            , " ORDER BY create_time DESC"),

    /**
     * 机器人广播操作日志
     */
    ROBOT_BROADCAST_OPERATION(DynamicDatatableEnum.ROBOT_BROADCAST_OPERATION_LOG.getValue()
            , "robot_regional_id = #{robotRegionalId}" +
            " AND robot_id = #{robotId}" +
            " AND create_by = #{createBy}" +
            " AND create_time BETWEEN #{beginDate} AND #{endDate}"
            , " ORDER BY create_time DESC"),

    /**
     * 服务器告警
     */
    SERVER_ALARM(DynamicDatatableEnum.SERVER_ALARM_LOG.getValue()
            , "server_id = #{serverId}" +
            " AND time BETWEEN #{beginDate} AND #{endDate}"
            , " ORDER BY create_time DESC"),

    /**
     * 跟踪器告警
     */
    TRACKER_ALARM(DynamicDatatableEnum.TRACKER_ALARM_LOG.getValue(), "", ""),

    /**
     * 气象站告警
     */
    WEATHER_STATION_ALARM(DynamicDatatableEnum.WEATHER_STATION_ALARM_LOG.getValue(), "", ""),

    /**
     * 气象站
     */
    WEATHER_STATION(DynamicDatatableEnum.WEATHER_STATION_LOG.getValue()
            , "weather_station_id = #{weatherStationId}" +
            " AND weather_station_number LIKE CONCAT('%', #{weatherStationNumber}, '%')" +
            " AND time BETWEEN #{beginDate} AND #{endDate}"
            , " ORDER BY time DESC"),

    ;

    private final String value;
    private final String queryCondition;
    private final String orderBy;

    public static DynamicDatatableQueryWrapperEnum getEnumByValue(String value) {
        return Arrays.stream(DynamicDatatableQueryWrapperEnum.values())
                .filter(e -> e.getValue().equals(value)).findFirst().orElse(null);
    }
}
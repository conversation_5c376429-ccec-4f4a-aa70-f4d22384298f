package org.springblade.thingcom.data.consumer;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.springblade.common.cache.CacheNames;
import org.springblade.common.utils.CommonUtil;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.tool.utils.Func;
import org.springblade.thingcom.communication.constant.MonitorAliasConstant;
import org.springblade.thingcom.communication.constant.ParamConstant;
import org.springblade.thingcom.communication.constant.StreamIdConstant;
import org.springblade.thingcom.communication.enums.ControlSwitchEnum;
import org.springblade.thingcom.core.dynamicDatasource.service.DynamicDatatableService;
import org.springblade.thingcom.core.dynamicDatasource.utils.DataTimeScopeUtil;
import org.springblade.thingcom.core.rabbitMq.RabbitMqConfig;
import org.springblade.thingcom.core.rabbitMq.RabbitMqService;
import org.springblade.thingcom.data.cache.StatusAnalysisCache;
import org.springblade.thingcom.data.constant.RobotDataConstant;
import org.springblade.thingcom.data.constant.StatusAnalysisIdConstant;
import org.springblade.thingcom.data.entity.RealData;
import org.springblade.thingcom.data.entity.StatusAnalysis;
import org.springblade.thingcom.data.enums.DynamicDatatableEnum;
import org.springblade.thingcom.data.enums.StatusAnalysisTypeEnum;
import org.springblade.thingcom.data.utils.RobotDataUtil;
import org.springblade.thingcom.device.cache.ClearingPathCache;
import org.springblade.thingcom.device.cache.RobotDataCache;
import org.springblade.thingcom.device.cache.RobotRegionalCache;
import org.springblade.thingcom.device.entity.ClearingPath;
import org.springblade.thingcom.device.entity.Robot;
import org.springblade.thingcom.device.entity.RobotRegional;
import org.springblade.thingcom.device.enums.DeviceStatusEnum;
import org.springblade.thingcom.device.vo.RobotRunStatusRecordVO;
import org.springblade.thingcom.logs.entity.RobotRunLog;
import org.springblade.thingcom.logs.service.RobotRunLogService;
import org.springblade.thingcom.operationMaintenance.entity.RobotClearing;
import org.springblade.thingcom.operationMaintenance.service.RobotClearingLogService;
import org.springblade.thingcom.operationMaintenance.service.RobotClearingService;
import org.springblade.thingcom.operationMaintenance.service.RobotRunStatisService;
import org.springblade.thingcom.statistic.entity.RobotBattery;
import org.springblade.thingcom.statistic.entity.RobotBatteryLog;
import org.springblade.thingcom.statistic.entity.RobotMotor;
import org.springblade.thingcom.statistic.entity.RobotMotorLog;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @ClassName HistoryDataConsumer
 * @Description: 历史数据消费者（机器人）
 * @Author: zgq
 * @Date: 2024/4/23 19:49
 * @Version: 1.0
 **/
@Service
@AllArgsConstructor
public class HistoryDataConsumer {

    private final RabbitMqService rabbitMqService;
    private final RobotClearingService robotClearingService;
    private final RobotClearingLogService robotClearingLogService;
    private final RobotRunStatisService robotRunStatisService;
    private final DynamicDatatableService dynamicDatatableService;
    private final RobotRunLogService robotRunLogService;
    private final BladeRedis bladeRedis;

    /**
     * 历史数据消费者
     *
     * @param json
     */
    @DSTransactional
    @RabbitListener(queues = RabbitMqConfig.QUEUE_HISTORY_DATA, containerFactory = "simpleListenerFactory")
    public void consumer(JSONObject json) {
        Robot robot = json.getBean(RobotDataConstant.ROBOT, Robot.class);
        String streamId = json.getStr(RobotDataConstant.STREAM_ID);
        if (Objects.isNull(robot) || StrUtil.isBlank(streamId)) {
            return;
        }
        // 机器人上下线
        if (StreamIdConstant.ROBOT_ONLINE_AND_OFFLINE.equals(streamId)) {
            Integer value = json.getInt(RobotDataConstant.VALUE);
            saveRobotOnlineAndOffline(robot, value);
        }
        // 机器人运行基本数据
        if (StreamIdConstant.ROBOT_RUN.equals(streamId)) {
            saveRobotRun(robot, json);
        }
        // 机器人清扫记录数据
        if (StreamIdConstant.E9.equals(streamId)) {
            saveRobotClearing(robot, json);
        }
        // 电池基本数据
        if (StreamIdConstant.ROBOT_BATTERY.equals(streamId)) {
            RobotBattery robotBattery = json.getBean(RobotDataConstant.REAL_DATA, RobotBattery.class);
            saveRobotBattery(robot, robotBattery);
        }
        // 电机-电流基本数据
        if (StreamIdConstant.ROBOT_MOTOR.equals(streamId)) {
            List<RobotMotor> robotMotorList = json.getBeanList(RobotDataConstant.REAL_DATA, RobotMotor.class);
            saveRobotMotor(robot, robotMotorList);
        }
    }

    /**
     * 保存机器人上下线数据
     *
     * @param robot
     */
    private void saveRobotOnlineAndOffline(Robot robot, Integer value) {
        LocalDateTime time = CommonUtil.getZoneTime();
        RobotRunLog robotRunLog = new RobotRunLog();
        robotRunLog.setRobotId(robot.getId());
        robotRunLog.setRobotNamePrefix(robot.getNamePrefix());
        robotRunLog.setRobotNumber(robot.getRobotNumber());
        robotRunLog.setRobotName(robot.getName());
        robotRunLog.setTime(time);
        // 获取机器人状态解析
        StatusAnalysis statusAnalysis = null;
        List<StatusAnalysis> list = StatusAnalysisCache.getList(MonitorAliasConstant.F01);
        if (CollUtil.isNotEmpty(list)) {
            statusAnalysis = list.stream().filter(f -> f.getBit().equals(value)).findFirst().orElse(null);
        }
        if (Objects.nonNull(statusAnalysis)) {
            robotRunLog.setStatusAnalysisId(statusAnalysis.getId());
            robotRunLog.setType(statusAnalysis.getType());
            robotRunLog.setContent(statusAnalysis.getStatusName());
        }
        // 获取机器人电池数据
        RobotBattery robotBattery = RobotDataCache.getBatteryDataByRobotEui(robot.getEui());
        if (Objects.nonNull(robotBattery)) {
            // 电池状态、电池剩余容量、电池电压、电池电流、电池温度
            robotRunLog.setBatteryStatus(robotBattery.getStatus());
            robotRunLog.setBatteryRemainingCapacity(robotBattery.getRemainingCapacity());
            robotRunLog.setBatteryVoltage(robotBattery.getVoltage());
            robotRunLog.setBatteryElectricity(robotBattery.getElectricity());
            robotRunLog.setBatteryTemperature(robotBattery.getTemperature());
        }
        // 分库分表保存数据
        String tableNamePrefix = DynamicDatatableEnum.ROBOT_RUN_LOG.getValue();
        dynamicDatatableService.saveData(tableNamePrefix, time, BeanUtil.beanToMap(robotRunLog));
    }

    /**
     * 保存机器人运行基本数据
     *
     * @param robot
     * @param json
     */
    private void saveRobotRun(Robot robot, JSONObject json) {
        RealData realData = json.getBean(RobotDataConstant.REAL_DATA, RealData.class);
        if (Objects.isNull(realData) || Objects.isNull(realData.getMapData())) {
            return;
        }
        // fa 上次的数据
        String faValueLast = json.getStr(RobotDataConstant.FA_VALUE_LAST);
        LocalDateTime time = realData.getTime();
        Map<Object, Object> mapData = realData.getMapData();
        // 获取机器人电池数据
        RobotBattery robotBattery = RobotDataUtil.getBatteryBaseData(mapData);
        // FA
        List<RobotRunLog> faList = parseRobotRunData(robot, realData, MonitorAliasConstant.FA, robotBattery, faValueLast);
        // FC
        List<RobotRunLog> fcList = parseRobotRunData(robot, realData, MonitorAliasConstant.FC, robotBattery, faValueLast);
        // FD
        List<RobotRunLog> fdList = parseRobotRunData(robot, realData, MonitorAliasConstant.FD, robotBattery, faValueLast);
        // 告警列表
        List<RobotRunLog> alarmList = new ArrayList<>();
        // FD-预警
        if (CollUtil.isNotEmpty(fdList)) {
            alarmList.addAll(fdList);
        }
        // FC-告警
        if (CollUtil.isNotEmpty(fcList)) {
            alarmList.addAll(fcList);
        }
        // 告警数据列表（alarmList：不为空：新增告警数据；为空：解除所有的告警数据）
        saveAlarmData(robot, time, alarmList, robotBattery);
        // 保存清扫记录
        RobotRunStatusRecordVO statusVO = RobotDataUtil.parseRobotRunStatus(robot, realData);
        if (Objects.nonNull(statusVO)) {
            saveRobotClearing(robot, realData, statusVO);
        }
        // FA-运行日志
        if (CollUtil.isNotEmpty(faList)) {
            // 过滤 FA 数据
            faList = faListFilter(robot.getId(), faList, faValueLast);
            robotRunLogService.saveBatch(time, faList);
        }
    }

    /**
     * faList 过滤
     *
     * @param robotId     机器人 id
     * @param faList      fa 数据列表
     * @param faValueLast fa 上次上报的值
     * @return List<RobotRunLog>
     */
    private List<RobotRunLog> faListFilter(Long robotId, List<RobotRunLog> faList, String faValueLast) {
        List<RobotRunLog> newFaList = new ArrayList<>();
        // 过滤 1004 数据
        RobotRunLog autoRun = faList.stream().filter(f -> StatusAnalysisIdConstant.ID_1004.equals(f.getStatusAnalysisId()))
                .findFirst().orElse(null);
        if (Objects.nonNull(autoRun)) {
            newFaList.add(autoRun);
        }
        for (RobotRunLog robotRunLog : faList) {
            Long statusAnalysisId = robotRunLog.getStatusAnalysisId();
            // 前进、后退：（在：自动运行中的前提下，上报就新增）
            if (StatusAnalysisIdConstant.ID_1007.equals(statusAnalysisId)
                    || StatusAnalysisIdConstant.ID_1008.equals(statusAnalysisId)) {
                if (Objects.nonNull(autoRun)) {
                    newFaList.add(robotRunLog);
                }
            } else {
                // 自动运行中在上面已经新增过，因此在这里过滤掉
                if (StatusAnalysisIdConstant.ID_1004.equals(statusAnalysisId)) {
                    continue;
                }
                if (StatusAnalysisIdConstant.ID_1028.equals(statusAnalysisId)) {
                    // 平台不允许运行：F0/F1 设备请求运行时：服务端回复不允许运行的情况下，新增一条 FA 数据
                    Object deviceCanStartStatus = bladeRedis.hGet(CacheNames.ROBOT_NOT_START_STATUS, robotId);
                    if (Objects.nonNull(deviceCanStartStatus) && ControlSwitchEnum.CLOSE.getValue().equals(deviceCanStartStatus)) {
                        newFaList.add(robotRunLog);
                        bladeRedis.hSet(CacheNames.ROBOT_NOT_START_STATUS, robotId, ControlSwitchEnum.OPEN.getValue());
                    }
                } else if (StatusAnalysisIdConstant.ID_1029.equals(statusAnalysisId)) {
                    // 自动运行请求回复超时（变化就新增）
                    newFaList.add(robotRunLog);
                } else {
                    // 其他：（0变化1再新增）
                    if (StrUtil.isEmpty(faValueLast)) {
                        newFaList.add(robotRunLog);
                    } else {
                        int faStrLength = faValueLast.length();
                        // faValueLast 是一个 32 位的二进制字符串，bit31 ~ bit0，robotRunLog 中的 statusAnalysisId 对应 bit0 ~ bit31，如果对应的 bit 位是 0，则新增
                        String binaryStr = StrUtil.reverse(faValueLast);
                        int bitIndex = Func.toInt(statusAnalysisId, -1) - StatusAnalysisIdConstant.FA_INDEX;
                        if (bitIndex > 0 && faStrLength > bitIndex
                                && ControlSwitchEnum.CLOSE.getValue() == Func.toInt(binaryStr.charAt(bitIndex))) {
                            newFaList.add(robotRunLog);
                        }
                    }
                }
            }
        }
        return newFaList;
    }

    /**
     * 保存告警数据
     *
     * @param robot        机器人
     * @param time         时间
     * @param alarmList    告警列表
     * @param robotBattery 机器人电池数据
     */
    private void saveAlarmData(Robot robot, LocalDateTime time, List<RobotRunLog> alarmList, RobotBattery robotBattery) {
        JSONObject json = JSONUtil.createObj();
        json.set(RobotDataConstant.ROBOT, JSONUtil.toJsonStr(robot));
        json.set(RobotDataConstant.ROBOT_BATTERY, JSONUtil.toJsonStr(robotBattery));
        // 消息队列里面，直接存储：LocalDateTime 出现了异常，因此改为时间戳
        json.set(RobotDataConstant.TIMESTAMP, CommonUtil.localDateTimeToTimestamp(time));
        json.set(RobotDataConstant.REAL_DATA, JSONUtil.toJsonStr(alarmList));
        // 发送到告警数据队列
        String routingKey = RabbitMqConfig.KEY_REAL_ALARM;
        rabbitMqService.sendMessage(routingKey, json);
    }

    /**
     * 保存机器人清扫数据
     *
     * @param robot
     * @param robotBattery
     */
    private void saveRobotClearing(Robot robot, RealData realData, RobotRunStatusRecordVO statusVO) {
        Map<Object, Object> mapData = realData.getMapData();
        LocalDateTime time = RobotDataUtil.getClearingStatusTime(realData);
        // 获取机器人清扫状态
        Integer lastRunStatus = statusVO.getLastRunStatus();
        LocalDateTime lastRunStatusTime = statusVO.getLastRunStatusTime();
        Integer currentRunStatus = statusVO.getCurrentRunStatus();
        // 清扫失败
        if (DeviceStatusEnum.RUN_FAIL.getValue().equals(currentRunStatus)) {
            // 上一次运行状态非故障状态，更新清扫故障记录
            if (!DeviceStatusEnum.RUN_FAIL.getValue().equals(lastRunStatus)) {
                // 保存运行统计数据
                robotRunStatisService.saveOrUpdateData(robot.getId(), time, DeviceStatusEnum.RUN_FAIL.getValue());
            }
        } else if (ControlSwitchEnum.OPEN.getValue().equals(currentRunStatus)) {
            // 清扫开始，上一次运行状态为非清扫状态，新增清扫开始记录
            if (!ControlSwitchEnum.OPEN.getValue().equals(lastRunStatus)) {
                // 结束时间先赋值为：纪元时间（防止插入数据失败，查询时对纪元时间置空）
                LocalDateTime epochTime = RobotDataConstant.EPOCH_TIME;
                // 新增清扫开始记录
                robotClearingLogService.saveRobotClearingLog(robot, 1, time, epochTime, 0L);
            }
        } else if (ControlSwitchEnum.CLOSE.getValue().equals(currentRunStatus)) {
            // 清扫结束，上一次运行状态为清扫状态，更新清扫记录
            if (ControlSwitchEnum.OPEN.getValue().equals(lastRunStatus)) {
                // 工作时长
                Object workDurationObj = mapData.get(MonitorAliasConstant.WORK_DURATION);
                Long workDuration = Objects.isNull(workDurationObj) ? 0L : Long.parseLong(workDurationObj.toString());
                // 运行里程（圈数 * 轮子周长）
                Object currentRunLapsObj = mapData.get(MonitorAliasConstant.CURRENT_RUN_LAPS);
                Long mileage = Objects.isNull(currentRunLapsObj) ? 0L : Func.toLong(currentRunLapsObj, 0) * ParamConstant.WHEEL_PERIMETER;
                // 更新清扫结束记录
                robotClearingLogService.updateRobotClearingLog(robot, lastRunStatusTime, time, workDuration, mileage);
                // 更新时长，查询记录：不存在则新增，存在则更新
                RobotClearing robotClearing = robotClearingService.getOne(Wrappers.<RobotClearing>lambdaQuery()
                        .eq(RobotClearing::getRobotId, robot.getId()).last("limit 1"));
                if (Objects.isNull(robotClearing)) {
                    robotClearing = new RobotClearing();
                    robotClearing.setRobotId(robot.getId());
                    robotClearing.setWorkDurationTotal(workDuration);
                    robotClearing.setWorkDurationCurrentMonth(workDuration);
                    robotClearing.setMileageTotal(mileage);
                    robotClearing.setMileageCurrentMonth(mileage);
                    robotClearing.setTime(time);
                    robotClearing.setCreateTime(time);
                    // 新增数据
                    robotClearingService.save(robotClearing);
                } else {
                    // 总工作时长
                    Long workDurationRecord = robotClearing.getWorkDurationTotal();
                    workDurationRecord = Objects.isNull(workDurationRecord) ? 0L : workDurationRecord;
                    robotClearing.setWorkDurationTotal(workDurationRecord + workDuration);
                    // 总运行里程
                    Long mileageRecord = robotClearing.getMileageTotal();
                    mileageRecord = Objects.isNull(mileageRecord) ? 0L : mileageRecord;
                    robotClearing.setMileageTotal(mileageRecord + mileage);
                    // 记录时间的月份和当前时间的月份不是同一个月，需要更新：当月工作时长，当月运行里程 为 0，否则累加
                    if (Objects.nonNull(robotClearing.getTime())
                            && robotClearing.getTime().getMonthValue() != time.getMonthValue()) {
                        robotClearing.setWorkDurationCurrentMonth(0L);
                        robotClearing.setMileageCurrentMonth(0L);
                    } else {
                        Long workDurationCurrentMonth = robotClearing.getWorkDurationCurrentMonth();
                        workDurationCurrentMonth = Objects.isNull(workDurationCurrentMonth) ? 0L : workDurationCurrentMonth;
                        robotClearing.setWorkDurationCurrentMonth(workDurationCurrentMonth + workDuration);
                        Long mileageCurrentMonth = robotClearing.getMileageCurrentMonth();
                        mileageCurrentMonth = Objects.isNull(mileageCurrentMonth) ? 0L : mileageCurrentMonth;
                        robotClearing.setMileageCurrentMonth(mileageCurrentMonth + mileage);
                    }
                    // 时间
                    robotClearing.setTime(time);
                    // 修改数据
                    robotClearingService.updateById(robotClearing);
                }
                // 保存运行统计数据
                robotRunStatisService.saveOrUpdateData(robot.getId(), time, DeviceStatusEnum.RUN.getValue());
            }
        }
    }

    /**
     * 保存机器人清扫数据
     *
     * @param robot
     * @param robotBattery
     */
    private void saveRobotClearing(Robot robot, JSONObject json) {
        RealData realData = json.getBean(RobotDataConstant.REAL_DATA, RealData.class);
        if (Objects.isNull(realData) || Objects.isNull(realData.getMapData())) {
            return;
        }
        Map<Object, Object> mapData = realData.getMapData();
        // 从机数据缓存 key
        String slave = RobotDataConstant.SLAVE;
        // 清扫开始时间-日
        List<Object> clearingTimeDayList = null;
        Map clearingTimeDayMap = (Map) mapData.get(MonitorAliasConstant.CLEARING_TIME_DAY);
        if (Objects.nonNull(clearingTimeDayMap) && Objects.nonNull(clearingTimeDayMap.get(slave))) {
            clearingTimeDayList = JSONUtil.toList((JSONArray) clearingTimeDayMap.get(slave), Object.class);
        }
        // 清扫开始时间-时
        List<Object> clearingTimeHourList = null;
        Map clearingTimeHourMap = (Map) mapData.get(MonitorAliasConstant.CLEARING_TIME_HOUR);
        if (Objects.nonNull(clearingTimeHourMap) && Objects.nonNull(clearingTimeHourMap.get(slave))) {
            clearingTimeHourList = JSONUtil.toList((JSONArray) clearingTimeHourMap.get(slave), Object.class);
        }
        // 清扫开始时间-分
        List<Object> clearingTimeMinuteList = null;
        Map clearingTimeMinuteMap = (Map) mapData.get(MonitorAliasConstant.CLEARING_TIME_MINUTE);
        if (Objects.nonNull(clearingTimeMinuteMap) && Objects.nonNull(clearingTimeMinuteMap.get(slave))) {
            clearingTimeMinuteList = JSONUtil.toList((JSONArray) clearingTimeMinuteMap.get(slave), Object.class);
        }
        // 清扫开始时间-清扫工作时长
        List<Object> clearingDurationList = null;
        Map clearingDurationMap = (Map) mapData.get(MonitorAliasConstant.CLEARING_DURATION);
        if (Objects.nonNull(clearingDurationMap) && Objects.nonNull(clearingDurationMap.get(slave))) {
            clearingDurationList = JSONUtil.toList((JSONArray) clearingDurationMap.get(slave), Object.class);
        }
        if (CollUtil.isEmpty(clearingTimeDayList) || CollUtil.isEmpty(clearingTimeHourList)
                || CollUtil.isEmpty(clearingTimeMinuteList) || CollUtil.isEmpty(clearingDurationList)) {
            return;
        }
        int dayListSize = clearingTimeDayList.size();
        int hourListSize = clearingTimeHourList.size();
        int minuteListSize = clearingTimeMinuteList.size();
        int durationListSize = clearingDurationList.size();
        // 数量不一致，不处理
        if (dayListSize != hourListSize || dayListSize != minuteListSize || dayListSize != durationListSize) {
            return;
        }
        // 取出 N 组清扫记录的数据
        for (int i = 0; i < clearingTimeDayList.size(); i++) {
            int clearingNumber = i + 1;
            // 取出 6 组清扫记录的数据
            int clearingTimeDay = Func.toInt(clearingTimeDayList.get(i));
            int clearingTimeHour = Func.toInt(clearingTimeHourList.get(i));
            int clearingTimeMinute = Func.toInt(clearingTimeMinuteList.get(i));
            long clearingDuration = Func.toLong(clearingDurationList.get(i));
            // 值需要大于 0
            if (clearingTimeDay <= 0 || clearingTimeHour < 0 || clearingTimeMinute < 0 || clearingDuration <= 0) {
                continue;
            }
            LocalDateTime time = realData.getTime();
            // 上报的日期大于当前日期，取上个月的日期
            if (clearingTimeDay > time.getDayOfMonth()) {
                time = time.minusMonths(1);
            }
            // 计算清扫开始时间
            LocalDateTime clearingBeginTime = LocalDateTime.of(time.getYear(), time.getMonthValue(), clearingTimeDay
                    , clearingTimeHour, clearingTimeMinute);
            // 计算清扫结束时间
            LocalDateTime clearingEndTime = clearingBeginTime.plusSeconds(clearingDuration);
            // 根据清扫开始时间，查询数据库中是否有记录，有记录则忽略，无记录则新增
            String dbName = DataTimeScopeUtil.buildDbName(clearingBeginTime.getYear());
            String tableNamePrefix = DynamicDatatableEnum.ROBOT_CLEARING_LOG.getValue();
            String tableName = DataTimeScopeUtil.getHandleDataTableName(tableNamePrefix, clearingBeginTime.toLocalDate());
            // 判断表是否存在，不存在则新增
            tableName = dynamicDatatableService.getTable(dbName, tableName);
            if (StrUtil.isBlank(tableName)) {
                robotClearingLogService.saveRobotClearingLog(robot, clearingNumber, clearingBeginTime, clearingEndTime, clearingDuration);
            } else {
                // 构建数据库名称和表名称
                String dbNameAndTableName = DataTimeScopeUtil.buildDbNameAndTableName(dbName, tableName);
                LocalDateTime queryDataEndTime = LocalDateTime.of(time.getYear(), time.getMonthValue(), clearingTimeDay
                        , clearingTimeHour, clearingTimeMinute, 59);
                // 构建查询条件，根据机器人ID、开始时间、结束时间（根据：年月日时分 查询，因此使用 BETWEEN 00 AND 59）
                String queryWrapper = StrUtil.format("robot_id = {} AND time BETWEEN '{}' AND '{}' LIMIT 1"
                        , robot.getId(), clearingBeginTime, queryDataEndTime);
                // 查询数据是否存在，存在则忽略，无记录则新增
                Map<String, Object> one = dynamicDatatableService.getOne(dbNameAndTableName, queryWrapper);
                if (Objects.isNull(one)) {
                    robotClearingLogService.saveRobotClearingLog(robot, clearingNumber, clearingBeginTime, clearingEndTime, clearingDuration);
                }
            }
        }
    }

    /**
     * 解析机器人运行数据
     *
     * @param robot        机器人
     * @param realData     实时数据
     * @param statusParam  状态参数
     * @param robotBattery 机器人电池
     * @param faValueLast  FA 上次上报的值
     */
    private List<RobotRunLog> parseRobotRunData(Robot robot, RealData realData, String statusParam
            , RobotBattery robotBattery, String faValueLast) {
        Map<Object, Object> mapData = realData.getMapData();
        LocalDateTime time = realData.getTime();
        // 解析机器人运行数据
        List<RobotRunLog> robotRunLogList = new ArrayList<>();
        // 开（触发）
        String open = ControlSwitchEnum.OPEN.getValue().toString();
        // 关（解除）
        String close = ControlSwitchEnum.CLOSE.getValue().toString();
        Object statusParamObj = mapData.get(statusParam);
        if (Objects.nonNull(statusParamObj)) {
            // bit31 ~ bit0 反转 bit0 ~ bit31
            String binaryStr = StrUtil.reverse(statusParamObj.toString());
            // bit0 ~ bit31
            String[] binaryArray = StrUtil.split(binaryStr, 1);
            List<StatusAnalysis> statusParamList = StatusAnalysisCache.getList(statusParam);
            if (CollUtil.isEmpty(statusParamList)) {
                return robotRunLogList;
            }
            int statusParamListSize = statusParamList.size();
            // binaryArray 为 32 位，list 为 32 位，查询 binaryArray 中为 1 的索引对应的 list 中的状态解析
            for (int i = 0; i < binaryArray.length; i++) {
                if (i >= statusParamListSize) {
                    break;
                }
                String currentBinary = binaryArray[i];
                StatusAnalysis statusAnalysis = statusParamList.get(i);
                String content = statusAnalysis.getStatusName();
                boolean addFlag;
                // 自动运行请求回复超时，变化就新增
                if (StatusAnalysisIdConstant.ID_1029.equals(statusAnalysis.getId())) {
                    if (StrUtil.isEmpty(faValueLast)) {
                        addFlag = true;
                    } else {
                        String binaryStrLast = StrUtil.reverse(faValueLast);
                        if (i >= binaryStrLast.length()) {
                            continue;
                        }
                        String lastBinary = String.valueOf(binaryStrLast.charAt(i));
                        // 发生变化再新增
                        addFlag = !currentBinary.equals(lastBinary);
                    }
                    // 1-触发，0-恢复
                    if (close.equals(binaryArray[i])) {
                        content = content + ParamConstant.RECOVER;
                    }
                } else {
                    addFlag = open.equals(currentBinary);
                }
                if (addFlag) {
                    // 对于 其它类型，不保存
                    if (StatusAnalysisTypeEnum.OTHER.getValue().equals(statusAnalysis.getType())) {
                        continue;
                    }
                    RobotRunLog robotRunLog = new RobotRunLog();
                    robotRunLog.setRobotId(robot.getId());
                    robotRunLog.setRobotNamePrefix(robot.getNamePrefix());
                    robotRunLog.setRobotNumber(robot.getRobotNumber());
                    robotRunLog.setRobotName(robot.getName());
                    robotRunLog.setStatusAnalysisId(statusAnalysis.getId());
                    robotRunLog.setType(statusAnalysis.getType());
                    robotRunLog.setContent(content);
                    robotRunLog.setSolution(statusAnalysis.getSolution());
                    // 电池状态、电池剩余容量、电池电压、电池电流、电池温度
                    robotRunLog.setBatteryStatus(robotBattery.getStatus());
                    robotRunLog.setBatteryRemainingCapacity(robotBattery.getRemainingCapacity());
                    robotRunLog.setBatteryVoltage(robotBattery.getVoltage());
                    robotRunLog.setBatteryElectricity(robotBattery.getElectricity());
                    robotRunLog.setBatteryTemperature(robotBattery.getTemperature());
                    robotRunLog.setTime(time);
                    robotRunLogList.add(robotRunLog);
                }
            }
        }
        return robotRunLogList;
    }

    /**
     * 保存机器人电池基本数据
     *
     * @param robot
     * @param robotBattery
     */
    private void saveRobotBattery(Robot robot, RobotBattery robotBattery) {
        // 赋值历史数据
        RobotBatteryLog robotBatteryLog = BeanUtil.toBean(robotBattery, RobotBatteryLog.class);
        robotBatteryLog.setRobotId(robot.getId());
        robotBatteryLog.setRobotRegionalId(robot.getRobotRegionalId());
        // 获取机器人分区信息
        RobotRegional robotRegional = RobotRegionalCache.getById(robot.getRobotRegionalId());
        if (Objects.nonNull(robotRegional)) {
            robotBatteryLog.setRobotRegionalNumber(robotRegional.getRegionalNumber());
        }
        robotBatteryLog.setRobotName(robot.getName());
        robotBatteryLog.setRobotNumber(robot.getRobotNumber());
        // 获取清扫路径信息
        ClearingPath clearingPath = ClearingPathCache.getById(robot.getClearingPathId());
        if (Objects.nonNull(clearingPath)) {
            robotBatteryLog.setClearingPathNumber(clearingPath.getPathNumber());
        }
        LocalDateTime time = robotBattery.getTime();
        // 分库分表保存数据
        String tableNamePrefix = DynamicDatatableEnum.ROBOT_BATTERY_LOG.getValue();
        dynamicDatatableService.saveData(tableNamePrefix, time, BeanUtil.beanToMap(robotBatteryLog));
    }

    /**
     * 保存机器人电机基本数据
     *
     * @param robot
     * @param robotMotorList
     */
    private void saveRobotMotor(Robot robot, List<RobotMotor> robotMotorList) {
        for (RobotMotor robotMotor : robotMotorList) {
            // 赋值历史数据
            RobotMotorLog robotMotorLog = BeanUtil.toBean(robotMotor, RobotMotorLog.class);
            robotMotorLog.setRobotId(robot.getId());
            robotMotorLog.setRobotRegionalId(robot.getRobotRegionalId());
            // 获取机器人分区信息
            RobotRegional robotRegional = RobotRegionalCache.getById(robot.getRobotRegionalId());
            if (Objects.nonNull(robotRegional)) {
                robotMotorLog.setRobotRegionalNumber(robotRegional.getRegionalNumber());
            }
            robotMotorLog.setRobotName(robot.getName());
            robotMotorLog.setRobotNumber(robot.getRobotNumber());
            // 获取清扫路径信息
            ClearingPath clearingPath = ClearingPathCache.getById(robot.getClearingPathId());
            if (Objects.nonNull(clearingPath)) {
                robotMotorLog.setClearingPathNumber(clearingPath.getPathNumber());
            }
            LocalDateTime time = robotMotorLog.getTime();
            // 分库分表保存数据
            String tableNamePrefix = DynamicDatatableEnum.ROBOT_MOTOR_LOG.getValue();
            dynamicDatatableService.saveData(tableNamePrefix, time, BeanUtil.beanToMap(robotMotorLog));
        }
    }

}
package org.springblade.thingcom.data.constant;

import cn.hutool.core.collection.CollUtil;

import java.util.List;

/**
 * @ClassName StatusAnalysisIdConstant
 * @Description tb_status_analysis 表的 id 值，该表为协议表，不会修改
 * <AUTHOR>
 * @Date 2025/1/13 11:30
 * @Version 1.0
 **/
public interface StatusAnalysisIdConstant {

    /**
     * FA bit 位索引
     */
    Integer FA_INDEX = 1000;

    /**
     * 自动运行中(指清扫中)
     */
    Long ID_1004 = 1004L;

    /**
     * 前进
     */
    Long ID_1007 = 1007L;

    /**
     * 后退
     */
    Long ID_1008 = 1008L;

    /**
     * 平台不允许运行
     */
    Long ID_1028 = 1028L;

    /**
     * 自动运行请求回复超时
     */
    Long ID_1029 = 1029L;

    /**
     * 主电机上限故障
     */
    Long ID_3009 = 3009L;

    /**
     * 从电机上限故障
     */
    Long ID_3010 = 3010L;

    /**
     * 电机告警
     */
    List<Long> MOTOR_ALARM_STATUS_ANALYSIS_ID_LIST = CollUtil.toList(ID_3009, ID_3010);
}

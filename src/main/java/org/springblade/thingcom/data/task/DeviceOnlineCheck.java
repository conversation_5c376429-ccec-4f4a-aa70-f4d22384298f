//package org.springblade.thingcom.data.task;
//
//import cn.hutool.core.util.StrUtil;
//import com.baomidou.mybatisplus.core.toolkit.Wrappers;
//import lombok.extern.slf4j.Slf4j;
//import org.springblade.thingcom.core.scheduling.entity.ScheduledCron;
//import org.springblade.thingcom.core.scheduling.enums.ScheduledCronEnum;
//import org.springblade.thingcom.core.scheduling.service.ScheduledCronService;
//import org.springblade.thingcom.device.service.RobotService;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.scheduling.annotation.EnableScheduling;
//import org.springframework.scheduling.annotation.SchedulingConfigurer;
//import org.springframework.scheduling.config.ScheduledTaskRegistrar;
//import org.springframework.scheduling.support.CronTrigger;
//
//import javax.annotation.Resource;
//import java.util.Objects;
//
///**
// * @ClassName DeviceOnlineCheck
// * @Description: 设备在线检测
// * @Author: zgq
// * @Date: 2024/4/26 17:37
// * @Version: 1.0
// **/
//@Slf4j
//@Configuration
//@EnableScheduling
//public class DeviceOnlineCheck implements SchedulingConfigurer {
//
//    @Resource
//    private ScheduledCronService scheduledCronService;
//    @Resource
//    private RobotService robotService;
//
//    @Override
//    public void configureTasks(ScheduledTaskRegistrar scheduledTaskRegistrar) {
//        scheduledTaskRegistrar.addTriggerTask(() -> {
//            log.info("设备在线监测定时任务开始");
//            try {
//                // 异步定时操作
//                robotService.updateOfflineStatus();
//            } catch (Exception e) {
//                log.error("设备在线监测定时任务异常", e);
//            }
//            log.info("设备在线监测定时任务结束");
//        }, triggerContext -> {
//            // 获取定时任务执行周期
//            ScheduledCron scheduledCron = scheduledCronService.getOne(Wrappers.<ScheduledCron>lambdaQuery()
//                    .eq(ScheduledCron::getType, ScheduledCronEnum.DEVICE_ONLINE_CHECK.getValue()));
//            if (Objects.isNull(scheduledCron) || StrUtil.isBlank(scheduledCron.getCron())) {
//                return null;
//            }
//            String cron = scheduledCron.getCron();
//            // 定时任务触发,可修改定时任务的执行周期
//            CronTrigger trigger = new CronTrigger(cron);
//            return trigger.nextExecutionTime(triggerContext);
//        });
//    }
//}

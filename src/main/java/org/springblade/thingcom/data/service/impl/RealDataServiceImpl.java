package org.springblade.thingcom.data.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.springblade.common.cache.CacheNames;
import org.springblade.common.cache.ParamCache;
import org.springblade.common.utils.CommonUtil;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.thingcom.command.dto.MulticastGroupDataDTO;
import org.springblade.thingcom.command.enums.CommandEnum;
import org.springblade.thingcom.command.enums.OperationStatusEnum;
import org.springblade.thingcom.command.service.RobotCommandService;
import org.springblade.thingcom.communication.constant.MonitorAliasConstant;
import org.springblade.thingcom.communication.constant.ParamConstant;
import org.springblade.thingcom.communication.constant.StreamIdConstant;
import org.springblade.thingcom.communication.entity.Timer;
import org.springblade.thingcom.communication.enums.ControlSwitchEnum;
import org.springblade.thingcom.communication.utils.StreamIdUtil;
import org.springblade.thingcom.core.dynamicDatasource.service.DynamicDatatableService;
import org.springblade.thingcom.core.dynamicDatasource.utils.DataTimeScopeUtil;
import org.springblade.thingcom.core.rabbitMq.RabbitMqConfig;
import org.springblade.thingcom.core.rabbitMq.RabbitMqService;
import org.springblade.thingcom.core.websocket.WebSocketSendTypeEnum;
import org.springblade.thingcom.core.websocket.WebSocketService;
import org.springblade.thingcom.data.constant.RobotDataConstant;
import org.springblade.thingcom.data.entity.RealData;
import org.springblade.thingcom.data.enums.DynamicDatatableEnum;
import org.springblade.thingcom.data.enums.DynamicDatatableUpdateWrapperEnum;
import org.springblade.thingcom.data.service.RealDataService;
import org.springblade.thingcom.data.utils.RobotDataUtil;
import org.springblade.thingcom.device.entity.Robot;
import org.springblade.thingcom.device.enums.DeviceStatusEnum;
import org.springblade.thingcom.device.service.RobotService;
import org.springblade.thingcom.device.vo.RobotVO;
import org.springblade.thingcom.license.enums.LicenseStatusEnum;
import org.springblade.thingcom.license.utils.LicenseUtil;
import org.springblade.thingcom.statistic.entity.RobotBattery;
import org.springblade.thingcom.statistic.entity.RobotMotor;
import org.springblade.thingcom.statistic.service.RobotBatteryService;
import org.springblade.thingcom.statistic.service.RobotMotorService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 实时数据记录（分库）服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-09 10:48:58
 */
@Service
@AllArgsConstructor
public class RealDataServiceImpl implements RealDataService {

    private final BladeRedis bladeRedis;
    private final RabbitMqService rabbitMqService;
    private final RobotBatteryService robotBatteryService;
    private final RobotMotorService robotMotorService;
    private final RobotCommandService robotCommandService;
    private final WebSocketService webSocketService;
    private final RobotService robotService;
    private final DynamicDatatableService dynamicDatatableService;

    @Override
    public void saveRealData(Robot robot, RealData realData) {
        String robotEui = robot.getEui();
        String dataStreamId = realData.getDataStreamId();
        String controlStreamId = realData.getControlStreamId();
        LocalDateTime time = realData.getTime();
        // 获取上次的 fa 数据
        Object faValueLast = RobotDataUtil.getRealData(robotEui, MonitorAliasConstant.FA);
        Map<Object, Object> mapData = realData.getMapData();
        // redis缓存
        bladeRedis.hMset(CacheNames.ROBOT_REALDATA + robotEui, mapData);
        // 状态数据
        if (StreamIdUtil.isStatusData(dataStreamId)) {
            // 保存历史数据
            saveHistoryData(robot, StreamIdConstant.ROBOT_RUN, realData, faValueLast);
        }
        // 清扫记录数据
        if (StreamIdConstant.E9.equals(dataStreamId)) {
            // 保存历史数据
            saveHistoryData(robot, StreamIdConstant.E9, realData, faValueLast);
        }
        // 电池基本数据
        if (StreamIdUtil.isBatteryData(dataStreamId)) {
            RobotBattery robotBattery = saveOrUpdateRobotBattery(robot, time, mapData);
            // 保存历史数据，需要把 id 置空
            robotBattery.setId(null);
            // 保存历史数据
            saveHistoryData(robot, StreamIdConstant.ROBOT_BATTERY, robotBattery, faValueLast);
        }
        // 电机-电流基本数据（涉及单个和批量）
        if (StreamIdUtil.isElectricityData(dataStreamId) || StreamIdUtil.isElectricityDatas(dataStreamId)) {
            List<RobotMotor> robotMotorList = saveOrUpdateRobotMotor(robot, dataStreamId, time, mapData);
            if (CollUtil.isNotEmpty(robotMotorList)) {
                // 保存历史数据
                saveHistoryData(robot, StreamIdConstant.ROBOT_MOTOR, robotMotorList, faValueLast);
            }
        }
        // 定时器启动请求
        if (StreamIdConstant.F0.equals(dataStreamId)) {
            Integer licenseStatus = LicenseUtil.getLicenseStatus();
            if (LicenseStatusEnum.EFFECTIVE.getValue().equals(licenseStatus)) {
                // 获取帧计数器
                String frameCounterHex = StreamIdUtil.getFrameCounterByControlStreamId(controlStreamId);
                Object timerStartRequestValueObj = mapData.get(MonitorAliasConstant.TIMER_START_REQUEST);
                if (Objects.nonNull(timerStartRequestValueObj)) {
                    String timerStartRequestValueStr = timerStartRequestValueObj.toString();
                    // 定时信息编号、星期、时、分、运行次数，每个占用两个字符（16进制字符）
                    if (timerStartRequestValueStr.length() >= 10) {
                        // 定时器
                        org.springblade.thingcom.communication.entity.Timer timer = new Timer();
                        timer.setNumber(Integer.parseInt(timerStartRequestValueStr.substring(0, 2), 16));
                        timer.setWeek(Integer.parseInt(timerStartRequestValueStr.substring(2, 4), 16));
                        timer.setHour(Integer.parseInt(timerStartRequestValueStr.substring(4, 6), 16));
                        timer.setMinute(Integer.parseInt(timerStartRequestValueStr.substring(6, 8), 16));
                        robotCommandService.sendTimerStartRequest(robot, frameCounterHex, timer);
                    }
                }
            }
        }
        // 未启动原因上报
        if (StreamIdConstant.E7.equals(dataStreamId)) {
            Integer licenseStatus = LicenseUtil.getLicenseStatus();
            if (LicenseStatusEnum.EFFECTIVE.getValue().equals(licenseStatus)) {
                robotCommandService.robotNotStartReceive(robot);
            }
            String startExceptionCauses = ParamConstant.START_EXCEPTION_CAUSES;
            // 告警内容
            String alarmContent = RobotDataUtil.getStartExceptionAlarmContent(false, startExceptionCauses, mapData);
            // websocket 通知
            webSocketService.sendMoreMessage(WebSocketSendTypeEnum.PROMPTS.getLabel() + robot.getId(), alarmContent);
        }
    }

    @Override
    public void updateRobotData(Robot robot, String dataStreamId, String robotNumberPrefix, String robotNumber
            , RealData realData) {
        // 更新机器人数据
        if (StreamIdUtil.isUpdateRobotData(dataStreamId)) {
            JSONObject json = JSONUtil.createObj();
            json.set(RobotDataConstant.ROBOT, JSONUtil.toJsonStr(robot));
            json.set(RobotDataConstant.REAL_DATA, JSONUtil.toJsonStr(realData));
            // 发送到更新数据队列
            String routingKey = RabbitMqConfig.KEY_UPDATE_DATA;
            rabbitMqService.sendMessage(routingKey, json);
        }
        // 离线 → 上线
        if (DeviceStatusEnum.OFFLINE.getValue().equals(robot.getStatus())) {
            //排除：设备掉电的情况
            Map<Object, Object> mapData = realData.getMapData();
            Object fdObj = mapData.get(MonitorAliasConstant.FD);
            boolean devicePoweredOff = RobotDataUtil.parseDevicePoweredOff(fdObj);
            if (!devicePoweredOff) {
                JSONObject json = JSONUtil.createObj();
                json.set(RobotDataConstant.ROBOT, JSONUtil.toJsonStr(robot));
                json.set(RobotDataConstant.STREAM_ID, StreamIdConstant.ROBOT_ONLINE_AND_OFFLINE);
                json.set(RobotDataConstant.VALUE, ControlSwitchEnum.OPEN.getValue());
                // 发送到历史数据队列
                String routingKey = RabbitMqConfig.KEY_HISTORY_DATA;
                rabbitMqService.sendMessage(routingKey, json);
            }
        }
        // 机器人编号
        if (StreamIdUtil.isRobotNumber(dataStreamId)) {
            if (StreamIdConstant.E9.equals(dataStreamId)) {
                // 更新机器人编号前缀、编号
                robot.setRobotNumberPrefix(robotNumberPrefix);
                robot.setRobotNumber(robotNumber);
                robot.setName(StrUtil.concat(true, robot.getNamePrefix(), robotNumberPrefix, robotNumber));
                JSONObject json = JSONUtil.createObj();
                json.set(RobotDataConstant.ROBOT, JSONUtil.toJsonStr(robot));
                json.set(RobotDataConstant.REAL_DATA, JSONUtil.toJsonStr(realData));
                // 发送到更新数据队列
                String routingKey = RabbitMqConfig.KEY_UPDATE_DATA;
                rabbitMqService.sendMessage(routingKey, json);
            }
            // 机器人编号前缀为空，找一台不为空的机器人作为参考
            if (StrUtil.isEmpty(robotNumberPrefix)) {
                Robot robotNumberPrefixIsNotNullRobot = robotService.getOne(Wrappers.<Robot>lambdaQuery()
                        .isNotNull(Robot::getRobotNumberPrefix)
                        .orderByDesc(Robot::getId).last("limit 1"));
                robotNumberPrefix = Objects.isNull(robotNumberPrefixIsNotNullRobot)
                        ? null : robotNumberPrefixIsNotNullRobot.getRobotNumberPrefix();
            }
            // 机器人编号全称
            String robotFullNumber = StrUtil.concat(true, robotNumberPrefix, robotNumber);
            JSONObject json = JSONUtil.createObj();
            json.set(RobotDataConstant.ROBOT, JSONUtil.toJsonStr(robot));
            json.set(RobotDataConstant.STREAM_ID, dataStreamId);
            json.set(RobotDataConstant.VALUE, robotFullNumber);
            // 消息队列里面，直接存储：LocalDateTime 出现了异常，因此改为时间戳
            LocalDateTime time = realData.getTime();
            json.set(RobotDataConstant.TIMESTAMP, CommonUtil.localDateTimeToTimestamp(time));
            // 发送到告警数据队列
            String routingKey = RabbitMqConfig.KEY_REAL_ALARM;
            rabbitMqService.sendMessage(routingKey, json);
        }
    }

    @Override
    public void timerExceptionHandle(Robot robot, RealData realData) {
        // 记录告警
        JSONObject json = JSONUtil.createObj();
        json.set(RobotDataConstant.ROBOT, JSONUtil.toJsonStr(robot));
        json.set(RobotDataConstant.REAL_DATA, JSONUtil.toJsonStr(realData));
        json.set(RobotDataConstant.STREAM_ID, realData.getDataStreamId());
        json.set(RobotDataConstant.ALARM_STATUS, ControlSwitchEnum.OPEN.getValue());
        // 消息队列里面，直接存储：LocalDateTime 出现了异常，因此改为时间戳
        LocalDateTime time = realData.getTime();
        json.set(RobotDataConstant.TIMESTAMP, CommonUtil.localDateTimeToTimestamp(time));
        // 发送到告警数据队列
        String routingKey = RabbitMqConfig.KEY_REAL_ALARM;
        rabbitMqService.sendMessage(routingKey, json);
        // 发送指令
        robotCommandService.syncTimeSingleSendBroadCast(robot, CommandEnum.BROADCAST_PARAM.getId(), StreamIdConstant.A8);
    }

    @Override
    public void realDataWebSocketSend(Robot robot, String streamId, Map<Object, Object> mapData) {
        // 获取实时数据
        RobotVO realData = RobotDataUtil.getRealData(robot, streamId, mapData);
        Map<String, Object> data = new HashMap<>();
        data.put(WebSocketSendTypeEnum.ROBOT_REAL_DATA.getLabel(), realData);
        String msg = JsonUtil.toJson(data);
        // websocket 通知
        webSocketService.sendMoreMessage(WebSocketSendTypeEnum.COMMAND.getLabel(), msg);
    }

    @Override
    public void parseParamConfigValue(Long robotId, LocalDateTime time, String dataStreamId, String value) {
        if (StrUtil.isEmpty(value)) {
            return;
        }
        int length = value.length();
        if (StreamIdConstant.C0.equals(dataStreamId) || StreamIdConstant.E0.equals(dataStreamId)) {
            List<MulticastGroupDataDTO> dataList = new ArrayList<>();
            // Lora 广播设置参数配置
            if (length >= 6) {
                MulticastGroupDataDTO data = new MulticastGroupDataDTO();
                data.setDataStreamId(StreamIdConstant.A4);
                data.setValue(value.substring(0, 6));
                dataList.add(data);
            }
            // 停机位 广播设置参数配置
            if (length >= 20) {
                MulticastGroupDataDTO data = new MulticastGroupDataDTO();
                data.setDataStreamId(StreamIdConstant.A3);
                data.setValue(value.substring(18, 20));
                dataList.add(data);
            }
            // 白天防误扫 广播设置参数配置
            if (length >= 22) {
                MulticastGroupDataDTO data = new MulticastGroupDataDTO();
                data.setDataStreamId(StreamIdConstant.A6);
                data.setValue(value.substring(20, 22));
                dataList.add(data);
            }
            // 定时器 广播设置参数配置
            if (length >= 78) {
                MulticastGroupDataDTO data = new MulticastGroupDataDTO();
                data.setDataStreamId(StreamIdConstant.A2);
                data.setValue(value.substring(22, 78));
                dataList.add(data);
            }
            //updateMulticastGroupOperationLogStatus(robotId, time, dataList);
        } else if (StreamIdConstant.C1.equals(dataStreamId) || StreamIdConstant.E1.equals(dataStreamId)) {
            List<MulticastGroupDataDTO> dataList = new ArrayList<>();
            // 电机 广播设置参数配置
            if (length >= 46) {
                MulticastGroupDataDTO data = new MulticastGroupDataDTO();
                data.setDataStreamId(StreamIdConstant.A0);
                data.setValue(value.substring(0, 46));
                dataList.add(data);
            }
            // 电池 广播设置参数配置
            if (length >= 68) {
                MulticastGroupDataDTO data = new MulticastGroupDataDTO();
                data.setDataStreamId(StreamIdConstant.A1);
                data.setValue(value.substring(46, 68));
                dataList.add(data);
            }
            updateMulticastGroupOperationLogStatus(robotId, time, dataList);
        }
    }

    @Override
    public void parseAndCacheParamConfigValue(String eui, LocalDateTime localDateTime, String dataStreamId, String value) {
        if (StrUtil.isEmpty(value)) {
            return;
        }
        int length = value.length();
        long time = CommonUtil.localDateTimeToTimestamp(localDateTime);
        Map<Object, Object> map = new HashMap<>();
        if (StreamIdConstant.C0.equals(dataStreamId) || StreamIdConstant.E0.equals(dataStreamId)) {
            // Lora 广播设置参数配置
            if (length >= 6) {
                Dict dict = Dict.create();
                dict.set(RobotDataConstant.TIMESTAMP, time);
                dict.set(RobotDataConstant.CONTROL_DATA, value.substring(0, 6));
                map.put(CommandEnum.LORA.getId(), dict);
            }
            // 停机位 广播设置参数配置
            if (length >= 20) {
                Dict dict = Dict.create();
                dict.set(RobotDataConstant.TIMESTAMP, time);
                dict.set(RobotDataConstant.CONTROL_DATA, value.substring(18, 20));
                map.put(CommandEnum.STOP_BIT.getId(), dict);
            }
            // 白天防误扫 广播设置参数配置
            if (length >= 22) {
                Dict dict = Dict.create();
                dict.set(RobotDataConstant.TIMESTAMP, time);
                dict.set(RobotDataConstant.CONTROL_DATA, value.substring(20, 22));
                map.put(CommandEnum.DAYTIME_PREVENT_CLEAN.getId(), dict);
            }
            // 定时器 广播设置参数配置
            if (length >= 78) {
                Dict dict = Dict.create();
                dict.set(RobotDataConstant.TIMESTAMP, time);
                dict.set(RobotDataConstant.CONTROL_DATA, CommonUtil.parseTimerRunCount(value.substring(22, 78)));
                map.put(CommandEnum.TIMER.getId(), dict);
            }
            // 操作状态（解锁/锁定） 广播设置参数配置
            if (length >= 80) {
                Dict dict = Dict.create();
                dict.set(RobotDataConstant.TIMESTAMP, time);
                dict.set(RobotDataConstant.CONTROL_DATA, value.substring(78, 80));
                map.put(StreamIdConstant.B01, dict);
            }
        } else if (StreamIdConstant.C1.equals(dataStreamId) || StreamIdConstant.E1.equals(dataStreamId)) {
            // 电机 广播设置参数配置
            if (length >= 46) {
                Dict dict = Dict.create();
                dict.set(RobotDataConstant.TIMESTAMP, time);
                dict.set(RobotDataConstant.CONTROL_DATA, value.substring(0, 46));
                map.put(CommandEnum.CONTROL_BOARD.getId(), dict);
            }
            // 电池 广播设置参数配置
            if (length >= 68) {
                Dict dict = Dict.create();
                dict.set(RobotDataConstant.TIMESTAMP, time);
                dict.set(RobotDataConstant.CONTROL_DATA, value.substring(46, 68));
                map.put(CommandEnum.BATTERY.getId(), dict);
            }
            // 主板温度 广播设置参数配置
            if (length >= 76) {
                Dict dict = Dict.create();
                dict.set(RobotDataConstant.TIMESTAMP, time);
                dict.set(RobotDataConstant.CONTROL_DATA, value.substring(68, 76));
                map.put(CommandEnum.MAIN_BOARD_TEMPERATURE.getId(), dict);
            }
        }
        if (CollUtil.isNotEmpty(map)) {
            String cacheKey = StreamIdUtil.getMulticastGroupControlValidateDataCacheKey(eui);
            bladeRedis.hMset(cacheKey, map);
        }
    }

    @Override
    public void updateMulticastGroupOperationLogStatus(Long robotId, LocalDateTime time, List<MulticastGroupDataDTO> dataList) {
        if (CollUtil.isEmpty(dataList)) {
            return;
        }
        // 需要更新的 streamId 集合
        List<String> updateStreamIdList = new ArrayList<>();
        // 目标时间列表
        List<LocalDateTime> targetTimeList = new ArrayList<>();
        // 系统规定的时间间隔
        String systemIntervalValue = ParamCache.getValue(org.springblade.common.constant.ParamConstant.MULTICAST_GROUP_COMMAND_INTERVAL);
        long interval = Func.toLong(systemIntervalValue, 600000L);
        // 校验列表中所有的元素是否在规定的时间范围内
        for (MulticastGroupDataDTO source : dataList) {
            String dataStreamId = source.getDataStreamId();
            String streamId = StreamIdUtil.getStreamId(dataStreamId);
            String value = source.getValue();
            // 根据 streamId 查询是否有操作广播设置参数
            Object multicastGroupCommandObj = bladeRedis.hGet(CacheNames.MULTICAST_GROUP_TIME, streamId);
            if (Objects.isNull(multicastGroupCommandObj)) {
                continue;
            }
            MulticastGroupDataDTO target = JSONUtil.toBean(multicastGroupCommandObj.toString(), MulticastGroupDataDTO.class);
            LocalDateTime targetTime = LocalDateTime.parse(target.getTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            // 时间间隔
            long between = Duration.between(targetTime, time).toMillis();
            // 不在规定的时间范围内，则忽略
            if (between > interval) {
                continue;
            }
            targetTimeList.add(targetTime);
            // 判断值是否相同，相同则更新，不相同则忽略
            if (StrUtil.isAllNotEmpty(value, target.getValue())) {
                // 定时器，只判断星期、时、分，不判断运行次数（因为下发的 1，设备端会自动加 1）
                if (CommandEnum.TIMER.getId().equals(streamId)) {
                    String targetValue = CommonUtil.parseTimerRunCount(target.getValue());
                    if (Objects.equals(value, targetValue)) {
                        updateStreamIdList.add(streamId);
                    }
                } else {
                    if (Objects.equals(value, target.getValue())) {
                        updateStreamIdList.add(streamId);
                    }
                }
            } else {
                // 针对没有值的监控点，比如：锁机、解锁，时间在规定的时间范围内，则更新
                updateStreamIdList.add(streamId);
            }
        }
        if (CollUtil.isEmpty(updateStreamIdList)) {
            return;
        }
        // streamId 条件，IN ('streamId1','streamId2'...)
        String streamIdIn = updateStreamIdList.stream().map(item -> "'" + item + "'").collect(Collectors.joining(","));
        // 开始时间、结束时间
        LocalDateTime beginTime = Collections.min(targetTimeList);
        LocalDateTime endTime = time;
        // 判断 beginTime 和 endTime 是否是同一个时间段，不是需要更新两张表，是只需更新一张表
        boolean isSameTimePeriod = DataTimeScopeUtil.isSameTimePeriod(beginTime, endTime);
        // 更新机器人操作日志表
        if (isSameTimePeriod) {
            LocalDateTime timeIndex = beginTime;
            updateRobotBroadcastOperationLog(robotId, streamIdIn, timeIndex, beginTime, endTime);
        } else {
            // 更新第一张表
            LocalDateTime timeIndex = beginTime;
            updateRobotBroadcastOperationLog(robotId, streamIdIn, timeIndex, beginTime, endTime);
            // 更新第二张表
            timeIndex = endTime;
            updateRobotBroadcastOperationLog(robotId, streamIdIn, timeIndex, beginTime, endTime);
        }
    }

    @Override
    public void updateRobotBroadcastOperationLog(Long robotId, String streamId, Integer status
            , LocalDateTime timeIndex, LocalDateTime beginTime, LocalDateTime endTime) {
        // 条件状态
        int conditionStatus = OperationStatusEnum.DEVICE_PROCESSING.getValue();
        // 根据时间获取数据库名称
        String dbName = DataTimeScopeUtil.buildDbName(timeIndex.getYear());
        String tableNamePrefix = DynamicDatatableEnum.ROBOT_BROADCAST_OPERATION_LOG.getValue();
        String tableName = DataTimeScopeUtil.getHandleDataTableName(tableNamePrefix, timeIndex.toLocalDate());
        // 判断表是否存在，存在则处理
        tableName = dynamicDatatableService.getTable(dbName, tableName);
        if (StrUtil.isEmpty(tableName)) {
            return;
        }
        streamId = "'" + streamId + "'";
        // 构建数据库名称和表名称
        String dbNameAndTableName = DataTimeScopeUtil.buildDbNameAndTableName(dbName, tableName);
        String beginTimeStr = beginTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        String endTimeStr = endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        // 处理条件
        String sqlWrapper = DynamicDatatableUpdateWrapperEnum.ROBOT_BROADCAST_OPERATION.getSqlWrapper();
        sqlWrapper = StrUtil.format(sqlWrapper, robotId, streamId, beginTimeStr, endTimeStr, conditionStatus);
        // 更新机器人操作日志表
        dynamicDatatableService.updateRobotBroadcastOperationLog(dbNameAndTableName, status, sqlWrapper);
    }

    /**
     * 保存历史数据
     *
     * @param robot       机器人
     * @param streamId    监控点ID
     * @param realData    实时数据
     * @param faValueLast fa 监控点上次值
     */
    private void saveHistoryData(Robot robot, String streamId, Object realData, Object faValueLast) {
        JSONObject json = JSONUtil.createObj();
        json.set(RobotDataConstant.ROBOT, JSONUtil.toJsonStr(robot));
        json.set(RobotDataConstant.STREAM_ID, streamId);
        json.set(RobotDataConstant.REAL_DATA, JSONUtil.toJsonStr(realData));
        json.set(RobotDataConstant.FA_VALUE_LAST, faValueLast);
        // 发送到历史数据队列
        String routingKey = RabbitMqConfig.KEY_HISTORY_DATA;
        rabbitMqService.sendMessage(routingKey, json);
    }

    /**
     * 保存或更新机器人电池数据
     *
     * @param robot   机器人
     * @param time    时间
     * @param mapData 数据
     */
    private RobotBattery saveOrUpdateRobotBattery(Robot robot, LocalDateTime time, Map<Object, Object> mapData) {
        RobotBattery robotBatteryData = RobotDataUtil.getBatteryBaseData(mapData);
        // 机器人电池实时数据表
        RobotBattery robotBattery = robotBatteryService.getOne(Wrappers.<RobotBattery>lambdaQuery()
                .eq(RobotBattery::getRobotId, robot.getId()).last("limit 1"));
        if (Objects.isNull(robotBattery)) {
            robotBattery = new RobotBattery();
            robotBattery.setRobotId(robot.getId());
        }
        // 保存实时数据
        robotBattery.setRemainingCapacity(robotBatteryData.getRemainingCapacity());
        robotBattery.setVoltage(robotBatteryData.getVoltage());
        robotBattery.setElectricity(robotBatteryData.getElectricity());
        robotBattery.setTemperature(robotBatteryData.getTemperature());
        robotBattery.setStatus(robotBatteryData.getStatus());
        robotBattery.setTime(time);
        robotBatteryService.saveOrUpdate(robotBattery);
        return robotBattery;
    }

    /**
     * 保存或更新机器人电机数据（主要针对电机-电流）
     *
     * @param robot    机器人
     * @param streamId 监控点ID
     * @param time     时间
     * @param mapData  数据
     */
    private List<RobotMotor> saveOrUpdateRobotMotor(Robot robot, String streamId
            , LocalDateTime time, Map<Object, Object> mapData) {
        Long robotId = robot.getId();
        List<RobotMotor> robotMotorList = new ArrayList<>();
        // 主电机电流
        BigDecimal masterMotorElectricity = null;
        // 主电机电流列表
        List<String> masterMotorElectricityList = null;
        // 从电机电流
        BigDecimal slaveMotorElectricity = null;
        // 从电机电流列表
        List<String> slaveMotorElectricityList = null;
        // 批量，取最新的一条数据作为实时数据（历史数据，则保存列表中的所有数据）
        if (StreamIdUtil.isElectricityDatas(streamId)) {
            Object masterMotorElectricityObj = mapData.get(MonitorAliasConstant.MASTER_MOTOR_ELECTRICITYS);
            if (Objects.nonNull(masterMotorElectricityObj)) {
                // masterMotorElectricityObj 为 map <key, list>
                Map<String, ArrayList> masterMotorElectricityMap = (Map<String, ArrayList>) masterMotorElectricityObj;
                masterMotorElectricityList = masterMotorElectricityMap.get(RobotDataConstant.SLAVE);
                if (CollUtil.isNotEmpty(masterMotorElectricityList)) {
                    // 过滤掉空值（无效值）
                    masterMotorElectricityList = masterMotorElectricityList.stream()
                            .filter(StrUtil::isNotBlank)
                            .collect(Collectors.toList());
                    // 取最新的一条数据作为实时数据
                    if (CollUtil.isNotEmpty(masterMotorElectricityList)) {
                        masterMotorElectricity = new BigDecimal(masterMotorElectricityList.get(0));
                        // 更新缓存中单个电流值
                        bladeRedis.hSet(CacheNames.ROBOT_REALDATA + robot.getEui()
                                , MonitorAliasConstant.MASTER_MOTOR_ELECTRICITY, masterMotorElectricity);
                    }
                }
            }
            Object slaveMotorElectricityObj = mapData.get(MonitorAliasConstant.SLAVE_MOTOR_ELECTRICITYS);
            if (Objects.nonNull(slaveMotorElectricityObj)) {
                // slaveMotorElectricityObj 为 map <key, list>
                Map<String, ArrayList> slaveMotorElectricityMap = (Map<String, ArrayList>) slaveMotorElectricityObj;
                slaveMotorElectricityList = slaveMotorElectricityMap.get(RobotDataConstant.SLAVE);
                if (CollUtil.isNotEmpty(slaveMotorElectricityList)) {
                    // 过滤掉空值、无效值
                    slaveMotorElectricityList = slaveMotorElectricityList.stream()
                            .filter(StrUtil::isNotBlank)
                            .collect(Collectors.toList());
                    // 取最新的一条数据作为实时数据
                    if (CollUtil.isNotEmpty(slaveMotorElectricityList)) {
                        slaveMotorElectricity = new BigDecimal(slaveMotorElectricityList.get(0));
                        // 更新缓存中单个电流值
                        bladeRedis.hSet(CacheNames.ROBOT_REALDATA + robot.getEui()
                                , MonitorAliasConstant.SLAVE_MOTOR_ELECTRICITY, slaveMotorElectricity);
                    }
                }
            }
        } else {
            // 单个
            Object masterMotorElectricityObj = mapData.get(MonitorAliasConstant.MASTER_MOTOR_ELECTRICITY);
            if (Objects.nonNull(masterMotorElectricityObj)) {
                masterMotorElectricity = new BigDecimal(masterMotorElectricityObj.toString());
            }
            Object slaveMotorElectricityObj = mapData.get(MonitorAliasConstant.SLAVE_MOTOR_ELECTRICITY);
            if (Objects.nonNull(slaveMotorElectricityObj)) {
                slaveMotorElectricity = new BigDecimal(slaveMotorElectricityObj.toString());
            }
        }
        // 主从机电流都为无效值，则跳过
        if (Objects.isNull(masterMotorElectricity) && Objects.isNull(slaveMotorElectricity)) {
            return null;
        }
        RobotMotor robotMotor = robotMotorService.getOne(Wrappers.<RobotMotor>lambdaQuery()
                .eq(RobotMotor::getRobotId, robotId).last("limit 1"));
        if (Objects.isNull(robotMotor)) {
            robotMotor = new RobotMotor();
            robotMotor.setRobotId(robotId);
        }
        // 主电机电流，非无效值才赋值
        if (Objects.nonNull(masterMotorElectricity)) {
            robotMotor.setMasterElectricity(masterMotorElectricity);
        }
        // 从电机电流，非无效值才赋值
        if (Objects.nonNull(slaveMotorElectricity)) {
            robotMotor.setSlaveElectricity(slaveMotorElectricity);
        }
        robotMotor.setTime(time);
        // 保存或更新电机实时数据表
        robotMotorService.saveOrUpdate(robotMotor);
        // 返回列表数据，用于保存历史数据
        if (StreamIdUtil.isElectricityDatas(streamId)) {
            if (CollUtil.isNotEmpty(masterMotorElectricityList) && CollUtil.isNotEmpty(slaveMotorElectricityList)) {
                // 电流数据长度
                int electricityDataSize = ParamConstant.ELECTRICITY_DATA_SIZE;
                // 电流数据时间间隔（默认电流数据长度，10 秒）
                Object electricityDataIntervalObj = mapData.getOrDefault(MonitorAliasConstant.ELECTRICITY_DATA_INTERVAL
                        , electricityDataSize);
                int electricityDataInterval = Func.toInt(electricityDataIntervalObj) / electricityDataSize;
                // 主从机电流长度
                int masterSize = masterMotorElectricityList.size();
                int slaveSize = slaveMotorElectricityList.size();
                for (int i = 0; i < masterSize; i++) {
                    // 主从机电流
                    String masterElectricityStr = masterMotorElectricityList.get(i);
                    String slaveElectricityStr = null;
                    if (i < slaveSize) {
                        slaveElectricityStr = slaveMotorElectricityList.get(i);
                    }
                    // 主从机电流 Bigdecimal，为空视为无效值
                    masterMotorElectricity = StrUtil.isEmpty(masterElectricityStr)
                            ? masterMotorElectricity : new BigDecimal(masterElectricityStr);
                    slaveMotorElectricity = StrUtil.isEmpty(slaveElectricityStr)
                            ? slaveMotorElectricity : new BigDecimal(slaveElectricityStr);
                    // 主从机电流都为无效值，则跳过
                    if (Objects.isNull(masterMotorElectricity) && Objects.isNull(slaveMotorElectricity)) {
                        continue;
                    }
                    robotMotor = new RobotMotor();
                    robotMotor.setRobotId(robotId);
                    // 主电机电流，非空才赋值
                    if (Objects.nonNull(masterMotorElectricity)) {
                        robotMotor.setMasterElectricity(masterMotorElectricity);
                    }
                    // 从电机电流，非空才赋值
                    if (Objects.nonNull(slaveMotorElectricity)) {
                        robotMotor.setSlaveElectricity(slaveMotorElectricity);
                    }
                    // time 往前推 i * electricityDataInterval 秒，第一个为最新的数据，最后一个为最早的数据
                    robotMotor.setTime(time.minusSeconds((long) i * electricityDataInterval));
                    robotMotor.setCreateTime(robotMotor.getTime());
                    robotMotorList.add(robotMotor);
                }
            }
        } else {
            // 保存历史数据，需要把 id 置空
            robotMotor.setId(null);
            robotMotorList.add(robotMotor);
        }
        return robotMotorList;
    }

    /**
     * 更新机器人操作日志表
     *
     * @param robotId    机器人ID
     * @param streamIdIn 监控点ID列表，IN ('streamId1','streamId2'...)
     * @param timeIndex  时间索引
     * @param beginTime  开始时间
     * @param endTime    结束时间
     */
    private void updateRobotBroadcastOperationLog(Long robotId, String streamIdIn, LocalDateTime timeIndex
            , LocalDateTime beginTime, LocalDateTime endTime) {
        // 操作状态，更新失败的记录为成功
        int successValue = OperationStatusEnum.SUCCESS.getValue();
        int failValue = OperationStatusEnum.FAIL.getValue();
        // 根据时间获取数据库名称
        String dbName = DataTimeScopeUtil.buildDbName(timeIndex.getYear());
        String tableNamePrefix = DynamicDatatableEnum.ROBOT_BROADCAST_OPERATION_LOG.getValue();
        String tableName = DataTimeScopeUtil.getHandleDataTableName(tableNamePrefix, timeIndex.toLocalDate());
        // 判断表是否存在，存在则处理
        tableName = dynamicDatatableService.getTable(dbName, tableName);
        if (StrUtil.isEmpty(tableName)) {
            return;
        }
        // 构建数据库名称和表名称
        String dbNameAndTableName = DataTimeScopeUtil.buildDbNameAndTableName(dbName, tableName);
        String beginTimeStr = beginTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        String endTimeStr = endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        // 处理条件
        // 告警处理条件
        String sqlWrapper = DynamicDatatableUpdateWrapperEnum.ROBOT_BROADCAST_OPERATION.getSqlWrapper();
        sqlWrapper = StrUtil.format(sqlWrapper, robotId, streamIdIn, beginTimeStr, endTimeStr, failValue);
        // 更新机器人操作日志表
        dynamicDatatableService.updateRobotBroadcastOperationLog(dbNameAndTableName, successValue, sqlWrapper);
    }

}
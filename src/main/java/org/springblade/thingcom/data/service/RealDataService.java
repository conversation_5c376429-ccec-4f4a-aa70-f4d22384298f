package org.springblade.thingcom.data.service;

import org.springblade.thingcom.command.dto.MulticastGroupDataDTO;
import org.springblade.thingcom.data.entity.RealData;
import org.springblade.thingcom.device.entity.Robot;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 实时数据记录服务接口
 *
 * <AUTHOR>
 * @since 2023-10-09 10:48:58
 */
public interface RealDataService {

    /**
     * 保存实时数据
     *
     * @param robot    机器人
     * @param realData 实时数据
     * @return
     */
    void saveRealData(Robot robot, RealData realData) throws InterruptedException;

    /**
     * 更新机器人数据
     *
     * @param robot             机器人
     * @param dataStreamId      数据流ID
     * @param robotNumberPrefix 机器人编号前缀
     * @param robotNumber       机器人编号
     * @param realData          实时数据
     * @return
     */
    void updateRobotData(Robot robot, String dataStreamId, String robotNumberPrefix, String robotNumber, RealData realData);

    /**
     * 机器人定时器异常处理
     *
     * @param robot    机器人
     * @param realData 实时数据
     * @return
     */
    void timerExceptionHandle(Robot robot, RealData realData) throws InterruptedException;

    /**
     * 实时数据 WebSocket 发送
     *
     * @param robot    机器人
     * @param streamId 数据流ID
     * @param mapData  实时数据Map
     * @return
     */
    void realDataWebSocketSend(Robot robot, String streamId, Map<Object, Object> mapData);

    /**
     * 解析参数配置值
     *
     * @param robotId      机器人ID
     * @param time         数据上报的时间
     * @param dataStreamId 数据流ID
     * @param value        数据值
     * @return
     */
    void parseParamConfigValue(Long robotId, LocalDateTime time, String dataStreamId, String value);

    /**
     * 解析并缓存参数配置值
     *
     * @param eui          机器人EUI
     * @param time         数据上报的时间
     * @param dataStreamId 数据流ID
     * @param value        数据值
     * @return
     */
    void parseAndCacheParamConfigValue(String eui, LocalDateTime time, String dataStreamId, String value);

    /**
     * 更新组播组操作日志状态
     *
     * @param robotId  机器人ID
     * @param time     数据上报的时间
     * @param dataList 数据列表
     * @return
     */
    void updateMulticastGroupOperationLogStatus(Long robotId, LocalDateTime time, List<MulticastGroupDataDTO> dataList);

    /**
     * 更新组播组操作日志状态
     *
     * @param robotId   机器人ID
     * @param streamId  数据流ID
     * @param status    状态
     * @param timeIndex 时间索引
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return
     */
    void updateRobotBroadcastOperationLog(Long robotId, String streamId, Integer status
            , LocalDateTime timeIndex, LocalDateTime beginTime, LocalDateTime endTime);
}
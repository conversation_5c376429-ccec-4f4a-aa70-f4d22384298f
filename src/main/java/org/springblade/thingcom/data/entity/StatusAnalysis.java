package org.springblade.thingcom.data.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;
import org.springblade.thingcom.core.mybatisPlus.entity.BaseEntity;

import java.io.Serializable;

/**
 * 状态解析实体类
 *
 * <AUTHOR>
 * @since 2024-04-09 19:46:27
 */
@Data
@TableName("tb_status_analysis")
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class StatusAnalysis extends BaseEntity<StatusAnalysis> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 参数
     */
    @Length(max = 10, message = "参数长度不能超过10")
    private String param;

    /**
     * 类型，0-上下线日志；1-运行；2-预警；3-告警；4-其它
     */
    private Integer type;

    /**
     * bit位
     */
    private Integer bit;

    /**
     * 状态名称
     */
    @Length(max = 50, message = "状态名称长度不能超过50")
    private String statusName;

    /**
     * 告警解决建议
     */
    @Length(max = 50, message = "告警解决建议长度不能超过100")
    private String solution;
}

package org.springblade.thingcom.data.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Param;
import org.springblade.thingcom.core.mybatisPlus.IBaseMapper;
import org.springblade.thingcom.data.entity.RealData;

/**
 * 实时数据记录（分库）数据库访问层
 *
 * <AUTHOR>
 * @since 2023-10-09 10:48:58
 */
public interface RealDataMapper extends IBaseMapper<RealData> {

    @DS("#dbName")
    void createTableIfNotExist(@Param("dbName") String dbName, @Param("imei") String imei);

    @DS("#dbName")
    void insertData(@Param("dbName") String dbName, @Param("imei") String imei, @Param("entity") RealData entity);
}

package org.springblade.thingcom.operationMaintenance.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.thingcom.core.mybatisPlus.IBaseService;
import org.springblade.thingcom.operationMaintenance.dto.RobotRunStatisDTO;
import org.springblade.thingcom.operationMaintenance.entity.RobotRunStatis;
import org.springblade.thingcom.operationMaintenance.vo.RobotRunStatisVO;

import java.time.LocalDateTime;

/**
 * 机器人运行统计服务接口
 *
 * <AUTHOR>
 * @since 2024-03-05 10:49:56
 */
public interface RobotRunStatisService extends IBaseService<RobotRunStatis> {

    /**
     * 检查数据合法性
     *
     * @param entity 实体对象
     * @return 异常消息，正常时为null
     */
    String validateData(RobotRunStatis entity);

    /**
     * 分页查询机器人运行统计
     *
     * @param page
     * @param dto
     * @return
     */
    IPage<RobotRunStatisVO> page(IPage<RobotRunStatis> page, RobotRunStatisDTO dto);

    /**
     * 新增机器人运行统计
     *
     * @param entity
     * @return
     */
    boolean saveData(RobotRunStatis entity);

    /**
     * 修改机器人运行统计
     *
     * @param entity
     * @return
     */
    boolean updateData(RobotRunStatis entity);

    /**
     * 保存或更新机器人运行统计
     *
     * @param robotId
     * @param time
     * @param deviceStatus
     */
    void saveOrUpdateData(Long robotId, LocalDateTime time, Integer deviceStatus);
}

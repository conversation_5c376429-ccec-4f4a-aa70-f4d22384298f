package org.springblade.thingcom.operationMaintenance.service;

import org.springblade.thingcom.core.mybatisPlus.IBaseService;
import org.springblade.thingcom.core.server.vo.ServerRunDataVO;
import org.springblade.thingcom.operationMaintenance.dto.ServerRunStatisDTO;
import org.springblade.thingcom.operationMaintenance.entity.Server;
import org.springblade.thingcom.operationMaintenance.entity.ServerRunStatis;
import org.springblade.thingcom.operationMaintenance.vo.ServerRunStatisVO;

/**
 * 服务器运行统计服务接口
 *
 * <AUTHOR>
 * @since 2024-03-06 15:34:50
 */
public interface ServerRunStatisService extends IBaseService<ServerRunStatis> {

    /**
     * 检查数据合法性
     *
     * @param entity 实体对象
     * @return 异常消息，正常时为null
     */
    String validateData(ServerRunStatis entity);

    /**
     * 新增服务器运行统计
     *
     * @param entity
     * @return
     */
    boolean saveData(ServerRunStatis entity);

    /**
     * 修改服务器运行统计
     *
     * @param entity
     * @return
     */
    boolean updateData(ServerRunStatis entity);

    /**
     * 服务器状态统计
     *
     * @return
     */
    ServerRunStatisVO statistics(ServerRunStatisDTO dto);

    /**
     * 保存或更新服务器运行数据
     *
     * @param server
     * @param runData
     * @param hardDiskStatus
     */
    void saveOrUpdateServerRunData(Server server, ServerRunDataVO runData, Integer hardDiskStatus);
}

package org.springblade.thingcom.operationMaintenance.service;

import org.springblade.thingcom.core.mybatisPlus.IBaseService;
import org.springblade.thingcom.device.entity.Robot;
import org.springblade.thingcom.operationMaintenance.entity.RobotClearingLog;

import java.time.LocalDateTime;

/**
 * 机器人清扫日志服务接口
 *
 * <AUTHOR>
 * @since 2024-03-07 10:58:27
 */
public interface RobotClearingLogService extends IBaseService<RobotClearingLog> {

    /**
     * 新增机器人清扫日志
     *
     * @param robot          机器人实体
     * @param clearingNumber 清扫编号
     * @param beginTime      清扫开始时间
     * @param endTime        清扫结束时间
     * @param workDuration   工作时长
     */
    void saveRobotClearingLog(Robot robot, Integer clearingNumber
            , LocalDateTime beginTime, LocalDateTime endTime, Long workDuration);

    /**
     * 更新机器人清扫日志（更新清扫结束时间）
     *
     * @param robot            机器人实体
     * @param beginTime        清扫开始时间
     * @param endTime          清扫结束时间
     * @param workDuration     工作时长
     * @param clearingDistance 清扫距离
     */
    void updateRobotClearingLog(Robot robot, LocalDateTime beginTime, LocalDateTime endTime
            , Long workDuration, Long clearingDistance);
}

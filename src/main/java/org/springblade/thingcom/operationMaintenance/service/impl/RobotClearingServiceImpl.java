package org.springblade.thingcom.operationMaintenance.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import org.springblade.thingcom.core.mybatisPlus.impl.BaseServiceImpl;
import org.springblade.thingcom.device.enums.DeviceStatusEnum;
import org.springblade.thingcom.device.service.RobotService;
import org.springblade.thingcom.operationMaintenance.dto.RobotClearingDTO;
import org.springblade.thingcom.operationMaintenance.entity.RobotClearing;
import org.springblade.thingcom.operationMaintenance.mapper.RobotClearingMapper;
import org.springblade.thingcom.operationMaintenance.service.RobotClearingService;
import org.springblade.thingcom.operationMaintenance.vo.RobotClearingVO;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 机器人实时清扫服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-07 10:15:18
 */
@Service
@AllArgsConstructor
public class RobotClearingServiceImpl extends BaseServiceImpl<RobotClearingMapper, RobotClearing> implements RobotClearingService {

    private final RobotService robotService;

    @Override
    public IPage<RobotClearingVO> page(IPage<RobotClearing> page, RobotClearingDTO dto) {
        return baseMapper.page(page, dto);
    }

    @Override
    public List<RobotClearingVO> list(RobotClearingDTO dto) {
        return baseMapper.list(dto);
    }

    @Override
    public RobotClearingVO statis(RobotClearingDTO dto) {
        RobotClearingVO vo = new RobotClearingVO();
        List<RobotClearingVO> robotClearingList = robotService.robotClearingStatis(dto);
        // 按照状态分组统计
        Map<Integer, Long> statusMap = robotClearingList.stream()
                .collect(Collectors.groupingBy(RobotClearingVO::getRobotStatus,
                        Collectors.summingLong(RobotClearingVO::getRobotCount)));
        // 填充VO
        long normalCount = statusMap.getOrDefault(DeviceStatusEnum.NORMAL.getValue(), 0L);
        long warnCount = statusMap.getOrDefault(DeviceStatusEnum.WARN.getValue(), 0L);
        long runCount = statusMap.getOrDefault(DeviceStatusEnum.RUN.getValue(), 0L);
        vo.setNormalCount(normalCount + warnCount + runCount);
        vo.setAlarmCount(statusMap.getOrDefault(DeviceStatusEnum.ALARM.getValue(), 0L));
        vo.setOfflineCount(statusMap.getOrDefault(DeviceStatusEnum.OFFLINE.getValue(), 0L));
        vo.setTotalCount(vo.getNormalCount() + vo.getAlarmCount() + vo.getOfflineCount());
        return vo;
    }

}

package org.springblade.thingcom.operationMaintenance.service.impl;

import org.springblade.thingcom.core.mybatisPlus.impl.BaseServiceImpl;
import org.springblade.thingcom.operationMaintenance.mapper.ServerMapper;
import org.springblade.thingcom.operationMaintenance.entity.Server;
import org.springblade.thingcom.operationMaintenance.service.ServerService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 服务器服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-06 16:13:44
 */
@Service
public class ServerServiceImpl extends BaseServiceImpl<ServerMapper, Server> implements ServerService {
    
    @Override
    public String validateData(Server entity) {
        // 检查名称是否存在
//        Long count = baseMapper.selectCount(Wrappers.<Server>lambdaQuery()
//                .eq(Server::getName, entity.getName())
//                // 编辑时，排除自己
//                .ne(entity.getId() != null, Server::getId, entity.getId()));
//        if (count > 0L) {
//            return "名称已经存在。名称：" + entity.getName();
//        }
        return null;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveData(Server entity) {
        // 新增服务器
        boolean ret = save(entity);
        return ret;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateData(Server entity) {
        // 修改服务器
        boolean ret = updateById(entity);
        return ret;
    }
}

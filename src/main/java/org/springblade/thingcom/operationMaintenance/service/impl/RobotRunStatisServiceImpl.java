package org.springblade.thingcom.operationMaintenance.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springblade.thingcom.core.mybatisPlus.impl.BaseServiceImpl;
import org.springblade.thingcom.device.enums.DeviceStatusEnum;
import org.springblade.thingcom.operationMaintenance.dto.RobotRunStatisDTO;
import org.springblade.thingcom.operationMaintenance.entity.RobotRunStatis;
import org.springblade.thingcom.operationMaintenance.mapper.RobotRunStatisMapper;
import org.springblade.thingcom.operationMaintenance.service.RobotRunStatisService;
import org.springblade.thingcom.operationMaintenance.vo.RobotRunStatisVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 机器人运行统计服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-05 10:49:56
 */
@Slf4j
@Service
public class RobotRunStatisServiceImpl extends BaseServiceImpl<RobotRunStatisMapper, RobotRunStatis> implements RobotRunStatisService {

    @Override
    public String validateData(RobotRunStatis entity) {
        // 检查名称是否存在
        //Long count = baseMapper.selectCount(Wrappers.<TsRobotRunStatis>lambdaQuery()
        //        .eq(TsRobotRunStatis::getName, entity.getName())
        //        // 编辑时，排除自己
        //        .ne(entity.getId() != null, TsRobotRunStatis::getId, entity.getId()));
        //if (count > 0L) {
        //    return "名称已经存在。名称：" + entity.getName();
        //}
        return null;
    }

    @Override
    public IPage<RobotRunStatisVO> page(IPage<RobotRunStatis> page, RobotRunStatisDTO dto) {
        return baseMapper.page(page, dto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveData(RobotRunStatis entity) {
        // 新增机器人运行统计
        return save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateData(RobotRunStatis entity) {
        // 修改机器人运行统计
        return updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateData(Long robotId, LocalDateTime time, Integer deviceStatus) {
        // 根据机器人id查询机器人统计记录，不存在则新增，存在则更新
        RobotRunStatis robotRunStatis = getOne(Wrappers.<RobotRunStatis>lambdaQuery()
                .eq(RobotRunStatis::getRobotId, robotId).last("LIMIT 1"));
        if (Objects.isNull(robotRunStatis)) {
            robotRunStatis = new RobotRunStatis();
            robotRunStatis.setRobotId(robotId);
            // 清扫
            if (DeviceStatusEnum.RUN.getValue().equals(deviceStatus)) {
                robotRunStatis.setClearingCount(1);
            }
            // 告警：清扫&告警各计数加1（清扫了 1次：清扫结果为失败）
            if (DeviceStatusEnum.RUN_FAIL.getValue().equals(deviceStatus)) {
                robotRunStatis.setClearingCount(1);
                robotRunStatis.setAlarmCount(1);
            }
            robotRunStatis.setTime(time);
            save(robotRunStatis);
        } else {
            // 清扫
            if (DeviceStatusEnum.RUN.getValue().equals(deviceStatus)) {
                Integer clearingCount = robotRunStatis.getClearingCount();
                clearingCount = Objects.isNull(clearingCount) ? 0 : clearingCount;
                robotRunStatis.setClearingCount(clearingCount + 1);
            }
            // 告警：清扫&告警各计数加1（清扫了 1次：清扫结果为失败）
            if (DeviceStatusEnum.RUN_FAIL.getValue().equals(deviceStatus)) {
                // 清扫
                Integer clearingCount = robotRunStatis.getClearingCount();
                clearingCount = Objects.isNull(clearingCount) ? 0 : clearingCount;
                robotRunStatis.setClearingCount(clearingCount + 1);
                // 告警
                Integer alarmCount = robotRunStatis.getAlarmCount();
                alarmCount = Objects.isNull(alarmCount) ? 0 : alarmCount;
                robotRunStatis.setAlarmCount(alarmCount + 1);
            }
            robotRunStatis.setTime(time);
            updateById(robotRunStatis);
        }
    }
}

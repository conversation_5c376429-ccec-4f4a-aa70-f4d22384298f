package org.springblade.thingcom.operationMaintenance.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.thingcom.core.excel.handler.AutoColumnWidthHandler;
import org.springblade.thingcom.core.excel.utils.ThingcomExcelUtil;
import org.springblade.thingcom.operationMaintenance.dto.ServerRunStatisDTO;
import org.springblade.thingcom.operationMaintenance.entity.Server;
import org.springblade.thingcom.operationMaintenance.entity.ServerRunStatis;
import org.springblade.thingcom.operationMaintenance.excel.ServerRunStatisExcel;
import org.springblade.thingcom.operationMaintenance.service.ServerRunStatisService;
import org.springblade.thingcom.operationMaintenance.service.ServerService;
import org.springblade.thingcom.operationMaintenance.vo.ServerRunStatisVO;
import org.springblade.thingcom.operationMaintenance.wrapper.ServerRunStatisWrapper;
import org.springblade.thingcom.translate.utils.TranslateUtil;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.Serializable;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 服务器运行统计控制层
 *
 * <AUTHOR>
 * @since 2024-03-06 15:34:50
 */
@AllArgsConstructor
@RestController
@RequestMapping("/tsServerRunStatis")
public class ServerRunStatisController extends BladeController {
    private final ServerRunStatisService serverRunStatisService;
    private final ServerService serverService;

    /**
     * 服务器运行统计分页查询
     *
     * @param query
     * @param dto
     * @return
     */
    @GetMapping("/page")
    public R<IPage<ServerRunStatisVO>> page(Query query, ServerRunStatisDTO dto) throws IOException, InterruptedException {
        LambdaQueryWrapper<ServerRunStatis> wrapper = setQueryParam(dto);
        IPage<ServerRunStatis> iPage = serverRunStatisService.page(Condition.getPage(query), wrapper);
        return data(ServerRunStatisWrapper.build().pageVO(iPage));
    }

    /**
     * 查看服务器运行统计详情
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public R<ServerRunStatisVO> show(@PathVariable Serializable id) {
        ServerRunStatis entity = serverRunStatisService.getById(id);
        return data(ServerRunStatisWrapper.build().entityVO(entity));
    }

    /**
     * sql检索条件
     *
     * @param dto
     * @return
     */
    private LambdaQueryWrapper<ServerRunStatis> setQueryParam(ServerRunStatisDTO dto) {
        LambdaQueryWrapper<ServerRunStatis> lqw = new LambdaQueryWrapper<>();
        // 时间查询
        if (Objects.nonNull(dto.getBeginDate()) && Objects.nonNull(dto.getEndDate())) {
            lqw.between(ServerRunStatis::getTime, dto.getBeginDate(), dto.getEndDate());
        }
        // 状态查询
        if (Objects.nonNull(dto.getStatus())) {
            List<Server> serverList = serverService.list(Wrappers.<Server>lambdaQuery().select(Server::getId).
                    eq(Server::getStatus, dto.getStatus()));
            if (CollUtil.isNotEmpty(serverList)) {
                lqw.in(ServerRunStatis::getServerId, serverList.stream().map(Server::getId).collect(Collectors.toList()));
            } else {
                lqw.in(ServerRunStatis::getServerId, -1);
            }
        }
        // 服务器ID查询
        lqw.eq(Objects.nonNull(dto.getServerId()), ServerRunStatis::getServerId, dto.getServerId());
        // 名称查询
        if (StrUtil.isNotBlank(dto.getServerName())) {
            List<Server> serverList = serverService.list(Wrappers.<Server>lambdaQuery().select(Server::getId)
                    .like(Server::getName, dto.getServerName()));
            if (CollUtil.isNotEmpty(serverList)) {
                lqw.in(ServerRunStatis::getServerId, serverList.stream().map(Server::getId).collect(Collectors.toList()));
            } else {
                lqw.in(ServerRunStatis::getServerId, -1);
            }
        }
        return lqw;
    }

    /**
     * 服务器状态统计
     *
     * @return
     */
    @GetMapping("/statusStatistics")
    public R<ServerRunStatisVO> statusStatistics(ServerRunStatisDTO dto) {
        return data(serverRunStatisService.statistics(dto));
    }

    /**
     * 导出
     *
     * @param dto
     * @param response
     * @throws Exception
     */
    @GetMapping("/export-data")
    public void exportData(ServerRunStatisDTO dto, HttpServletResponse response) throws Exception {
        LambdaQueryWrapper<ServerRunStatis> wrapper = setQueryParam(dto);
        List<ServerRunStatis> list = serverRunStatisService.list(wrapper);
        String sheetName = "服务器状态";
        // 判断是否需要翻译
        boolean translate = TranslateUtil.isTranslate(dto.getLang());
        // 表内数据常量翻译
        List<ServerRunStatisExcel> data = ServerRunStatisWrapper.build().listToExcel(list, translate);
        // 表头翻译
        List<List<String>> headerList = ThingcomExcelUtil.getHeadList(ServerRunStatisExcel.class, translate);
        // translate为真，翻译
        if (translate) {
            // 表文件名的翻译
            sheetName = TranslateUtil.chineseToEnglish(sheetName);
        }
        ThingcomExcelUtil.setExportDataFile(response, sheetName);
        EasyExcel.write(response.getOutputStream(), ServerRunStatisExcel.class)
                .inMemory(true)
                .registerWriteHandler(new AutoColumnWidthHandler())
                .head(headerList)
                .sheet(sheetName).doWrite(data);
    }
}

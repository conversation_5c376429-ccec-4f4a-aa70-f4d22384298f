package org.springblade.thingcom.operationMaintenance.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.thingcom.operationMaintenance.entity.DcsRunStatis;


/**
 * dcs运行统计出参类
 *
 * <AUTHOR>
 * @since 2024-03-19 10:43:26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties({"deleted", "createTime", "creatorId", "updateId"})
public class DcsRunStatisVO extends DcsRunStatis {
    private static final long serialVersionUID = 1L;

    /**
     * 状态
     */
    private String statusVo;

    /**
     * 运行时间
     */
    private String runDurationVo;

    /**
     * 通信延时时间
     */
    private String communicationLatencyDurationVo;

    /**
     * 总数
     */
    private Long totalCount;
    /**
     * 正常数量
     */
    private Long normalCount;
    /**
     * 离线数量
     */
    private Long offlineCount;
}

package org.springblade.thingcom.operationMaintenance.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.thingcom.operationMaintenance.entity.RobotRunStatis;


/**
 * 机器人运行统计出参类
 *
 * <AUTHOR>
 * @since 2024-03-05 10:49:52
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties({"deleted", "createTime", "creatorId", "updateTime", "updateId"})
public class RobotRunStatisVO extends RobotRunStatis {
    private static final long serialVersionUID = 1L;

    /**
     * 机器人区域编号
     */
    private String robotRegionalNumber;

    /**
     * 机器人分区id
     */
    private Long robotRegionalId;

    /**
     * 清扫路径id
     */
    private Long clearingPathId;

    /**
     * 机器人名称
     */
    private String robotName;

    /**
     * 清扫路径编号
     */
    private String clearingPathNumber;

    /**
     * 机器人状态
     */
    private String robotStatus;

    /**
     * 故障占比
     */
    private String failureRatio;
}

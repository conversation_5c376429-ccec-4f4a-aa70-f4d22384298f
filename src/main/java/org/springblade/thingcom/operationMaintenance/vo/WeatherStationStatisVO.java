package org.springblade.thingcom.operationMaintenance.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 气象站统计出参类
 *
 * <AUTHOR>
 * @since 2024-03-19 15:28:59
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WeatherStationStatisVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 气象站编号
     */
    private String weatherStationNumber;
    /**
     * 气象站名称
     */
    private String weatherStationName;
    /**
     * ip
     */
    private String ip;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 状态
     */
    private String statusName;
    /**
     * 风向
     */
    private String windDirectionVo;
    /**
     * 风速
     */
    private String windSpeedVo;
    /**
     * 温度
     */
    private String temperatureVo;
    /**
     * 湿度
     */
    private String humidityVo;
    /**
     * 辐照度
     */
    private String irradianceVo;
    /**
     * 降雨量
     */
    private String precipitationVo;
    /**
     * 位置
     */
    private String location;
    /**
     * 总数
     */
    private Long totalCount;
    /**
     * 正常
     */
    private Long normalCount;
    /**
     * 告警
     */
    private Long alarmCount;
    /**
     * 离线
     */
    private Long offlineCount;
    /**
     * 数据上报时间
     */
    private LocalDateTime time;

}

package org.springblade.thingcom.operationMaintenance.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.thingcom.operationMaintenance.entity.WeatherStation;
import org.springblade.thingcom.translate.annotation.TranslateCrtl;


/**
 * 气象站出参类
 *
 * <AUTHOR>
 * @since 2024-03-19 15:57:43
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties({"deleted", "createTime", "creatorId", "updateTime", "updateId"})
public class WeatherStationVO extends WeatherStation {
    private static final long serialVersionUID = 1L;

    /**
     * 气象站编号
     */
    @TranslateCrtl(translateMode = 0)
    private String weatherStationNumber;

    /**
     * 状态，详见字典表 weather_station_status
     */
    private String statusName;
}

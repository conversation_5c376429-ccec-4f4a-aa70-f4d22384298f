package org.springblade.thingcom.operationMaintenance.wrapper;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.thingcom.operationMaintenance.dto.WeatherStationDTO;
import org.springblade.thingcom.operationMaintenance.entity.WeatherStation;
import org.springblade.thingcom.operationMaintenance.enums.WeatherStationStatusEnum;
import org.springblade.thingcom.operationMaintenance.excel.GpsGatewayExcel;
import org.springblade.thingcom.operationMaintenance.excel.WeatherStationStatisExcel;
import org.springblade.thingcom.operationMaintenance.vo.WeatherStationStatisVO;
import org.springblade.thingcom.operationMaintenance.vo.WeatherStationVO;
import org.springblade.thingcom.translate.utils.TranslateUtil;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 气象站包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-03-19 15:57:43
 */
public class WeatherStationWrapper extends BaseEntityWrapper<WeatherStation, WeatherStationVO> {

    public static WeatherStationWrapper build() {
        return new WeatherStationWrapper();
    }

    @Override
    public WeatherStationVO entityVO(WeatherStation entity) {
        if (null == entity) {
            return null;
        }
        WeatherStationVO vo = BeanUtil.copy(entity, WeatherStationVO.class);
        vo.setWeatherStationNumber(entity.getNumber());
        vo.setStatusName(WeatherStationStatusEnum.translationValue(entity.getStatus()));
        return vo;
    }

    /**
     * 导出列表
     *
     * @param list
     * @return
     */
    public List<WeatherStationStatisExcel> listToExcel(List<WeatherStation> list, Boolean translate) {
        return list.stream()
                .map(entity -> {
                    WeatherStationStatisExcel excel = new WeatherStationStatisExcel();
                    excel.setWeatherStationName(entity.getName());
                    // translate为真 常量名翻译
                    if (translate) {
                        excel.setStatusName(TranslateUtil.chineseToEnglish
                                (WeatherStationStatusEnum.translationValue(entity.getStatus())));
                    } else {
                        excel.setStatusName(WeatherStationStatusEnum.translationValue(entity.getStatus()));
                    }
                    excel.setIp(entity.getIp());
                    excel.setWindSpeed(Objects.isNull(entity.getWindSpeed()) ? "" : entity.getWindSpeed() + "m/s");
                    excel.setTime(entity.getTime());
                    return excel;
                }).collect(Collectors.toList());
    }

    /**
     * 数据列表转excel
     *
     * @param list
     * @return
     */
    public List<GpsGatewayExcel.DataExport> gpsGatewayListToExcel(List<WeatherStation> list, boolean translate) {
        return list.stream().map(m -> {
            GpsGatewayExcel.DataExport excel = BeanUtil.copy(m, GpsGatewayExcel.DataExport.class);
            excel.setStatusName(WeatherStationStatusEnum.translationValue(m.getStatus()));
            if (translate) {
                excel.setStatusName(TranslateUtil.chineseToEnglish(excel.getStatusName()));
                excel.setWindDirection(TranslateUtil.chineseToEnglish(m.getWindDirection()));
            }
            return excel;
        }).collect(Collectors.toList());
    }

    /**
     * 数据列表转excel
     *
     * @param list
     * @return
     */
    public List<GpsGatewayExcel.StatisDataExport> gpsGatewayStatisListToExcel(List<WeatherStation> list, Boolean translate) {
        return list.stream().map(m -> {
            GpsGatewayExcel.StatisDataExport excel = new GpsGatewayExcel.StatisDataExport();
            excel.setNumber(m.getNumber());
            // translate为真 常量名翻译
            if (translate) {
                excel.setStatusName(TranslateUtil.chineseToEnglish
                        (WeatherStationStatusEnum.translationValue(m.getStatus())));
            } else {
                excel.setStatusName(WeatherStationStatusEnum.translationValue(m.getStatus()));
            }
            excel.setIp(m.getIp());
            excel.setWindSpeed(Func.toStr(m.getWindSpeed()));
            excel.setHumidity(Func.toStr(m.getHumidity()));
            excel.setTime(m.getTime());
            return excel;
        }).collect(Collectors.toList());
    }

    /**
     * 设置查询参数
     *
     * @param dto
     * @return LambdaQueryWrapper<WeatherStation>
     */
    public LambdaQueryWrapper<WeatherStation> setQueryParam(WeatherStationDTO dto) {
        LambdaQueryWrapper<WeatherStation> lqw = new LambdaQueryWrapper<>();
        // 类型
        lqw.eq(Objects.nonNull(dto.getType()), WeatherStation::getType, dto.getType());
        // 气象站类型
        lqw.eq(Objects.nonNull(dto.getWeatherStationType()), WeatherStation::getType, dto.getWeatherStationType());
        // id
        lqw.eq(Objects.nonNull(dto.getId()), WeatherStation::getId, dto.getId());
        // 气象站id
        lqw.eq(Objects.nonNull(dto.getWeatherStationId()), WeatherStation::getId, dto.getWeatherStationId());
        // 编号
        lqw.like(StrUtil.isNotBlank(dto.getNumber()), WeatherStation::getNumber, dto.getNumber());
        // mac
        lqw.like(StrUtil.isNotBlank(dto.getMac()), WeatherStation::getMac, dto.getMac());
        // 状态
        lqw.eq(Objects.nonNull(dto.getStatus()), WeatherStation::getStatus, dto.getStatus());
        // 起始日期
        if (Objects.nonNull(dto.getBeginDate()) && Objects.nonNull(dto.getEndDate())) {
            lqw.between(WeatherStation::getTime, dto.getBeginDate(), dto.getEndDate());
        }
        // 时间倒序
        lqw.orderByDesc(WeatherStation::getTime);
        return lqw;
    }

    /*** 统计模块 ***/

    public IPage<WeatherStationStatisVO> statisPageVO(IPage<WeatherStation> iPage) {
        List<WeatherStationStatisVO> records = this.statisListVO(iPage.getRecords());
        IPage<WeatherStationStatisVO> pageVo = new Page(iPage.getCurrent(), iPage.getSize(), iPage.getTotal());
        pageVo.setRecords(records);
        return pageVo;
    }

    public List<WeatherStationStatisVO> statisListVO(List<WeatherStation> list) {
        return list.stream().map(this::statisEntityVO).collect(Collectors.toList());
    }

    public WeatherStationStatisVO statisEntityVO(WeatherStation entity) {
        if (null == entity) {
            return null;
        }
        WeatherStationStatisVO vo = new WeatherStationStatisVO();
        vo.setWeatherStationNumber(entity.getNumber());
        vo.setWeatherStationName(entity.getName());
        vo.setIp(entity.getIp());
        vo.setStatus(entity.getStatus());
        vo.setStatusName(WeatherStationStatusEnum.translationValue(entity.getStatus()));
        vo.setWindDirectionVo(entity.getWindDirection());
        vo.setWindSpeedVo(Objects.isNull(entity.getWindSpeed()) ? "" : entity.getWindSpeed() + "m/s");
        vo.setTemperatureVo(Objects.isNull(entity.getTemperature()) ? "" : entity.getTemperature() + "℃");
        vo.setHumidityVo(Objects.isNull(entity.getHumidity()) ? "" : entity.getHumidity() + "RH%");
        vo.setIrradianceVo(Objects.isNull(entity.getIrradiance()) ? "" : entity.getIrradiance() + "w/㎡");
        vo.setPrecipitationVo(Objects.isNull(entity.getPrecipitation()) ? "" : entity.getPrecipitation() + "mm");
        vo.setLocation(entity.getLocation());
        vo.setTime(entity.getTime());
        return vo;
    }
}

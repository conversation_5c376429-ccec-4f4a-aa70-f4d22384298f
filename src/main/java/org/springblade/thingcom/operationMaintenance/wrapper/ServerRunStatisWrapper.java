package org.springblade.thingcom.operationMaintenance.wrapper;

import cn.hutool.core.util.StrUtil;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.thingcom.device.enums.DeviceStatusEnum;
import org.springblade.thingcom.operationMaintenance.entity.Server;
import org.springblade.thingcom.operationMaintenance.entity.ServerRunStatis;
import org.springblade.thingcom.operationMaintenance.enums.HardDiskStatusEnum;
import org.springblade.thingcom.operationMaintenance.enums.ServerStatusEnum;
import org.springblade.thingcom.operationMaintenance.excel.ServerRunStatisExcel;
import org.springblade.thingcom.operationMaintenance.service.ServerService;
import org.springblade.thingcom.operationMaintenance.vo.ServerRunStatisVO;
import org.springblade.thingcom.translate.utils.TranslateUtil;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 服务器运行统计包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-03-06 15:34:50
 */
public class ServerRunStatisWrapper extends BaseEntityWrapper<ServerRunStatis, ServerRunStatisVO> {

    private static final ServerService serverService;

    static {
        serverService = SpringUtil.getBean(ServerService.class);
    }

    public static ServerRunStatisWrapper build() {
        return new ServerRunStatisWrapper();
    }

    @Override
    public ServerRunStatisVO entityVO(ServerRunStatis entity) {
        if (null == entity) {
            return null;
        }
        ServerRunStatisVO vo = BeanUtil.copy(entity, ServerRunStatisVO.class);
        // 硬盘状态
        vo.setHardStausName(HardDiskStatusEnum.translationValue(entity.getHardDiskStatus()));
        if (StrUtil.isNotBlank(entity.getHardDiskUsed()) && StrUtil.isNotBlank(entity.getHardDiskTotal())) {
            // 提取里面的数字部分
            Double hardDiskUsed = Func.toDouble(entity.getHardDiskUsed().replace(CommonConstant.HARD_DISK_UNIT, ""), 0.0);
            Double hardDiskTotal = Func.toDouble(entity.getHardDiskTotal().replace(CommonConstant.HARD_DISK_UNIT, ""), 0.0);
            if (hardDiskTotal == 0.0) {
                vo.setHardDiskUseRate(0D);
            } else {
                // 硬盘使用率，使用率 / 硬盘总容量 * 100
                vo.setHardDiskUseRate(Double.valueOf(String.format("%.2f", (hardDiskUsed / hardDiskTotal * 100))));
            }
        }
        // 服务器状态
        Server server = serverService.getById(entity.getServerId());
        if (Objects.nonNull(server)) {
            // IP地址
            vo.setIp(server.getIp());
            // 服务器名称
            vo.setServerName(server.getName());
            // 服务器状态
            vo.setServerStatusName(ServerStatusEnum.translationValue(server.getStatus()));
        }
        return vo;
    }

    /**
     * 导出列表
     *
     * @param list
     * @return
     */
    public List<ServerRunStatisExcel> listToExcel(List<ServerRunStatis> list, Boolean translate) {
        return list.stream()
                .map(entity -> {
                    ServerRunStatisExcel excel = BeanUtil.copy(entity, ServerRunStatisExcel.class);
                    excel.setCpuUseRate(entity.getCpuUseRate());
                    excel.setMemoryUseRate(entity.getMemoryUseRate());
                    // 硬盘状态
                    // translate为真 常量名翻译
                    if (translate) {
                        excel.setHardDiskStatus(TranslateUtil.chineseToEnglish
                                (HardDiskStatusEnum.translationValue(entity.getHardDiskStatus())));
                    } else {
                        excel.setHardDiskStatus(HardDiskStatusEnum.translationValue(entity.getHardDiskStatus()));
                    }
                    Server server = serverService.getById(entity.getServerId());
                    if (Objects.nonNull(server)) {
                        // IP地址
                        excel.setIp(server.getIp());
                        // 服务器名称
                        if (translate) {
                            // translate为真 常量名翻译
                            excel.setServerName(TranslateUtil.chineseToEnglish
                                    (server.getName()));
                        } else {
                            excel.setServerName(server.getName());
                        }
                        // 服务器状态
                        if (translate) {
                            // translate为真 常量名翻译
                            excel.setServerStatus(TranslateUtil.chineseToEnglish
                                    (ServerStatusEnum.translationValue(server.getStatus())));
                        } else {
                            excel.setServerStatus(ServerStatusEnum.translationValue(server.getStatus()));
                        }
                    }
                    return excel;
                }).collect(Collectors.toList());
    }
}

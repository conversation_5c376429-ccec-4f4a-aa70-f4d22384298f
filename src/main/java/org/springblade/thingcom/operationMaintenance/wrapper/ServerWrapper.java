package org.springblade.thingcom.operationMaintenance.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.thingcom.operationMaintenance.entity.Server;
import org.springblade.thingcom.operationMaintenance.vo.ServerVO;

/**
 * 服务器包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-03-06 16:13:44
 */
public class ServerWrapper extends BaseEntityWrapper<Server, ServerVO> {
       
//  private static final ServerService serverService;
//
//	static {
//		serverService = SpringUtil.getBean(ServerService.class);
//	}

	public static ServerWrapper build() {
		return new ServerWrapper();
	}

	@Override
	public ServerVO entityVO(Server entity) {
	    if (null == entity) {
	        return null;
	    }
		ServerVO vo = BeanUtil.copy(entity, ServerVO.class);
		return vo;
	}
}

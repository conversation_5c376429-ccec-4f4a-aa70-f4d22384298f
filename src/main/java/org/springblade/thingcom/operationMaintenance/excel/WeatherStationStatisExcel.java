package org.springblade.thingcom.operationMaintenance.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 气象站统计excel类
 *
 * <AUTHOR>
 * @since 2024-03-19 15:28:59
 */
@Data
@ColumnWidth(16)
@HeadRowHeight(52)
@ContentRowHeight(18)
public class WeatherStationStatisExcel implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 气象站编号
     */
    @ExcelProperty(value = "气象站编号")
    private String weatherStationName;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态")
    private String statusName;

    /**
     * IP地址
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "IP地址")
    private String ip;

    /**
     * 风速，单位 m/s
     */
    @ExcelProperty(value = "风速(m/s)")
    private String windSpeed;

    /**
     * 湿度，单位 RH%
     */
    //@ExcelProperty(value = "湿度(RH%)")
    //private String humidity;

    /**
     * 更新时间
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "更新时间")
    private LocalDateTime time;
}
package org.springblade.thingcom.operationMaintenance.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;
import org.springblade.thingcom.core.mybatisPlus.entity.BaseEntity;
import org.springblade.thingcom.operationMaintenance.enums.WeatherStationStatusEnum;
import org.springblade.thingcom.operationMaintenance.enums.WeatherStationTypeEnum;
import org.springblade.thingcom.translate.annotation.TranslateCrtl;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 气象站实体类
 *
 * <AUTHOR>
 * @since 2024-03-19 15:57:43
 */
@Data
@TableName("tb_weather_station")
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class WeatherStation extends BaseEntity<WeatherStation> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 第三方平台id
     */
    private Long thirdPartyId;

    /**
     * 第三方风速id
     */
    private Long thirdPartyWindId;

    /**
     * 气象站类型，1-Thingcom；2-Arctech
     *
     * @see WeatherStationTypeEnum
     */
    private Integer type;
    /**
     * 气象站名称
     */
    @TranslateCrtl(translateMode = 0)
    @Length(max = 20, message = "编号长度不能超过20")
    private String name;
    /**
     * 气象站编号
     */
    @TranslateCrtl(translateMode = 0)
    @Length(max = 20, message = "编号长度不能超过20")
    private String number;
    /**
     * 网关编号
     */
    @TranslateCrtl(translateMode = 0)
    private String gatewayNumber;
    /**
     * mac
     */
    @Length(max = 20, message = "MAC长度不能超过20")
    private String mac;
    /**
     * 位置
     */
    private String location;
    /**
     * 风向
     *
     * @see org.springblade.thingcom.communication.enums.WindDirectionEnum
     */
    private String windDirection;
    /**
     * 风速，单位 m/s
     */
    private BigDecimal windSpeed;
    /**
     * 温度，单位 ℃
     */
    private BigDecimal temperature;
    /**
     * 湿度，单位 RH%
     */
    private BigDecimal humidity;
    /**
     * 辐照度，单位 w/㎡
     */
    private BigDecimal irradiance;
    /**
     * 降雨量，单位 mm
     */
    private BigDecimal precipitation;
    /**
     * ip
     */
    @Length(max = 50, message = "IP长度不能超过50")
    private String ip;
    /**
     * 数据上报时间
     */
    private LocalDateTime time;
    /**
     * 状态，详见字典表 weather_station_status
     */
    private Integer status = WeatherStationStatusEnum.NORMAL.getValue();

    /**
     * 风速告警状态
     */
    private Integer windStatus;

    /**
     * 通信箱告警状态
     */
    private Integer alarmStatus;

    /**
     * 采集器上报时间间隔
     */
    @Min(value = 1, message = "采集器上报时间间隔不能小于1")
    @Max(value = 65535, message = "采集器上报时间间隔不能超过65535")
    private Integer collectorReportInterval;
}

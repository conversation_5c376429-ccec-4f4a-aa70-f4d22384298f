package org.springblade.thingcom.operationMaintenance.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springblade.thingcom.core.mybatisPlus.entity.BaseEntity;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 机器人实时清扫实体类
 *
 * <AUTHOR>
 * @since 2024-03-07 10:15:18
 */
@Data
@TableName("tb_robot_clearing")
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class RobotClearing extends BaseEntity<RobotClearing> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 机器人id
     */
    private Long robotId;

    /**
     * 总工作时长，单位 s
     */
    private Long workDurationTotal;

    /**
     * 当月工作时长，单位 s
     */
    private Long workDurationCurrentMonth;

    /**
     * 总运行里程，单位 cm
     */
    private Long mileageTotal;

    /**
     * 当月运行里程，单位 cm
     */
    private Long mileageCurrentMonth;

    /**
     * 数据上报时间
     */
    private LocalDateTime time;

}

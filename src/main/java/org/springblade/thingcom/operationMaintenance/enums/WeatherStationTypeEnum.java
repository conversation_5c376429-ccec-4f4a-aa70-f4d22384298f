package org.springblade.thingcom.operationMaintenance.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @ClassName WeatherStationTypeEnum
 * @Description 气象站类型枚举
 * <AUTHOR>
 * @Date 2024/7/27 18:42
 * @Version 1.0
 **/
@Getter
@AllArgsConstructor
public enum WeatherStationTypeEnum {

    /**
     * 轨物气象站（gps 网关，采集器）
     */
    THINGCOM(1, "轨物气象站"),

    /**
     * 中信博气象站
     */
    ARCTECH(2, "中信博气象站"),

    ;

    private final Integer value;
    private final String label;
}

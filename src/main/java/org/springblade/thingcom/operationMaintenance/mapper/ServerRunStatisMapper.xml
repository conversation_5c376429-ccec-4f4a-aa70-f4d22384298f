<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.thingcom.operationMaintenance.mapper.ServerRunStatisMapper">

    <resultMap type="org.springblade.thingcom.operationMaintenance.entity.ServerRunStatis" id="TsServerRunStatisMap">
        <result column="id" property="id"/>
        <result column="server_id" property="serverId"/>
        <result column="cpu_use_rate" property="cpuUseRate"/>
        <result column="memory_use_rate" property="memoryUseRate"/>
        <result column="uplink_flow" property="uplinkFlow"/>
        <result column="downlink_flow" property="downlinkFlow"/>
        <result column="hard_disk_status" property="hardDiskStatus"/>
        <result column="time" property="time"/>
        <result column="creator_id" property="creatorId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_id" property="updateId"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="deleted"/>
    </resultMap>
    
</mapper>

package org.springblade.thingcom.operationMaintenance.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.springblade.thingcom.core.mybatisPlus.IBaseMapper;
import org.springblade.thingcom.operationMaintenance.dto.RobotRunStatisDTO;
import org.springblade.thingcom.operationMaintenance.entity.RobotRunStatis;
import org.springblade.thingcom.operationMaintenance.vo.RobotRunStatisVO;

/**
 * 机器人运行统计数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-05 10:49:56
 */
public interface RobotRunStatisMapper extends IBaseMapper<RobotRunStatis> {

    /**
     * 分页查询机器人运行统计数据
     *
     * @param page 分页对象
     * @param dto  查询条件对象
     * @return IPage<RobotRunStatisVO>
     */
    IPage<RobotRunStatisVO> page(IPage<RobotRunStatis> page, @Param("param") RobotRunStatisDTO dto);
}

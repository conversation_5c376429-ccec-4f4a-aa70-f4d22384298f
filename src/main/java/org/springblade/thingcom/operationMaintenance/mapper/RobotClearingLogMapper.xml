<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.thingcom.operationMaintenance.mapper.RobotClearingLogMapper">

    <resultMap type="org.springblade.thingcom.operationMaintenance.entity.RobotClearingLog" id="RobotClearingLogMap">
        <result column="id" property="id"/>
        <result column="robot_regional_id" property="robotRegionalId"/>
        <result column="robot_regional_number" property="robotRegionalNumber"/>
        <result column="robot_id" property="robotId"/>
        <result column="robot_name_prefix" property="robotNamePrefix"/>
        <result column="robot_number" property="robotNumber"/>
        <result column="robot_name" property="robotName"/>
        <result column="content" property="content"/>
        <result column="work_duration" property="workDuration"/>
        <result column="clearing_distance" property="clearingDistance"/>
        <result column="time" property="time"/>
        <result column="creator_id" property="creatorId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_id" property="updateId"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="deleted"/>
    </resultMap>
    
    <!--新增所有列-->
<!--    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into tl_robot_clearing_log(robot_regional_id, robot_regional_number, robot_id, robot_name_prefix, robot_number, content, work_duration, clearing_distance, time, creator_id, create_time, update_id, update_time, is_deleted)
        values (#{robotRegionalId}, #{robotRegionalNumber}, #{robotId}, #{robotNamePrefix}, #{robotNumber}, #{content}, #{workDuration}, #{clearingDistance}, #{time}, #{creatorId}, #{createTime}, #{updateId}, #{updateTime}, #{isDeleted})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into tl_robot_clearing_log(robot_regional_id, robot_regional_number, robot_id, robot_name_prefix, robot_number, content, work_duration, clearing_distance, time, creator_id, create_time, update_id, update_time, is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.robotRegionalId}, #{entity.robotRegionalNumber}, #{entity.robotId}, #{entity.robotNamePrefix}, #{entity.robotNumber}, #{entity.content}, #{entity.workDuration}, #{entity.clearingDistance}, #{entity.time}, #{entity.creatorId}, #{entity.createTime}, #{entity.updateId}, #{entity.updateTime}, #{entity.isDeleted})
        </foreach>
    </insert>-->

</mapper>

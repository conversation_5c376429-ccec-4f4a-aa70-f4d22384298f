package org.springblade.thingcom.operationMaintenance.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 机器人运行统计入参类
 *
 * <AUTHOR>
 * @since 2024-03-05 10:49:55
 */
@Data
public class RobotRunStatisSaveDTO {
    /**
	 * 机器人id
	 */
    private Long robotId;

    /**
	 * 离线时长，单位 s
	 */
    private Long offlineDuration;

    /**
	 * 待机时长，单位 s
	 */
    private Long standbyDuration;

    /**
	 * 工作时长，单位 s
	 */
    private Long workDuration;

    /**
	 * 故障时长，单位 s
	 */
    private Long alarmDuration;

    /**
	 * 故障次数
	 */
    private Integer failureNumber;

    /**
	 * 清扫次数
	 */
    private Integer cleaningNumber;

    /**
	 * 工作天数
	 */
    private Integer workingDays;

    /**
	 * 时间
	 */
    private LocalDateTime time;

}

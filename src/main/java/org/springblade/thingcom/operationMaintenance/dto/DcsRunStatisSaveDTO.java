package org.springblade.thingcom.operationMaintenance.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * dcs运行统计入参类
 *
 * <AUTHOR>
 * @since 2024-03-19 10:43:26
 */
@Data
public class DcsRunStatisSaveDTO {
    /**
	 * ip
	 */
    private String ip;

    /**
	 * 上行端口
	 */
    private String uplinkPort;

    /**
	 * 下行端口
	 */
    private String downlinkPort;

    /**
	 * 数据协议
	 */
    private String dataProtocol;

    /**
	 * 状态
	 */
    private Integer status;

    /**
	 * 运行时长，单位 s
	 */
    private Long runDuration;

    /**
	 * 通信延时时长，单位 s
	 */
    private Long communicationLatencyDuration;

    /**
	 * 数据上报时间
	 */
    private LocalDateTime time;

}

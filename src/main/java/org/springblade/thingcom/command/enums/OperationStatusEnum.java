package org.springblade.thingcom.command.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 操作状态枚举
 */
@Getter
@AllArgsConstructor
public enum OperationStatusEnum {

    /**
     * 失败
     */
    FAIL(0, "失败"),
    /**
     * 成功
     */
    SUCCESS(1, "成功"),
    /**
     * 设备处理中
     */
    DEVICE_PROCESSING(2, "设备处理中"),

    ;

    private final Integer value;
    private final String label;

    /**
     * 翻译编码
     */
    public static String translationValue(Integer value) {
        for (OperationStatusEnum e : OperationStatusEnum.values()) {
            if (e.value.equals(value)) {
                return e.label;
            }
        }
        return null;
    }
}

package org.springblade.thingcom.command.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 控制类型编码枚举
 */
@Getter
@AllArgsConstructor
public enum ControlTypeCodeEnum {

    /**
     * 停机位
     */
    STOP_BIT(1, "stop_bit"),

    /**
     * 运行速率（m/min:%）
     */
    RUN_RATE(2, "run_rate"),

    /**
     * 发射功率
     */
    SEND_POWER(3, "send_power"),

    /**
     * 区域
     */
    REGION(4, "region"),

    /**
     * 频段
     */
    FREQUENCY_BAND(5, "frequency_band"),

    ;

    /**
     * 编码
     */
    private final Integer value;

    /**
     * 翻译编码
     */
    private final String label;

    /**
     * 翻译编码
     */
    public static String translationValue(Integer value) {
        for (ControlTypeCodeEnum e : ControlTypeCodeEnum.values()) {
            if (e.value.equals(value)) {
                return e.label;
            }
        }
        return null;
    }

}

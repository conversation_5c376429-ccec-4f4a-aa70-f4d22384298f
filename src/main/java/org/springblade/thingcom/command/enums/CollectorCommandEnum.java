package org.springblade.thingcom.command.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 采集器指令枚举
 */
@Getter
@AllArgsConstructor
public enum CollectorCommandEnum {

    /**
     * 上报时间间隔
     */
    REPORT_INTERVAL("reportInterval", "application/GPSGW/{}/down"
            , "设置上报时间间隔"
            , "{\"operation\":\"write\",\"id\":\"1234\",\"cmmdId\":\"a7\",\"streamId\":\"5000\",\"regType\":\"20\",\"dataNum\":1,\"dataSize\":2,\"address\":0,\"dataType\":5,\"bitAddr\":0,\"value\":1}"
            , "上报时间间隔：{}s"),

    ;

    /**
     * id
     */
    private final String id;

    /**
     * 主题
     */
    private final String topic;

    /**
     * 名称
     */
    private final String name;

    /**
     * 指令模板
     */
    private final String commandTemplate;

    /**
     * 操作内容
     */
    private final String operationContentTemplate;

    public static CollectorCommandEnum getById(String id) {
        for (CollectorCommandEnum collectorCommandEnum : CollectorCommandEnum.values()) {
            if (collectorCommandEnum.getId().equals(id)) {
                return collectorCommandEnum;
            }
        }
        return null;
    }
}

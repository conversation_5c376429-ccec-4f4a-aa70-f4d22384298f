package org.springblade.thingcom.command.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @ClassName MulticastGroupEnum
 * @Description 组播枚举
 * <AUTHOR>
 * @Date 2024/7/20 17:51
 * @Version 1.0
 **/
@Getter
@AllArgsConstructor
public enum MulticastGroupEnum {

    /**
     * 校时
     */
    SYNC_TIME(1, "校时", "当前时间：{}"),

    /**
     * 风速告警
     */
    WIND_SPEED_ALARM(2, "风速告警", "当前风速：{}，风速阈值：{}"),

    /**
     * 风速解除告警
     */
    WIND_SPEED_ALARM_RELIEVE(3, "风速解除告警", "当前风速：{}，风速阈值：{}"),

    ;

    private final Integer code;
    private final String title;
    private final String content;
}

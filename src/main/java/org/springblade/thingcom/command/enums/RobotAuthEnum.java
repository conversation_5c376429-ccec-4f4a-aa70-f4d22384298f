package org.springblade.thingcom.command.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 机器人权限枚举
 */
@Getter
@AllArgsConstructor
public enum RobotAuthEnum {

    /**
     * 1-查看
     */
    VIEW(1, "仅查看"),
    /**
     * 2-查看 + 操作
     */
    OPERATION(2, "可查看、可操作"),

    ;

    final Integer value;
    final String label;

    public static String translationValue(Integer value) {
        for (RobotAuthEnum e : RobotAuthEnum.values()) {
            if (e.getValue().equals(value)) {
                return e.getLabel();
            }
        }
        return null;
    }
}

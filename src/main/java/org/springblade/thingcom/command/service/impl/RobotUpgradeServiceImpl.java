package org.springblade.thingcom.command.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.cache.CacheNames;
import org.springblade.common.utils.NumberUtil;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.thingcom.command.constant.CommandConstant;
import org.springblade.thingcom.command.enums.CommandEnum;
import org.springblade.thingcom.command.enums.OperationStatusEnum;
import org.springblade.thingcom.command.handle.CommandHandler;
import org.springblade.thingcom.command.service.RobotUpgradeService;
import org.springblade.thingcom.communication.utils.StreamIdUtil;
import org.springblade.thingcom.data.utils.MonitorValueParseUtil;
import org.springblade.thingcom.device.entity.Robot;
import org.springblade.thingcom.device.enums.UpgradeStatusEnum;
import org.springblade.thingcom.device.mapper.RobotMapper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.Future;

/**
 * @ClassName RobotUpgradeServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/7/25 14:48
 * @Version 1.0
 **/
@Slf4j
@Service
@AllArgsConstructor
public class RobotUpgradeServiceImpl implements RobotUpgradeService {

    private final BladeRedis bladeRedis;
    private final RobotMapper robotMapper;
    private final CommandHandler commandHandler;

    @Override
    @Async("asyncPoolTaskExecutor")
    public Future<String> upgradeBatch(List<Robot> robotList, List<String> hexStringList, String fileSize, Long userId, String createBy) {
        try {
            robotList.forEach(robot -> {
                // 更新机器人升级状态（升级中）
                robotMapper.update(new Robot(), Wrappers.<Robot>lambdaUpdate()
                        .set(Robot::getUpgradeStatus, UpgradeStatusEnum.UPGRADEING.getValue())
                        .eq(Robot::getId, robot.getId()));
                // 帧计算器清零
                bladeRedis.del(CacheNames.ROBOT_UPGRADE_FRAME_COUNTER);
                // 升级回复缓存清空
                bladeRedis.del(CacheNames.ROBOT_UPGRADE_RECEIVE + robot.getEui());
                // 升级命令帧发送
                CommandEnum commandEnumUpgrandCommand = CommandEnum.UPGRADE_COMMAND;
                // 数据域-参数
                StringBuilder value = new StringBuilder();
                // 操作内容
                String operationContent = commandEnumUpgrandCommand.getOperationContentTemplate();
                boolean upgradeBegin = sendCommandUpgrade(robot, operationContent, commandEnumUpgrandCommand, value
                        , CommandConstant.UPGRADE_CONTENT1, null, fileSize, userId, createBy);
                if (!upgradeBegin) {
                    Assert.isFalse(true, "升级失败");
                }

                // 起始数据帧发送
                CommandEnum commandEnumInitialData = CommandEnum.INITIAL_DATA;
                // 数据域-参数
                StringBuilder value2 = new StringBuilder();
                value2.append(hexStringList.get(0));
                // 操作内容
                String operationContent2 = commandEnumInitialData.getOperationContentTemplate();
                boolean upgradeInitialData = sendCommandUpgrade(robot, operationContent2, commandEnumInitialData, value2
                        , CommandConstant.UPGRADE_CONTENT2, null, fileSize, userId, createBy);
                if (!upgradeInitialData) {
                    Assert.isFalse(true, "升级失败");
                }

                // 中间数据帧发送
                CommandEnum commandEnumIntermediateData = CommandEnum.INTERMEDIATE_DATA;
                for (int i = 1; i < hexStringList.size() - 1; i++) {
                    // 数据域-参数
                    StringBuilder value3 = new StringBuilder();
                    value3.append(hexStringList.get(i));
                    // 操作内容
                    String operationContent3 = commandEnumIntermediateData.getOperationContentTemplate();
                    boolean upgradeMidData = sendCommandUpgrade(robot, operationContent3, commandEnumIntermediateData, value3
                            , CommandConstant.UPGRADE_CONTENT3, null, fileSize, userId, createBy);
                    if (!upgradeMidData) {
                        Assert.isFalse(true, "升级失败");
                    }
                }

                // 结束数据帧发送
                CommandEnum commandEnumEndData = CommandEnum.END_DATA;
                // 数据域-参数
                StringBuilder value4 = new StringBuilder();
                value4.append(hexStringList.get(hexStringList.size() - 1));
                Integer len = (hexStringList.get(hexStringList.size() - 1)).length() / 2;
                // 操作内容
                String operationContent4 = commandEnumEndData.getOperationContentTemplate();
                boolean upgradeEndData = sendCommandUpgrade(robot, operationContent4, commandEnumEndData, value4
                        , CommandConstant.UPGRADE_CONTENT4, len, fileSize, userId, createBy);
                if (!upgradeEndData) {
                    Assert.isFalse(true, "升级失败");
                }

                // 升级命令结束帧发送
                CommandEnum commandEnumEndUpgradeEnd = CommandEnum.UPGRADE_END;
                // 数据域-参数
                StringBuilder value5 = new StringBuilder();
                // 操作内容
                String operationContent5 = commandEnumEndUpgradeEnd.getOperationContentTemplate();
                boolean upgradeEnd = sendCommandUpgrade(robot, operationContent5, commandEnumEndUpgradeEnd, value5
                        , CommandConstant.UPGRADE_CONTENT5, null, fileSize, userId, createBy);
                if (!upgradeEnd) {
                    Assert.isFalse(true, "升级失败");
                }
                // 所有升级指令都成功：升级成功，有一条指令失败：升级失败
                // 升级成功，如果升级失败，下发指令那里会记录失败的日志
                commandHandler.upgradeFailHandler(robot, userId, createBy, OperationStatusEnum.SUCCESS.getValue());
            });
            log.info("升级成功");
            return new AsyncResult<>("升级成功");
        } catch (Exception e) {
            log.error("升级失败", e);
            return new AsyncResult<>("升级失败");
        }
    }

    @Override
    @Async("asyncPoolTaskExecutor")
    public Future<String> multicastGroupUpgrade(List<String> hexStringList, String fileSize) {
        try {
            // 更新机器人升级状态（升级中）
            robotMapper.update(new Robot(), Wrappers.<Robot>lambdaUpdate()
                    .set(Robot::getUpgradeStatus, UpgradeStatusEnum.UPGRADEING.getValue()));
            // 帧计算器清零
            bladeRedis.del(CacheNames.ROBOT_UPGRADE_FRAME_COUNTER);
            // 升级命令帧发送
            CommandEnum commandEnumUpgrandCommand = CommandEnum.UPGRADE_COMMAND;
            // 数据域-参数
            StringBuilder value = new StringBuilder();
            boolean upgradeBegin = multicastGroupUpgrade(commandEnumUpgrandCommand, value
                    , CommandConstant.UPGRADE_CONTENT1, null, fileSize);
            if (!upgradeBegin) {
                Assert.isFalse(true, "升级失败");
            }
            // 起始数据帧发送
            CommandEnum commandEnumInitialData = CommandEnum.INITIAL_DATA;
            // 数据域-参数
            StringBuilder value2 = new StringBuilder();
            value2.append(hexStringList.get(0));
            boolean upgradeInitialData = multicastGroupUpgrade(commandEnumInitialData, value2
                    , CommandConstant.UPGRADE_CONTENT2, null, fileSize);
            if (!upgradeInitialData) {
                Assert.isFalse(true, "升级失败");
            }
            // 中间数据帧发送
            CommandEnum commandEnumIntermediateData = CommandEnum.INTERMEDIATE_DATA;
            for (int i = 1; i < hexStringList.size() - 1; i++) {
                // 数据域-参数
                StringBuilder value3 = new StringBuilder();
                value3.append(hexStringList.get(i));
                boolean upgradeMidData = multicastGroupUpgrade(commandEnumIntermediateData, value3
                        , CommandConstant.UPGRADE_CONTENT3, null, fileSize);
                if (!upgradeMidData) {
                    Assert.isFalse(true, "升级失败");
                }
            }
            // 结束数据帧发送
            CommandEnum commandEnumEndData = CommandEnum.END_DATA;
            // 数据域-参数
            StringBuilder value4 = new StringBuilder();
            value4.append(hexStringList.get(hexStringList.size() - 1));
            Integer len = (hexStringList.get(hexStringList.size() - 1)).length() / 2;
            boolean upgradeEndData = multicastGroupUpgrade(commandEnumEndData, value4
                    , CommandConstant.UPGRADE_CONTENT4, len, fileSize);
            if (!upgradeEndData) {
                Assert.isFalse(true, "升级失败");
            }
            // 升级命令结束帧发送
            CommandEnum commandEnumEndUpgradeEnd = CommandEnum.UPGRADE_END;
            // 数据域-参数
            StringBuilder value5 = new StringBuilder();
            boolean upgradeEnd = multicastGroupUpgrade(commandEnumEndUpgradeEnd, value5
                    , CommandConstant.UPGRADE_CONTENT5, null, fileSize);
            if (!upgradeEnd) {
                Assert.isFalse(true, "升级失败");
            }
            // 更新机器人升级状态（升级成功）
            robotMapper.update(new Robot(), Wrappers.<Robot>lambdaUpdate()
                    .set(Robot::getUpgradeStatus, UpgradeStatusEnum.UPGRADE.getValue()));
            log.info("升级成功");
            return new AsyncResult<>("升级成功");
        } catch (Exception e) {
            // 更新机器人升级状态（升级失败）
            robotMapper.update(new Robot(), Wrappers.<Robot>lambdaUpdate()
                    .set(Robot::getUpgradeStatus, UpgradeStatusEnum.UNUPGRADE.getValue()));
            log.error("升级失败", e);
            return new AsyncResult<>("升级失败");
        }
    }

    /**
     * 机器人升级组装指令内容并发送
     *
     * @param robot
     * @param operationContent
     * @param commandEnum
     * @param value
     * @param type
     * @param len
     * @param fileSize
     */
    private boolean sendCommandUpgrade(Robot robot, String operationContent, CommandEnum commandEnum
            , StringBuilder value, Integer type, Integer len, String fileSize, Long userId, String createBy) {
        // 指令模板
        String commandTemplate = commandEnum.getCommandTemplate();
        // 机器人编号，并转换为16进制
        String robotNumber = robot.getRobotNumber();
        String robotNumberHex = NumberUtil.decToHex(robotNumber, 4);
        // 获取帧计数器
        String frameCounterHex = StreamIdUtil.getUpgradeFrameCounter(robot);
        // 校验位前置条件：需要校验的字符串
        String checkStr;
        if (CommandConstant.UPGRADE_CONTENT4.equals(type)) {
            String s = NumberUtil.decToHex(len.toString(), 2);
            checkStr = StrUtil.format(commandTemplate.split(StrUtil.COMMA)[0], robotNumberHex, frameCounterHex, s, value);
        } else if (CommandConstant.UPGRADE_CONTENT5.equals(type)) {
            checkStr = StrUtil.format(commandTemplate.split(StrUtil.COMMA)[0], robotNumberHex, frameCounterHex, fileSize, value);
        } else {
            checkStr = StrUtil.format(commandTemplate.split(StrUtil.COMMA)[0], robotNumberHex, frameCounterHex, value);
        }
        // 校验位
        String checkSum = NumberUtil.getCheckSum(checkStr);
        // 命令内容
        String commandContent;
        // 开始命令
        if (CommandConstant.UPGRADE_CONTENT1.equals(type)) {
            commandContent = StrUtil.format(commandTemplate, robotNumberHex, frameCounterHex, checkSum);
        } else if (CommandConstant.UPGRADE_CONTENT5.equals(type)) {
            // 结束命令
            commandContent = StrUtil.format(commandTemplate, robotNumberHex, frameCounterHex, fileSize, checkSum);
        } else if (CommandConstant.UPGRADE_CONTENT2.equals(type) || CommandConstant.UPGRADE_CONTENT3.equals(type)) {
            // 起始数据和中间数据
            commandContent = StrUtil.format(commandTemplate, robotNumberHex, frameCounterHex, value, checkSum);
        } else {
            // 结束数据
            String s = NumberUtil.decToHex(len.toString(), 2);
            commandContent = StrUtil.format(commandTemplate, robotNumberHex, frameCounterHex, s, value, checkSum);
        }
        commandContent = commandContent.replace(StrUtil.COMMA, StrUtil.EMPTY).toUpperCase();
        String upgradeData = MonitorValueParseUtil.getUpgradeData(commandContent);
        // 指令结果处理，判断指令是否执行成功，并记录操作日志
        return commandHandler.upgradeHandler(robot, commandContent, operationContent, upgradeData, userId, createBy);
    }

    /**
     * 组装指令内容并发送
     *
     * @return
     */
    private boolean multicastGroupUpgrade(CommandEnum commandEnum
            , StringBuilder value, Integer type, Integer len, String fileSize) {
        // 指令模板
        String commandTemplate = commandEnum.getCommandTemplate();
        // 机器人编号，并转换为16进制
        String robotNumberHex = "FFFF";
        // 获取帧计数器
        String frameCounterHex = StreamIdUtil.getMulticastGroupFrameCounter();
        // 校验位前置条件：需要校验的字符串
        String checkStr;
        if (CommandConstant.UPGRADE_CONTENT4.equals(type)) {
            String s = NumberUtil.decToHex(len.toString(), 2);
            checkStr = StrUtil.format(commandTemplate.split(StrUtil.COMMA)[0], robotNumberHex, frameCounterHex, s, value);
        } else if (CommandConstant.UPGRADE_CONTENT5.equals(type)) {
            checkStr = StrUtil.format(commandTemplate.split(StrUtil.COMMA)[0], robotNumberHex, frameCounterHex, fileSize, value);
        } else {
            checkStr = StrUtil.format(commandTemplate.split(StrUtil.COMMA)[0], robotNumberHex, frameCounterHex, value);
        }
        // 校验位
        String checkSum = NumberUtil.getCheckSum(checkStr);
        // 命令内容
        String commandContent;
        // 开始命令
        if (CommandConstant.UPGRADE_CONTENT1.equals(type)) {
            commandContent = StrUtil.format(commandTemplate, robotNumberHex, frameCounterHex, checkSum);
        } else if (CommandConstant.UPGRADE_CONTENT5.equals(type)) {
            // 结束命令
            commandContent = StrUtil.format(commandTemplate, robotNumberHex, frameCounterHex, fileSize, checkSum);
        } else if (CommandConstant.UPGRADE_CONTENT2.equals(type) || CommandConstant.UPGRADE_CONTENT3.equals(type)) {
            // 起始数据和中间数据
            commandContent = StrUtil.format(commandTemplate, robotNumberHex, frameCounterHex, value, checkSum);
        } else {
            // 结束数据
            String s = NumberUtil.decToHex(len.toString(), 2);
            commandContent = StrUtil.format(commandTemplate, robotNumberHex, frameCounterHex, s, value, checkSum);
        }
        commandContent = commandContent.replace(StrUtil.COMMA, StrUtil.EMPTY).toUpperCase();
        // 指令结果处理，判断指令是否执行成功
        return commandHandler.multicastGroupUpgradeHandler(commandContent);
    }
}

package org.springblade.thingcom.command.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.cache.CacheNames;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.CommonUtil;
import org.springblade.common.utils.NumberUtil;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.thingcom.base.cache.SystemCache;
import org.springblade.thingcom.base.entity.SystemSet;
import org.springblade.thingcom.base.service.SystemSetService;
import org.springblade.thingcom.command.constant.CommandConstant;
import org.springblade.thingcom.command.dto.*;
import org.springblade.thingcom.command.enums.CommandEnum;
import org.springblade.thingcom.command.enums.MulticastGroupEnum;
import org.springblade.thingcom.command.handle.CommandHandler;
import org.springblade.thingcom.command.service.CommandValidateService;
import org.springblade.thingcom.command.service.RobotCommandService;
import org.springblade.thingcom.command.service.RobotUpgradeService;
import org.springblade.thingcom.command.utils.ControlUtil;
import org.springblade.thingcom.communication.constant.ParamConstant;
import org.springblade.thingcom.communication.constant.StreamIdConstant;
import org.springblade.thingcom.communication.entity.Motor;
import org.springblade.thingcom.communication.entity.Timer;
import org.springblade.thingcom.communication.enums.ControlSwitchEnum;
import org.springblade.thingcom.communication.enums.MotorTypeEnum;
import org.springblade.thingcom.communication.enums.StopBitEnum;
import org.springblade.thingcom.communication.enums.WeekEnum;
import org.springblade.thingcom.communication.utils.StreamIdUtil;
import org.springblade.thingcom.core.rabbitMq.RabbitMqConfig;
import org.springblade.thingcom.core.rabbitMq.RabbitMqService;
import org.springblade.thingcom.data.constant.RobotDataConstant;
import org.springblade.thingcom.device.entity.Robot;
import org.springblade.thingcom.device.enums.AlarmStatusExtEnum;
import org.springblade.thingcom.device.enums.DeviceStatusEnum;
import org.springblade.thingcom.device.enums.LicenseAuthEnum;
import org.springblade.thingcom.device.enums.UpgradeStatusEnum;
import org.springblade.thingcom.device.mapper.RobotMapper;
import org.springblade.thingcom.operationMaintenance.entity.WeatherStation;
import org.springblade.thingcom.warning.enums.WeatherStationAlarmTypeEnum;
import org.springblade.thingcom.warning.service.AlarmService;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * @ClassName RobotCommandServiceImpl
 * @Description: 机器人指令服务实现类
 * @Author: zgq
 * @Date: 2024/1/19 11:53
 * @Version: 1.0
 **/
@Slf4j
@Service
@AllArgsConstructor
public class RobotCommandServiceImpl implements RobotCommandService {

    private final BladeRedis bladeRedis;
    private final RobotMapper robotMapper;
    private final CommandValidateService commandValidateService;
    private final CommandHandler commandHandler;
    private final RobotUpgradeService robotUpgradeService;
    private final SystemSetService systemSetService;
    private final RabbitMqService rabbitMqService;
    private final AlarmService alarmService;

    @Override
    public boolean saveNumberSet(Robot robot) {
        BladeUser user = AuthUtil.getUser();
        Long userId = user.getUserId();
        String createBy = user.getAccount();
        // 权限校验
        commandValidateService.commandAuth(userId);
        // 离线校验
        Assert.isTrue(!DeviceStatusEnum.OFFLINE.getValue().equals(robot.getStatus()), "机器人离线，请稍后再试");
        // 升级中校验
        Assert.isTrue(!UpgradeStatusEnum.UPGRADEING.getValue().equals(robot.getUpgradeStatus()), "机器人升级中，请稍后再试");
        // 指令协议
        CommandEnum commandEnum = CommandEnum.NUMBER_SET;
        // 操作内容
        String operationContentTemplate = commandEnum.getOperationContentTemplate();
        String operationContent = StrUtil.format(operationContentTemplate, robot.getRobotNumber());
        // 指令参数
        CommandParamDTO commandParam = new CommandParamDTO();
        String controlStreamId = StreamIdUtil.getControlStreamId(robot.getEui(), StreamIdConstant.A7);
        commandParam.setControlStreamId(controlStreamId);
        commandParam.setOperationTitle(commandEnum.getName());
        commandParam.setOperationContent(operationContent);
        commandParam.setUserId(userId);
        commandParam.setCreateBy(createBy);
        commandParam.setCommandEnum(commandEnum);
        // 新增或修改后的机器人编号
        commandParam.setValue(NumberUtil.decToHex(robot.getRobotNumber(), 4));
        // 下发指令
        sendCommand(robot, commandParam);
        return true;
    }

    @Override
    public boolean saveControlBoardSet(ControlBoardDTO dto) {
        BladeUser user = AuthUtil.getUser();
        Long userId = user.getUserId();
        String createBy = user.getAccount();
        // 权限校验
        commandValidateService.commandAuth(userId);
        // 指令密码校验
        commandValidateService.commandPassword(dto.getPassword());
        // 指令协议
        CommandEnum commandEnum = CommandEnum.CONTROL_BOARD;
        // 电机列表
        List<Motor> motorList = dto.getMotorList();
        // 行走电机
        Motor walkMotor = motorList.stream().filter(motor ->
                MotorTypeEnum.WALK.getValue().equals(motor.getMotorType())).findFirst().orElse(null);
        Assert.notNull(walkMotor, "行走电机不能为空");
        // 数据域-参数
        StringBuilder value = new StringBuilder();
        // 行走、毛刷、防风电机-速率
        value.append(NumberUtil.decToHex(walkMotor.getRunRate(), 2));
        value.append(NumberUtil.decToHex(0, 2));
        value.append(NumberUtil.decToHex(0, 2));
        // 行走、毛刷、防风电机-上限电流停机值
        value.append(NumberUtil.decToHex(walkMotor.getElectricityMaxAstrictStop(), 4));
        value.append(NumberUtil.decToHex(0, 4));
        value.append(NumberUtil.decToHex(0, 4));
        // 行走、毛刷、防风电机-上限电流预警值
        value.append(NumberUtil.decToHex(walkMotor.getElectricityMaxAstrictWarn(), 4));
        value.append(NumberUtil.decToHex(0, 4));
        value.append(NumberUtil.decToHex(0, 4));
        // 行走、毛刷、防风电机-运行超时时间
        value.append(NumberUtil.decToHex(walkMotor.getOverstepRunTime(), 4));
        value.append(NumberUtil.decToHex(0, 4));
        value.append(NumberUtil.decToHex(0, 4));
        // 运行反转时间
        value.append(NumberUtil.decToHex(dto.getReverseWaitTime(), 2));
        // 保护角度（删除后，为了硬件方便，默认为0）
        value.append(NumberUtil.decToHex(0, 2));
        // 操作内容
        String operationContentTemplate = commandEnum.getOperationContentTemplate();
        String operationContent = StrUtil.format(operationContentTemplate
                , dto.getReverseWaitTime()
                , walkMotor.getRunRate(), walkMotor.getElectricityMaxAstrictStop()
                , walkMotor.getElectricityMaxAstrictWarn(), walkMotor.getOverstepRunTime());
        // 批量发送指令
        sendCommandBatch(dto, operationContent, commandEnum, value, userId, createBy);
        return true;
    }

    @Override
    public boolean saveMulticastGroupControlBoardSet(ControlBoardDTO dto) {
        BladeUser user = AuthUtil.getUser();
        Long userId = user.getUserId();
        String createBy = user.getAccount();
        // 权限校验
        commandValidateService.commandAuth(userId);
        // 指令密码校验
        commandValidateService.commandPassword(dto.getPassword());
        // 组播时间间隔校验
        commandValidateService.multicastGroupInterval();
        // 指令协议
        CommandEnum commandEnum = CommandEnum.CONTROL_BOARD;
        // 电机列表
        List<Motor> motorList = dto.getMotorList();
        // 行走电机
        Motor walkMotor = motorList.stream().filter(motor ->
                MotorTypeEnum.WALK.getValue().equals(motor.getMotorType())).findFirst().orElse(null);
        Assert.notNull(walkMotor, "行走电机不能为空");
        // 数据域-参数
        StringBuilder value = new StringBuilder();
        // 行走、毛刷、防风电机-速率
        value.append(NumberUtil.decToHex(walkMotor.getRunRate(), 2));
        value.append(NumberUtil.decToHex(0, 2));
        value.append(NumberUtil.decToHex(0, 2));
        // 行走、毛刷、防风电机-上限电流停机值
        value.append(NumberUtil.decToHex(walkMotor.getElectricityMaxAstrictStop(), 4));
        value.append(NumberUtil.decToHex(0, 4));
        value.append(NumberUtil.decToHex(0, 4));
        // 行走、毛刷、防风电机-上限电流预警值
        value.append(NumberUtil.decToHex(walkMotor.getElectricityMaxAstrictWarn(), 4));
        value.append(NumberUtil.decToHex(0, 4));
        value.append(NumberUtil.decToHex(0, 4));
        // 行走、毛刷、防风电机-运行超时时间
        value.append(NumberUtil.decToHex(walkMotor.getOverstepRunTime(), 4));
        value.append(NumberUtil.decToHex(0, 4));
        value.append(NumberUtil.decToHex(0, 4));
        // 运行反转时间
        value.append(NumberUtil.decToHex(dto.getReverseWaitTime(), 2));
        // 保护角度（删除后，为了硬件方便，默认为0）
        value.append(NumberUtil.decToHex(0, 2));
        // 操作内容
        String operationContentTemplate = commandEnum.getOperationContentTemplate();
        String operationContent = StrUtil.format(operationContentTemplate
                , dto.getReverseWaitTime()
                , walkMotor.getRunRate(), walkMotor.getElectricityMaxAstrictStop()
                , walkMotor.getElectricityMaxAstrictWarn(), walkMotor.getOverstepRunTime());
        // 设置广播参数，并发送指令
        setMulticastGroupParamConfigAndSend(dto, commandEnum, value.toString(), operationContent, userId, createBy);
        return true;
    }

    @Override
    public boolean saveBatterySet(BatteryDTO dto) {
        BladeUser user = AuthUtil.getUser();
        Long userId = user.getUserId();
        String createBy = user.getAccount();
        // 权限校验
        commandValidateService.commandAuth(userId);
        // 指令密码校验
        commandValidateService.commandPassword(dto.getPassword());
        // 指令协议
        CommandEnum commandEnum = CommandEnum.BATTERY;
        // 数据域-参数
        StringBuilder value = new StringBuilder();
        // 保护电流
        value.append(NumberUtil.decToHex(dto.getProtectElectricity(), 4));
        // 高温阈值
        value.append(NumberUtil.decToHex(dto.getHighTemperatureThreshold(), 2));
        // 低温阈值
        value.append(NumberUtil.decToHex(dto.getLowTemperatureThreshold(), 2));
        // 保护温度
        value.append(NumberUtil.decToHex(dto.getProtectTemperature(), 2));
        // 恢复温度
        value.append(NumberUtil.decToHex(dto.getRecoverTemperature(), 2));
        // 保护电压
        value.append(NumberUtil.decToHex(dto.getProtectVoltage(), 2));
        // 恢复电压
        value.append(NumberUtil.decToHex(dto.getRecoverVoltage(), 2));
        // 保护电量
        value.append(NumberUtil.decToHex(dto.getProtectCapacity(), 2));
        // 限制运行电量
        value.append(NumberUtil.decToHex(dto.getAstrictCapacity(), 2));
        // 恢复电量
        value.append(NumberUtil.decToHex(dto.getRecoverCapacity(), 2));
        // 操作内容
        String operationContentTemplate = commandEnum.getOperationContentTemplate();
        String operationContent = StrUtil.format(operationContentTemplate
                , dto.getHighTemperatureThreshold(), dto.getLowTemperatureThreshold()
                , dto.getProtectTemperature(), dto.getRecoverTemperature()
                , dto.getProtectVoltage(), dto.getRecoverVoltage()
                , dto.getProtectCapacity(), dto.getRecoverCapacity()
                , dto.getProtectElectricity(), dto.getAstrictCapacity());
        // 批量发送指令
        sendCommandBatch(dto, operationContent, commandEnum, value, userId, createBy);
        return true;
    }

    @Override
    public boolean saveMulticastGroupBatterySet(BatteryDTO dto) {
        BladeUser user = AuthUtil.getUser();
        Long userId = user.getUserId();
        String createBy = user.getAccount();
        // 权限校验
        commandValidateService.commandAuth(userId);
        // 指令密码校验
        commandValidateService.commandPassword(dto.getPassword());
        // 组播时间间隔校验
        commandValidateService.multicastGroupInterval();
        // 指令协议
        CommandEnum commandEnum = CommandEnum.BATTERY;
        // 数据域-参数
        StringBuilder value = new StringBuilder();
        // 保护电流
        value.append(NumberUtil.decToHex(dto.getProtectElectricity(), 4));
        // 高温阈值
        value.append(NumberUtil.decToHex(dto.getHighTemperatureThreshold(), 2));
        // 低温阈值
        value.append(NumberUtil.decToHex(dto.getLowTemperatureThreshold(), 2));
        // 保护温度
        value.append(NumberUtil.decToHex(dto.getProtectTemperature(), 2));
        // 恢复温度
        value.append(NumberUtil.decToHex(dto.getRecoverTemperature(), 2));
        // 保护电压
        value.append(NumberUtil.decToHex(dto.getProtectVoltage(), 2));
        // 恢复电压
        value.append(NumberUtil.decToHex(dto.getRecoverVoltage(), 2));
        // 保护电量
        value.append(NumberUtil.decToHex(dto.getProtectCapacity(), 2));
        // 限制运行电量
        value.append(NumberUtil.decToHex(dto.getAstrictCapacity(), 2));
        // 恢复电量
        value.append(NumberUtil.decToHex(dto.getRecoverCapacity(), 2));
        // 操作内容
        String operationContentTemplate = commandEnum.getOperationContentTemplate();
        String operationContent = StrUtil.format(operationContentTemplate
                , dto.getHighTemperatureThreshold(), dto.getLowTemperatureThreshold()
                , dto.getProtectTemperature(), dto.getRecoverTemperature()
                , dto.getProtectVoltage(), dto.getRecoverVoltage()
                , dto.getProtectCapacity(), dto.getRecoverCapacity()
                , dto.getProtectElectricity(), dto.getAstrictCapacity());
        // 设置广播参数，并发送指令
        setMulticastGroupParamConfigAndSend(dto, commandEnum, value.toString(), operationContent, userId, createBy);
        return true;
    }

    @Override
    public boolean saveTimerSet(TimerDTO dto) {
        BladeUser user = AuthUtil.getUser();
        Long userId = user.getUserId();
        String createBy = user.getAccount();
        // 权限校验
        commandValidateService.commandAuth(userId);
        // 指令密码校验
        //commandValidateService.commandPasswordValidate(dto.getPassword());
        // 指令协议
        CommandEnum commandEnum = CommandEnum.TIMER;
        // 数据域-参数
        StringBuilder value = new StringBuilder();
        // 操作内容
        String operationContentTemplate = commandEnum.getOperationContentTemplate();
        List<String> timerContentList = new ArrayList<>(0);
        int timerIndex = 1;
        // 定时器
        for (Timer timer : dto.getTimerList()) {
            // 定时器-星期
            value.append(NumberUtil.decToHex(timer.getWeek().toString(), 2));
            // 定时器-时间
            String time = timer.getTime();
            Assert.isTrue(time.contains(StrUtil.COLON), "时间格式错误");
            String[] timeArr = time.split(StrUtil.COLON);
            String hour = timeArr[0];
            String minute = timeArr[1];
            // 请填写正确的时间
            int hourInt = NumberUtil.strToDec(hour);
            Assert.isTrue(hourInt >= 0 && hourInt <= 23, "时间格式错误");
            int minuteInt = NumberUtil.strToDec(minute);
            Assert.isTrue(minuteInt >= 0 && minuteInt <= 59, "时间格式错误");
            // 定时器-时
            value.append(NumberUtil.decToHex(hour, 2));
            // 定时器-分
            value.append(NumberUtil.decToHex(minute, 2));
            // 定时器-运行次数
            value.append(NumberUtil.decToHex(timer.getRunCount(), 2));
            String weekName = WeekEnum.translationValue(timer.getWeek());
            timerContentList.add(StrUtil.format(operationContentTemplate, timerIndex, weekName, time, timer.getRunCount()));
            timerIndex++;
        }
        String operationContent;
        if (CollUtil.isNotEmpty(timerContentList)) {
            operationContent = StrUtil.join(StrUtil.COMMA, timerContentList);
        } else {
            operationContent = "关闭定时器";
        }
        // 一共 7 个定时器，数据域不足的补 0，每个定时器 8 个字符（星期、小时、分钟、运行次数 各 2 个字符）
        int totalLength = ParamConstant.TIMER_DATA_LENGTH;
        int valueLength = value.length();
        if (valueLength < totalLength) {
            value.append(StrUtil.repeat("0", totalLength - valueLength));
        }
        // 批量发送指令
        sendCommandBatch(dto, operationContent, commandEnum, value, userId, createBy);
        return true;
    }

    @Override
    public boolean saveMulticastGroupTimerSet(TimerDTO dto) {
        BladeUser user = AuthUtil.getUser();
        Long userId = user.getUserId();
        String createBy = user.getAccount();
        // 权限校验
        commandValidateService.commandAuth(userId);
        // 指令密码校验
        //commandValidateService.commandPasswordValidate(dto.getPassword());
        // 组播时间间隔校验
        commandValidateService.multicastGroupInterval();
        // 指令协议
        CommandEnum commandEnum = CommandEnum.TIMER;
        // 数据域-参数
        StringBuilder value = new StringBuilder();
        // 操作内容
        String operationContentTemplate = commandEnum.getOperationContentTemplate();
        List<String> timerContentList = new ArrayList<>(0);
        int timerIndex = 1;
        // 定时器
        for (Timer timer : dto.getTimerList()) {
            // 定时器-星期
            value.append(NumberUtil.decToHex(timer.getWeek().toString(), 2));
            // 定时器-时间
            String time = timer.getTime();
            Assert.isTrue(time.contains(StrUtil.COLON), "时间格式错误");
            String[] timeArr = time.split(StrUtil.COLON);
            String hour = timeArr[0];
            String minute = timeArr[1];
            // 请填写正确的时间
            int hourInt = NumberUtil.strToDec(hour);
            Assert.isTrue(hourInt >= 0 && hourInt <= 23, "时间格式错误");
            int minuteInt = NumberUtil.strToDec(minute);
            Assert.isTrue(minuteInt >= 0 && minuteInt <= 59, "时间格式错误");
            // 定时器-时
            value.append(NumberUtil.decToHex(hour, 2));
            // 定时器-分
            value.append(NumberUtil.decToHex(minute, 2));
            // 定时器-运行次数
            value.append(NumberUtil.decToHex(timer.getRunCount(), 2));
            String weekName = WeekEnum.translationValue(timer.getWeek());
            timerContentList.add(StrUtil.format(operationContentTemplate, timerIndex, weekName, time, timer.getRunCount()));
            timerIndex++;
        }
        String operationContent;
        if (CollUtil.isNotEmpty(timerContentList)) {
            operationContent = StrUtil.join(StrUtil.COMMA, timerContentList);
        } else {
            operationContent = "关闭定时器";
        }
        // 一共 7 个定时器，数据域不足的补 0，每个定时器 8 个字符（星期、小时、分钟、运行次数 各 2 个字符）
        int totalLength = ParamConstant.TIMER_DATA_LENGTH;
        int valueLength = value.length();
        if (valueLength < totalLength) {
            value.append(StrUtil.repeat("0", totalLength - valueLength));
        }
        // 设置广播参数，并发送指令
        setMulticastGroupParamConfigAndSend(dto, commandEnum, value.toString(), operationContent, userId, createBy);
        return true;
    }

    @Override
    public boolean saveStopBitSet(BaseCommandDTO dto) {
        BladeUser user = AuthUtil.getUser();
        Long userId = user.getUserId();
        String createBy = user.getAccount();
        // 权限校验
        commandValidateService.commandAuth(userId);
        // 指令密码校验
        commandValidateService.commandPassword(dto.getPassword());
        // 指令协议
        CommandEnum commandEnum = CommandEnum.STOP_BIT;
        // 数据域-参数
        StringBuilder value = new StringBuilder();
        // 停机位
        value.append(NumberUtil.decToHex(dto.getValue(), 2));
        // 操作内容
        String operationContentTemplate = commandEnum.getOperationContentTemplate();
        String operationContent = StrUtil.format(operationContentTemplate
                , StopBitEnum.translationValue(dto.getValue()));
        // 批量发送指令
        sendCommandBatch(dto, operationContent, commandEnum, value, userId, createBy);
        return true;
    }

    @Override
    public boolean saveMulticastGroupStopBitSet(BaseCommandDTO dto) {
        BladeUser user = AuthUtil.getUser();
        Long userId = user.getUserId();
        String createBy = user.getAccount();
        // 权限校验
        commandValidateService.commandAuth(userId);
        // 指令密码校验
        commandValidateService.commandPassword(dto.getPassword());
        // 组播时间间隔校验
        commandValidateService.multicastGroupInterval();
        // 指令协议
        CommandEnum commandEnum = CommandEnum.STOP_BIT;
        // 数据域-参数
        StringBuilder value = new StringBuilder();
        // 停机位
        value.append(NumberUtil.decToHex(dto.getValue(), 2));
        // 操作内容
        String operationContentTemplate = commandEnum.getOperationContentTemplate();
        String operationContent = StrUtil.format(operationContentTemplate
                , StopBitEnum.translationValue(dto.getValue()));
        // 设置广播参数，并发送指令
        setMulticastGroupParamConfigAndSend(dto, commandEnum, value.toString(), operationContent, userId, createBy);
        return true;
    }

    @Override
    public boolean saveLoraSet(LoraDTO dto) {
        BladeUser user = AuthUtil.getUser();
        Long userId = user.getUserId();
        String createBy = user.getAccount();
        // 权限校验
        commandValidateService.commandAuth(userId);
        // 指令密码校验
        commandValidateService.commandPassword(dto.getPassword());
        // 指令协议
        CommandEnum commandEnum = CommandEnum.LORA;
        // 数据域-参数
        StringBuilder value = new StringBuilder();
        // Lora 功率
        value.append(NumberUtil.decToHex(dto.getSendPower(), 2));
        // Lora 频率
        value.append(NumberUtil.decToHex(dto.getFrequencyBand(), 2));
        // Lora 速率
        value.append(NumberUtil.decToHex(dto.getSpeedRate(), 2));
        // 操作内容
        String operationContentTemplate = commandEnum.getOperationContentTemplate();
        // 区域名称
        //String regionName = dto.getRegionName();
        //if (StrUtil.isBlank(regionName)) {
        //    // 通过 value 转换为 label
        //    ControlType controlType = ControlTypeCache.getOneByCommandValue(
        //            ControlTypeCodeEnum.REGION.getLabel(), dto.getRegion());
        //    regionName = Objects.isNull(controlType) ? dto.getRegion() : controlType.getValue();
        //}
        // 频段名称
        //String frequencyBandName = dto.getFrequencyBandName();
        //if (StrUtil.isBlank(frequencyBandName)) {
        //    // 频段：通过 value 转换为 label
        //    ControlType controlType = ControlTypeCache.getOneByCommandValue(
        //            ControlTypeCodeEnum.FREQUENCY_BAND.getLabel(), dto.getFrequencyBand());
        //    frequencyBandName = Objects.isNull(controlType) ? dto.getFrequencyBand() : controlType.getValue();
        //}
        String operationContent = StrUtil.format(operationContentTemplate, dto.getSendPower());
        // 批量发送指令
        sendCommandBatch(dto, operationContent, commandEnum, value, userId, createBy);
        return true;
    }

    @Override
    public boolean saveMulticastGroupLoraSet(LoraDTO dto) {
        BladeUser user = AuthUtil.getUser();
        Long userId = user.getUserId();
        String createBy = user.getAccount();
        // 权限校验
        commandValidateService.commandAuth(userId);
        // 指令密码校验
        commandValidateService.commandPassword(dto.getPassword());
        // 组播时间间隔校验
        commandValidateService.multicastGroupInterval();
        // 指令协议
        CommandEnum commandEnum = CommandEnum.LORA;
        // 数据域-参数
        StringBuilder value = new StringBuilder();
        // Lora 功率
        value.append(NumberUtil.decToHex(dto.getSendPower(), 2));
        // Lora 频率
        value.append(NumberUtil.decToHex(dto.getFrequencyBand(), 2));
        // Lora 速率
        value.append(NumberUtil.decToHex(dto.getSpeedRate(), 2));
        // 操作内容
        String operationContentTemplate = commandEnum.getOperationContentTemplate();
        // 区域名称
        //String regionName = dto.getRegionName();
        //if (StrUtil.isBlank(regionName)) {
        //    // 通过 value 转换为 label
        //    ControlType controlType = ControlTypeCache.getOneByCommandValue(
        //            ControlTypeCodeEnum.REGION.getLabel(), dto.getRegion());
        //    regionName = Objects.isNull(controlType) ? dto.getRegion() : controlType.getValue();
        //}
        // 频段名称
        //String frequencyBandName = dto.getFrequencyBandName();
        //if (StrUtil.isBlank(frequencyBandName)) {
        //    // 频段：通过 value 转换为 label
        //    ControlType controlType = ControlTypeCache.getOneByCommandValue(
        //            ControlTypeCodeEnum.FREQUENCY_BAND.getLabel(), dto.getFrequencyBand());
        //    frequencyBandName = Objects.isNull(controlType) ? dto.getFrequencyBand() : controlType.getValue();
        //}
        String operationContent = StrUtil.format(operationContentTemplate, dto.getSendPower());
        // 设置广播参数，并发送指令
        setMulticastGroupParamConfigAndSend(dto, commandEnum, value.toString(), operationContent, userId, createBy);
        return true;
    }

    @Override
    public boolean saveWindSet(WindDTO dto) {
        BladeUser user = AuthUtil.getUser();
        Long userId = user.getUserId();
        String createBy = user.getAccount();
        // 权限校验
        commandValidateService.commandAuth(userId);
        // 指令密码校验
        commandValidateService.commandPassword(dto.getPassword());
        // 指令协议
        CommandEnum commandEnum = CommandEnum.WIND_SPEED;
        // 数据域-参数
        StringBuilder value = new StringBuilder();
        // 风速阈值
        value.append(NumberUtil.decToHex(dto.getWindSpeedThreshold(), 2));
        // 操作内容
        String operationContentTemplate = commandEnum.getOperationContentTemplate();
        String operationContent = StrUtil.format(operationContentTemplate, dto.getWindSpeedThreshold());
        // 批量发送指令
        sendCommandBatch(dto, operationContent, commandEnum, value, userId, createBy);
        return true;
    }

    @Override
    public boolean saveMulticastGroupWindSet(WindDTO dto) {
        BladeUser user = AuthUtil.getUser();
        Long userId = user.getUserId();
        String createBy = user.getAccount();
        // 权限校验
        commandValidateService.commandAuth(userId);
        // 指令密码校验
        commandValidateService.commandPassword(dto.getPassword());
        // 组播时间间隔校验
        commandValidateService.multicastGroupInterval();
        // 指令协议
        CommandEnum commandEnum = CommandEnum.WIND_SPEED;
        // 数据域-参数
        StringBuilder value = new StringBuilder();
        // 风速阈值
        value.append(NumberUtil.decToHex(dto.getWindSpeedThreshold(), 2));
        // 操作内容
        String operationContentTemplate = commandEnum.getOperationContentTemplate();
        String operationContent = StrUtil.format(operationContentTemplate, dto.getWindSpeedThreshold());
        // 设置广播参数，并发送指令
        setMulticastGroupParamConfigAndSend(dto, commandEnum, value.toString(), operationContent, userId, createBy);
        return true;
    }

    @Override
    public boolean saveUnlock(BaseCommandDTO dto) {
        BladeUser user = AuthUtil.getUser();
        Long userId = user.getUserId();
        String createBy = user.getAccount();
        // 权限校验
        commandValidateService.commandAuth(userId);
        // 指令密码校验
        commandValidateService.lockPassword(CommandEnum.UNLOCK.getId(), dto.getPassword());
        // 指令协议
        CommandEnum commandEnum = CommandEnum.UNLOCK;
        // 操作内容
        String operationContent = commandEnum.getOperationContentTemplate();
        // 批量发送指令
        sendCommandBatch(dto, operationContent, commandEnum, null, userId, createBy);
        return true;
    }

    @Override
    public boolean saveMulticastGroupUnlock(BaseCommandDTO dto) {
        BladeUser user = AuthUtil.getUser();
        Long userId = user.getUserId();
        String createBy = user.getAccount();
        // 权限校验
        commandValidateService.commandAuth(userId);
        // 指令密码校验
        commandValidateService.lockPassword(CommandEnum.UNLOCK.getId(), dto.getPassword());
        // 组播时间间隔校验
        commandValidateService.multicastGroupInterval();
        // 指令协议
        CommandEnum commandEnum = CommandEnum.UNLOCK;
        // 操作内容
        String operationContent = commandEnum.getOperationContentTemplate();
        // 设置广播参数，并发送指令
        setMulticastGroupParamConfigAndSend(dto, commandEnum, null, operationContent, userId, createBy);
        return true;
    }

    @Override
    public boolean saveLock(BaseCommandDTO dto) {
        BladeUser user = AuthUtil.getUser();
        Long userId = user.getUserId();
        String createBy = user.getAccount();
        // 权限校验
        commandValidateService.commandAuth(userId);
        // 指令密码校验
        commandValidateService.lockPassword(CommandEnum.LOCK.getId(), dto.getPassword());
        // 指令协议
        CommandEnum commandEnum = CommandEnum.LOCK;
        // 操作内容
        String operationContent = commandEnum.getOperationContentTemplate();
        // 批量发送指令
        sendCommandBatch(dto, operationContent, commandEnum, null, userId, createBy);
        return true;
    }

    @Override
    public boolean saveMulticastGroupLock(BaseCommandDTO dto) {
        BladeUser user = AuthUtil.getUser();
        Long userId = user.getUserId();
        String createBy = user.getAccount();
        // 权限校验
        commandValidateService.commandAuth(userId);
        // 指令密码校验
        commandValidateService.lockPassword(CommandEnum.LOCK.getId(), dto.getPassword());
        // 组播时间间隔校验
        commandValidateService.multicastGroupInterval();
        // 指令协议
        CommandEnum commandEnum = CommandEnum.LOCK;
        // 操作内容
        String operationContent = commandEnum.getOperationContentTemplate();
        // 设置广播参数，并发送指令
        setMulticastGroupParamConfigAndSend(dto, commandEnum, null, operationContent, userId, createBy);
        return true;
    }

    @Override
    public boolean saveMainBoardTemperatureSet(MainBoardTemperatureDTO dto) {
        BladeUser user = AuthUtil.getUser();
        Long userId = user.getUserId();
        String createBy = user.getAccount();
        // 权限校验
        commandValidateService.commandAuth(userId);
        // 指令密码校验
        commandValidateService.commandPassword(dto.getPassword());
        // 指令协议
        CommandEnum commandEnum = CommandEnum.MAIN_BOARD_TEMPERATURE;
        // 数据域-参数
        StringBuilder value = new StringBuilder();
        // 保护温度
        BigDecimal protectionTemperature = dto.getProtectionTemperature().multiply(BigDecimal.valueOf(10));
        value.append(NumberUtil.decToHex(protectionTemperature.toString(), 4));
        // 恢复温度
        BigDecimal recoveryTemperature = dto.getRecoveryTemperature().multiply(BigDecimal.valueOf(10));
        value.append(NumberUtil.decToHex(recoveryTemperature.toString(), 4));
        String operationContentTemplate = commandEnum.getOperationContentTemplate();
        String operationContent = StrUtil.format(operationContentTemplate
                , dto.getProtectionTemperature(), dto.getRecoveryTemperature());
        // 批量发送指令
        sendCommandBatch(dto, operationContent, commandEnum, value, userId, createBy);
        return true;
    }

    @Override
    public boolean saveMulticastGroupMainBoardTemperatureSet(MainBoardTemperatureDTO dto) {
        BladeUser user = AuthUtil.getUser();
        Long userId = user.getUserId();
        String createBy = user.getAccount();
        // 权限校验
        commandValidateService.commandAuth(userId);
        // 指令密码校验
        commandValidateService.commandPassword(dto.getPassword());
        // 组播时间间隔校验
        commandValidateService.multicastGroupInterval();
        // 指令协议
        CommandEnum commandEnum = CommandEnum.MAIN_BOARD_TEMPERATURE;
        // 数据域-参数
        StringBuilder value = new StringBuilder();
        // 保护温度
        BigDecimal protectionTemperature = dto.getProtectionTemperature().multiply(BigDecimal.valueOf(10));
        value.append(NumberUtil.decToHex(protectionTemperature.toString(), 4));
        // 恢复温度
        BigDecimal recoveryTemperature = dto.getRecoveryTemperature().multiply(BigDecimal.valueOf(10));
        value.append(NumberUtil.decToHex(recoveryTemperature.toString(), 4));
        String operationContentTemplate = commandEnum.getOperationContentTemplate();
        String operationContent = StrUtil.format(operationContentTemplate
                , dto.getProtectionTemperature(), dto.getRecoveryTemperature());
        // 设置广播参数，并发送指令
        setMulticastGroupParamConfigAndSend(dto, commandEnum, value.toString(), operationContent, userId, createBy);
        return true;
    }

    @Override
    public boolean saveStart(StartDTO dto) {
        BladeUser user = AuthUtil.getUser();
        Long userId = user.getUserId();
        String createBy = user.getAccount();
        // 权限校验
        commandValidateService.commandAuth(userId);
        // 查询系统配置信息
        SystemSet systemSet = systemSetService.getOne(Wrappers.<SystemSet>lambdaQuery()
                .orderByDesc(SystemSet::getId).last("limit 1"));
        // 机器人运行条件校验
        commandValidateService.runCondition(dto.getRobotRegionalId(), dto.getRobotIds(), systemSet);
        // 校验风速、湿度告警
        Integer alarmValue = commandValidateService.environmentAlarm(systemSet);
        String alarmContent = WeatherStationAlarmTypeEnum.translationValue(alarmValue);
        if (StrUtil.isNotBlank(alarmContent)) {
            throw new IllegalArgumentException(alarmContent + "，请勿操作");
        }
        // 指令协议
        CommandEnum commandEnum = CommandEnum.START;
        // 数据域-参数
        StringBuilder value = new StringBuilder();
        // 启动次数
        value.append(NumberUtil.decToHex(dto.getStartCount(), 2));
        // 操作内容
        String operationContent = commandEnum.getOperationContentTemplate();
        // 批量发送指令
        sendCommandBatch(dto, operationContent, commandEnum, value, userId, createBy);
        return true;
    }

    @Override
    public boolean saveForward(BaseCommandDTO dto) {
        BladeUser user = AuthUtil.getUser();
        Long userId = user.getUserId();
        String createBy = user.getAccount();
        // 权限校验
        commandValidateService.commandAuth(userId);
        // 查询系统配置信息
        SystemSet systemSet = systemSetService.getOne(Wrappers.<SystemSet>lambdaQuery()
                .orderByDesc(SystemSet::getId).last("limit 1"));
        // 机器人运行条件校验
        commandValidateService.runCondition(dto.getRobotRegionalId(), dto.getRobotIds(), systemSet);
        // 校验风速、湿度告警
        Integer alarmValue = commandValidateService.environmentAlarm(systemSet);
        String alarmContent = WeatherStationAlarmTypeEnum.translationValue(alarmValue);
        if (StrUtil.isNotBlank(alarmContent)) {
            throw new IllegalArgumentException(alarmContent + "，请勿操作");
        }
        // 指令协议
        CommandEnum commandEnum = CommandEnum.FORWARD;
        // 操作内容
        String operationContent = commandEnum.getOperationContentTemplate();
        // 批量发送指令
        sendCommandBatch(dto, operationContent, commandEnum, null, userId, createBy);
        return true;
    }

    @Override
    public boolean saveBackup(BaseCommandDTO dto) {
        BladeUser user = AuthUtil.getUser();
        Long userId = user.getUserId();
        String createBy = user.getAccount();
        // 权限校验
        commandValidateService.commandAuth(userId);
        // 查询系统配置信息
        SystemSet systemSet = systemSetService.getOne(Wrappers.<SystemSet>lambdaQuery()
                .orderByDesc(SystemSet::getId).last("limit 1"));
        // 机器人运行条件校验
        commandValidateService.runCondition(dto.getRobotRegionalId(), dto.getRobotIds(), systemSet);
        // 校验风速、湿度告警
        Integer alarmValue = commandValidateService.environmentAlarm(systemSet);
        String alarmContent = WeatherStationAlarmTypeEnum.translationValue(alarmValue);
        if (StrUtil.isNotBlank(alarmContent)) {
            throw new IllegalArgumentException(alarmContent + "，请勿操作");
        }
        // 指令协议
        CommandEnum commandEnum = CommandEnum.BACKUP;
        // 操作内容
        String operationContent = commandEnum.getOperationContentTemplate();
        // 批量发送指令
        sendCommandBatch(dto, operationContent, commandEnum, null, userId, createBy);
        return true;
    }

    @Override
    public boolean saveStop(BaseCommandDTO dto) {
        BladeUser user = AuthUtil.getUser();
        Long userId = user.getUserId();
        String createBy = user.getAccount();
        // 权限校验
        commandValidateService.commandAuth(userId);
        // 指令协议
        CommandEnum commandEnum = CommandEnum.STOP;
        // 操作内容
        String operationContent = commandEnum.getOperationContentTemplate();
        // 批量发送指令
        sendCommandBatch(dto, operationContent, commandEnum, null, userId, createBy);
        return true;
    }

    @Override
    public boolean saveReset(BaseCommandDTO dto) {
        BladeUser user = AuthUtil.getUser();
        Long userId = user.getUserId();
        String createBy = user.getAccount();
        // 权限校验
        commandValidateService.commandAuth(userId);
        // 查询系统配置信息
        SystemSet systemSet = systemSetService.getOne(Wrappers.<SystemSet>lambdaQuery()
                .orderByDesc(SystemSet::getId).last("limit 1"));
        // 机器人运行条件校验
        commandValidateService.runCondition(dto.getRobotRegionalId(), dto.getRobotIds(), systemSet);
        // 校验风速、湿度告警
        Integer alarmValue = commandValidateService.environmentAlarm(systemSet);
        String alarmContent = WeatherStationAlarmTypeEnum.translationValue(alarmValue);
        if (StrUtil.isNotBlank(alarmContent)) {
            throw new IllegalArgumentException(alarmContent + "，请勿操作");
        }
        // 指令协议
        CommandEnum commandEnum = CommandEnum.RESET;
        // 操作内容
        String operationContent = commandEnum.getOperationContentTemplate();
        // 批量发送指令
        sendCommandBatch(dto, operationContent, commandEnum, null, userId, createBy);
        return true;
    }

    @Override
    public boolean saveRestart(BaseCommandDTO dto) {
        BladeUser user = AuthUtil.getUser();
        Long userId = user.getUserId();
        String createBy = user.getAccount();
        // 权限校验
        commandValidateService.commandAuth(userId);
        // 指令协议
        CommandEnum commandEnum = CommandEnum.RESTART;
        // 操作内容
        String operationContent = commandEnum.getOperationContentTemplate();
        // 批量发送指令
        sendCommandBatch(dto, operationContent, commandEnum, null, userId, createBy);
        return true;
    }

    @Override
    public boolean setDaytimePreventClean(BaseCommandDTO dto) {
        BladeUser user = AuthUtil.getUser();
        Long userId = user.getUserId();
        String createBy = user.getAccount();
        // 权限校验
        commandValidateService.commandAuth(userId);
        // 指令密码校验
        commandValidateService.commandPassword(dto.getPassword());
        // 指令协议
        CommandEnum commandEnum = CommandEnum.DAYTIME_PREVENT_CLEAN;
        // 数据域-参数
        StringBuilder value = new StringBuilder();
        // 白天防误扫开关
        value.append(NumberUtil.decToHex(dto.getValue(), 2));
        // 操作内容
        String operationContentTemplate = commandEnum.getOperationContentTemplate();
        String operationContent = StrUtil.format(operationContentTemplate
                , ControlSwitchEnum.translationValue(dto.getValue()));
        // 批量发送指令
        sendCommandBatch(dto, operationContent, commandEnum, value, userId, createBy);
        return true;
    }

    @Override
    public boolean setMulticastGroupDaytimePreventClean(BaseCommandDTO dto) {
        BladeUser user = AuthUtil.getUser();
        Long userId = user.getUserId();
        String createBy = user.getAccount();
        // 权限校验
        commandValidateService.commandAuth(userId);
        // 指令密码校验
        commandValidateService.commandPassword(dto.getPassword());
        // 组播时间间隔校验
        commandValidateService.multicastGroupInterval();
        // 指令协议
        CommandEnum commandEnum = CommandEnum.DAYTIME_PREVENT_CLEAN;
        // 数据域-参数
        StringBuilder value = new StringBuilder();
        // 白天防误扫开关
        value.append(NumberUtil.decToHex(dto.getValue(), 2));
        // 操作内容
        String operationContentTemplate = commandEnum.getOperationContentTemplate();
        String operationContent = StrUtil.format(operationContentTemplate
                , ControlSwitchEnum.translationValue(dto.getValue()));
        // 设置广播参数，并发送指令
        setMulticastGroupParamConfigAndSend(dto, commandEnum, value.toString(), operationContent, userId, createBy);
        return true;
    }

    @Override
    public boolean saveLicenseAuth(LicenseAuthDTO dto) {
        Assert.isFalse(Objects.isNull(dto.getRobotRegionalId()) && StrUtil.isBlank(dto.getRobotIds()), "缺少机器人参数");
        BladeUser user = AuthUtil.getUser();
        Long userId = user.getUserId();
        String createBy = user.getAccount();
        // 权限校验
        commandValidateService.commandAuth(userId);
        // 指令密码校验
        commandValidateService.commandPassword(dto.getPassword());
        // 机器人列表
        List<Robot> robotList = robotMapper.selectList(Wrappers.<Robot>lambdaQuery()
                .eq(Objects.nonNull(dto.getRobotRegionalId()), Robot::getRobotRegionalId, dto.getRobotRegionalId())
                .in(StrUtil.isNotBlank(dto.getRobotIds()), Robot::getId, Func.toLongList(dto.getRobotIds())));
        Assert.isTrue(CollUtil.isNotEmpty(robotList), "机器人不存在");
        // 校验机器人升级状态
        commandValidateService.robotUpgradeing(robotList);
        // 未授权、永久授权，不能重复操作
        if (LicenseAuthEnum.UN_AUTH.getValue().equals(dto.getAuthType())
                || LicenseAuthEnum.PERMANENT_AUTH.getValue().equals(dto.getAuthType())) {
            Optional<Robot> first = robotList.stream()
                    .filter(robot -> dto.getAuthType().equals(robot.getLicenseAuth())).findFirst();
            if (first.isPresent()) {
                throw new IllegalArgumentException(StrUtil.format("机器人{}当前处于{}，请勿重复操作"
                        , first.get().getRobotNumber()
                        , LicenseAuthEnum.translationValue(dto.getAuthType())));
            }
        }
        // 值参数（未授权、永久授权，默认参数）
        String valueParam = "0000";
        // 临时授权、永久授权，需要校验：授权数量
        //License license = null;
        if (!LicenseAuthEnum.UN_AUTH.getValue().equals(dto.getAuthType())) {
            // 获取对应 license
            //license = licenseService.getLicense(dto);
            // 获取系统配置的：临时 license 授权数量
            SystemSet systemSet = SystemCache.getById(CommonConstant.SYSTEM_CONFIG_ID);
            // 默认 10 次
            Integer tempLicenseAuthCount = 10;
            if (Objects.nonNull(systemSet) && Objects.nonNull(systemSet.getTempLicenseAuthCount())) {
                tempLicenseAuthCount = systemSet.getTempLicenseAuthCount();
            }
            valueParam = NumberUtil.decToHex(tempLicenseAuthCount, 4);
        }
        //if (Objects.nonNull(license) && Objects.nonNull(license.getAuthCount())) {
        //    Assert.isTrue(license.getAuthCount() >= robotList.size(), "剩余授权数量不足");
        //}
        // 指令协议
        CommandEnum commandEnum = CommandEnum.LICENSE_AUTH;
        // 操作内容
        String operationContentTemplate = commandEnum.getOperationContentTemplate();
        String operationContent = StrUtil.format(operationContentTemplate
                , LicenseAuthEnum.translationValue(dto.getAuthType()));
        // 循环下发指令
        for (Robot robot : robotList) {
            // 临时授权：需要校验单个机器人的授权限制数量
            //if (LicenseAuthEnum.TEMP_AUTH.getValue().equals(dto.getAuthType())) {
            //    licenseService.validateRobotAuth(robot, license);
            //}
            // 指令参数
            CommandParamDTO commandParam = new CommandParamDTO();
            commandParam.setOperationTitle(commandEnum.getName());
            commandParam.setOperationContent(operationContent);
            commandParam.setUserId(userId);
            commandParam.setCreateBy(createBy);
            commandParam.setCommandEnum(commandEnum);
            commandParam.setValue("0" + dto.getAuthType() + valueParam);
            // 控制streamId
            String controlStreamId = StreamIdUtil.getControlStreamId(robot.getEui(), dto.getStreamId());
            commandParam.setControlStreamId(controlStreamId);
            sendCommand(robot, commandParam);
        }
        return true;
    }

    @Override
    public boolean saveRobotUpgrade(RobotUpgradeDTO dto) throws IOException {
        // 文件校验
        Assert.notNull(dto.getFile(), "文件不能为空");
        Assert.isTrue(dto.getFile().getOriginalFilename().toLowerCase().endsWith(".bin")
                , "文件格式不正确，请上传正确的文件");
        Assert.notBlank(dto.getRobotIds(), "缺少机器人参数");
        // 当前用户id
        BladeUser user = AuthUtil.getUser();
        Long userId = user.getUserId();
        String createBy = user.getAccount();
        // 权限校验
        commandValidateService.commandAuth(userId);
        // 机器人列表
        List<Long> robotIdList = Func.toLongList(dto.getRobotIds());
        List<Robot> robotList = robotMapper.selectBatchIds(robotIdList);
        Assert.isTrue(CollUtil.isNotEmpty(robotList), "机器人不存在");
        // 校验机器人离线状态
        //commandValidateService.robotOffline(robotList);
        // 校验机器人升级状态
        commandValidateService.robotUpgradeing(robotList);
        // 读取文件内容
        byte[] bytes = dto.getFile().getBytes();
        // base64编码
        String base64String = Base64.getEncoder().encodeToString(bytes);
        // 16进制字符串
        String hexString = NumberUtil.base64ToHexStr(base64String);
        // 80 长度切分
        List<String> hexStringList = new ArrayList<>();
        int length = 80;
        if (hexString.length() % length == 0) {
            for (int i = 0; i < hexString.length(); i += length) {
                hexStringList.add(hexString.substring(i, i + length));
            }
        } else {
            int n = hexString.length() - hexString.length() % length;
            for (int i = 0; i < n; i += length) {
                hexStringList.add(hexString.substring(i, i + length));
            }
            hexStringList.add(hexString.substring(n));
        }
        // 文件大小
        long size = dto.getFile().getSize();
        String fileSize = String.format("%08x", (int) size);
        // 批量升级
        robotUpgradeService.upgradeBatch(robotList, hexStringList, fileSize, userId, createBy);
        return true;
    }

    @Override
    public void syncTimeMulticastGroup() {
        List<Robot> robotList = robotMapper.selectList(Wrappers.lambdaQuery());
        if (CollUtil.isEmpty(robotList)) {
            return;
        }
        // 校验机器人升级状态
        commandValidateService.robotUpgradeing(robotList);
        // 发送广播指令
        commandHandler.asyncMulticastGroup(false);
    }

    @Override
    public void syncTimeSingleSendBroadCast(Robot robot, String commandId, String streamId) {
        // 校时成功后，获取当前时间（已经根据时区转换）
        LocalDateTime timeZoneTime = CommonUtil.getZoneTime();
        // 拼接广播协议参数数据
        String value = ControlUtil.concatBoardcastParamData(false, robot);
        // 组装指令参数
        CommandEnum commandEnum = CommandEnum.getById(commandId);
        if (Objects.isNull(commandEnum)) {
            return;
        }
        String commandTemplate = commandEnum.getCommandTemplate();
        // 机器人编号
        String robotNumberHex = NumberUtil.decToHex(robot.getRobotNumber(), 4);
        // 获取帧计数器
        String frameCounterHex = StreamIdUtil.getControlFrameCounter(robot.getEui(), streamId);
        // 数据域长度
        String valueLengthHex = ControlUtil.getDataDomainLengthHex(value);
        // 校验位前置条件：需要校验的字符串
        String checkStr = StrUtil.format(commandTemplate.split(StrUtil.COMMA)[0], robotNumberHex, frameCounterHex, valueLengthHex, value);
        // 校验位
        String checkSum = NumberUtil.getCheckSum(checkStr);
        String commandContent = StrUtil.format(commandTemplate, robotNumberHex, frameCounterHex, valueLengthHex, value, checkSum);
        commandContent = commandContent.replace(StrUtil.COMMA, StrUtil.EMPTY).toUpperCase();
        if (CommandEnum.F2.getId().equals(commandId)) {
            commandHandler.asyncReceiveDeviceRequest(robot.getId(), robot.getEui(), commandContent);
        } else {
            // 控制 streamId
            String controlStreamId = StreamIdUtil.getControlStreamId(commandContent);
            // 下发命令：6841 → 6882
            controlStreamId = controlStreamId.replace(StreamIdConstant.CONTROL_REQUEST_PREFIX
                    , StreamIdConstant.CONTROL_RESPONSE_PREFIX);
            CommandParamDTO commandParam = new CommandParamDTO();
            commandParam.setControlStreamId(controlStreamId);
            commandParam.setCommandContent(commandContent);
            long timestamp = System.currentTimeMillis();
            commandParam.setTimestamp(timestamp);
            MulticastGroupEnum syncTime = MulticastGroupEnum.SYNC_TIME;
            String operationTitle = syncTime.getTitle();
            String operationContent = StrUtil.format(syncTime.getContent()
                    , timeZoneTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            // 异步单个下发广播协议指令
            commandHandler.asyncSingleSendBroadcast(robot, commandParam, operationTitle, operationContent);
        }
    }

    @Override
    public void syncTimeSingleSendBroadCast(BaseCommandDTO dto) {
        Assert.notBlank(dto.getRobotIds(), "缺少机器人参数");
        BladeUser user = AuthUtil.getUser();
        Long userId = user.getUserId();
        String createBy = user.getAccount();
        // 权限校验
        commandValidateService.commandAuth(userId);
        // 指令密码校验
        commandValidateService.commandPassword(dto.getPassword());
        // 查询机器人列表
        List<Long> robotIdList = Func.toLongList(dto.getRobotIds());
        List<Robot> robotList = robotMapper.selectBatchIds(robotIdList);
        // 校验机器人升级状态
        commandValidateService.robotUpgradeing(robotList);
        // 校时成功后，获取当前时间（已经根据时区转换）
        LocalDateTime timeZoneTime = CommonUtil.getZoneTime();
        // 组装指令参数
        CommandEnum commandEnum = CommandEnum.BROADCAST_PARAM;
        String commandTemplate = commandEnum.getCommandTemplate();
        // 循环下发指令
        for (Robot robot : robotList) {
            // 机器人编号
            String robotNumberHex = NumberUtil.decToHex(robot.getRobotNumber(), 4);
            // 获取帧计数器
            String frameCounterHex = StreamIdUtil.getControlFrameCounter(robot.getEui(), StreamIdConstant.A8);
            // 拼接广播协议参数数据
            String value = ControlUtil.concatBoardcastParamData(false, robot);
            // 数据域长度
            String valueLengthHex = ControlUtil.getDataDomainLengthHex(value);
            // 校验位前置条件：需要校验的字符串
            String checkStr = StrUtil.format(commandTemplate.split(StrUtil.COMMA)[0], robotNumberHex, frameCounterHex, valueLengthHex, value);
            // 校验位
            String checkSum = NumberUtil.getCheckSum(checkStr);
            String commandContent = StrUtil.format(commandTemplate, robotNumberHex, frameCounterHex, valueLengthHex, value, checkSum);
            commandContent = commandContent.replace(StrUtil.COMMA, StrUtil.EMPTY).toUpperCase();
            // 控制 streamId
            String controlStreamId = StreamIdUtil.getControlStreamId(commandContent);
            // 下发命令：6841 → 6882
            controlStreamId = controlStreamId.replace(StreamIdConstant.CONTROL_REQUEST_PREFIX
                    , StreamIdConstant.CONTROL_RESPONSE_PREFIX);
            MulticastGroupEnum syncTime = MulticastGroupEnum.SYNC_TIME;
            String operationTitle = syncTime.getTitle();
            String operationContent = StrUtil.format(syncTime.getContent()
                    , timeZoneTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            CommandParamDTO commandParam = new CommandParamDTO();
            commandParam.setControlStreamId(controlStreamId);
            commandParam.setCommandContent(commandContent);
            commandParam.setUserId(userId);
            commandParam.setCreateBy(createBy);
            long timestamp = System.currentTimeMillis();
            commandParam.setTimestamp(timestamp);
            // 异步单个下发广播协议指令
            commandHandler.asyncSingleSendBroadcast(robot, commandParam, operationTitle, operationContent);
        }
    }

    @Override
    public void windSpeedAlarm(List<WeatherStation> weatherStationList, SystemSet systemSet) {
        BigDecimal windSpeedThreshold = systemSet.getWindSpeedThreshold();
        if (Objects.isNull(windSpeedThreshold)) {
            return;
        }
        // weatherStationList 中风速如果有空值，则不处理
        if (weatherStationList.stream().anyMatch(d -> Objects.isNull(d.getWindSpeed()))) {
            return;
        }
        // 校验机器人升级状态，有升级中机器人，不进行处理
        Long robotUpgradeing = robotMapper.selectCount(Wrappers.<Robot>lambdaQuery()
                .eq(Robot::getUpgradeStatus, UpgradeStatusEnum.UPGRADEING.getValue()));
        if (robotUpgradeing > 0) {
            return;
        }
        // 告警，list 中每个风速数据都超过设置的阈值
        boolean alarmFlag = weatherStationList.stream()
                .allMatch(d -> alarmService.validateWindSpeedAlarm(d.getWindSpeed(), windSpeedThreshold));
        if (alarmFlag) {
            windSpeedAlarmMulticastGroup(AlarmStatusExtEnum.ALARM.getValue());
        } else {
            // 解除告警，list 中每个风速数据都未超过设置的阈值
            boolean relieveFlag = weatherStationList.stream()
                    .noneMatch(d -> alarmService.validateWindSpeedAlarm(d.getWindSpeed(), windSpeedThreshold));
            if (relieveFlag) {
                windSpeedAlarmMulticastGroup(AlarmStatusExtEnum.NORMAL.getValue());
            }
        }
    }

    @Override
    public void windSpeedAlarm(Long weatherStationId, BigDecimal windSpeed, BigDecimal windSpeedThreshold) {
        if (Objects.isNull(weatherStationId) || Objects.isNull(windSpeed)) {
            return;
        }
        int environmentDataCacheCount = CommandConstant.ENVIRONMENT_DATA_CACHE_COUNT;
        // 气象站统计类数据（风速）
        WeatherStation weatherStation = new WeatherStation();
        weatherStation.setId(weatherStationId);
        weatherStation.setWindSpeed(windSpeed);
        List<WeatherStation> weatherStationList;
        // 获取缓存中的风速数据
        Object windSpeedObj = bladeRedis.get(CacheNames.WIND_SPEED_LAST5);
        if (Objects.isNull(windSpeedObj)) {
            weatherStationList = new ArrayList<>(environmentDataCacheCount);
            weatherStationList.add(weatherStation);
            // 更新风速数据缓存
            bladeRedis.set(CacheNames.WIND_SPEED_LAST5, JsonUtil.toJson(weatherStationList));
        } else {
            // 满足 5 条数据才触发
            weatherStationList = JsonUtil.parseArray(windSpeedObj.toString(), WeatherStation.class);
            weatherStationList.add(weatherStation);
            if (weatherStationList.size() >= environmentDataCacheCount) {
                // 只保存最近5次数据
                if (weatherStationList.size() > environmentDataCacheCount) {
                    weatherStationList.remove(0);
                }
                // 更新风速数据缓存
                bladeRedis.set(CacheNames.WIND_SPEED_LAST5, JsonUtil.toJson(weatherStationList));
                // 系统配置的风速阈值
                if (Objects.isNull(windSpeedThreshold)) {
                    return;
                }
                // 校验机器人升级状态，有升级中机器人，不进行处理
                Long robotUpgradeing = robotMapper.selectCount(Wrappers.<Robot>lambdaQuery()
                        .eq(Robot::getUpgradeStatus, UpgradeStatusEnum.UPGRADEING.getValue()));
                if (robotUpgradeing > 0) {
                    return;
                }
                // 告警，list 中每个风速数据都超过设置的阈值
                boolean alarmFlag = weatherStationList.stream()
                        .allMatch(d -> alarmService.validateWindSpeedAlarm(d.getWindSpeed(), windSpeedThreshold));
                if (alarmFlag) {
                    windSpeedAlarmMulticastGroup(AlarmStatusExtEnum.ALARM.getValue());
                } else {
                    // 解除告警，list 中每个风速数据都未超过设置的阈值
                    boolean relieveFlag = weatherStationList.stream()
                            .noneMatch(d -> alarmService.validateWindSpeedAlarm(d.getWindSpeed(), windSpeedThreshold));
                    if (relieveFlag) {
                        windSpeedAlarmMulticastGroup(AlarmStatusExtEnum.NORMAL.getValue());
                    }
                }
            } else {
                // 更新风速数据缓存
                bladeRedis.set(CacheNames.WIND_SPEED_LAST5, JsonUtil.toJson(weatherStationList));
            }
        }
    }

    @Override
    public void temperatureAlarm(List<WeatherStation> weatherStationList, SystemSet systemSet) {
        // weatherStationList 中温度如果有空值，则不处理
        if (weatherStationList.stream().anyMatch(d -> Objects.isNull(d.getTemperature()))) {
            return;
        }
        // 校验机器人升级状态，有升级中机器人，不进行处理
        Long robotUpgradeing = robotMapper.selectCount(Wrappers.<Robot>lambdaQuery()
                .eq(Robot::getUpgradeStatus, UpgradeStatusEnum.UPGRADEING.getValue()));
        if (robotUpgradeing > 0) {
            return;
        }
        // 告警，list 中每个风速数据都超过设置的阈值
        boolean alarmFlag = weatherStationList.stream()
                .allMatch(d -> alarmService.validateTemperatureAlarm(d.getTemperature(), systemSet));
        if (alarmFlag) {
            temperatureAlarmMulticastGroup(AlarmStatusExtEnum.ALARM.getValue());
        } else {
            boolean relieveFlag = weatherStationList.stream()
                    .noneMatch(d -> alarmService.validateTemperatureAlarm(d.getTemperature(), systemSet));
            if (relieveFlag) {
                temperatureAlarmMulticastGroup(AlarmStatusExtEnum.NORMAL.getValue());
            }
        }
    }

    /**
     * 风速告警多播组
     *
     * @param alarmStatus
     * @return
     */
    private void windSpeedAlarmMulticastGroup(Integer alarmStatus) {
        Object windSpeedAlarmObj = bladeRedis.get(CacheNames.WIND_SPEED_ALARM);
        // 告警，查询是否已经触发过告警，已触发过，不进行处理（避免重复告警）
        if (AlarmStatusExtEnum.ALARM.getValue().equals(alarmStatus)) {
            if (Objects.nonNull(windSpeedAlarmObj)
                    && AlarmStatusExtEnum.ALARM.getValue().equals(Func.toInt(windSpeedAlarmObj))) {
                return;
            }
        } else {
            // 解除告警，查询是否已经触发过告警，未触发，不进行处理（没有告警，不需要解除）
            if (Objects.isNull(windSpeedAlarmObj)
                    || AlarmStatusExtEnum.NORMAL.getValue().equals(Func.toInt(windSpeedAlarmObj))) {
                return;
            }
        }
        // 更新告警标识
        bladeRedis.set(CacheNames.WIND_SPEED_ALARM, alarmStatus);
        // 发送广播指令
        commandHandler.asyncMulticastGroup(true);
    }

    /**
     * 温度告警多播组
     *
     * @param alarmStatus
     * @return
     */
    private void temperatureAlarmMulticastGroup(Integer alarmStatus) {
        Object windSpeedAlarmObj = bladeRedis.get(CacheNames.TEMPERATURE_ALARM);
        // 告警，查询是否已经触发过告警，已触发过，不进行处理（避免重复告警）
        if (AlarmStatusExtEnum.ALARM.getValue().equals(alarmStatus)) {
            if (Objects.nonNull(windSpeedAlarmObj)
                    && AlarmStatusExtEnum.ALARM.getValue().equals(Func.toInt(windSpeedAlarmObj))) {
                return;
            }
        } else {
            // 解除告警，查询是否已经触发过告警，未触发，不进行处理（没有告警，不需要解除）
            if (Objects.isNull(windSpeedAlarmObj)
                    || AlarmStatusExtEnum.NORMAL.getValue().equals(Func.toInt(windSpeedAlarmObj))) {
                return;
            }
        }
        // 更新告警标识
        bladeRedis.set(CacheNames.TEMPERATURE_ALARM, alarmStatus);
        // 发送广播指令
        commandHandler.asyncMulticastGroup(true);
    }

    @Override
    public void sendTimerStartRequest(Robot robot, String frameCounterHex, Timer timer) {
        // 启动运行标志，0-不能启动；1-可以启动
        int startFlag = ControlSwitchEnum.CLOSE.getValue();
        // 校验定时器是否可以执行
        boolean validate = commandValidateService.timerExec(robot, timer);
        if (validate) {
            startFlag = ControlSwitchEnum.OPEN.getValue();
            // 根据定时器编号，对应解除定时器异常的告警
            JSONObject json = JSONUtil.createObj();
            json.set(RobotDataConstant.ROBOT, JSONUtil.toJsonStr(robot));
            json.set(RobotDataConstant.STREAM_ID, StreamIdConstant.E6);
            json.set(RobotDataConstant.ALARM_STATUS, ControlSwitchEnum.CLOSE.getValue());
            // 发送到告警数据队列
            String routingKey = RabbitMqConfig.KEY_REAL_ALARM;
            rabbitMqService.sendMessage(routingKey, json);
        } else {
            bladeRedis.hSet(CacheNames.ROBOT_NOT_START_STATUS, robot.getId(), startFlag);
        }
        // 发送启动请求
        sendStartRequest(robot, CommandEnum.F0.getId(), frameCounterHex, startFlag);
    }

    @Override
    public void sendRobotStartRequest(Robot robot, String frameCounterHex) {
        // 启动运行标志，0-不能启动；1-可以启动
        int startFlag = ControlSwitchEnum.CLOSE.getValue();
        // 校验定时器是否可以执行
        boolean validate = commandValidateService.robotExec(robot);
        if (validate) {
            startFlag = ControlSwitchEnum.OPEN.getValue();
        } else {
            bladeRedis.hSet(CacheNames.ROBOT_NOT_START_STATUS, robot.getId(), startFlag);
        }
        // 发送启动请求
        sendStartRequest(robot, CommandEnum.F1.getId(), frameCounterHex, startFlag);
    }

    @Override
    public void robotNotStartReceive(Robot robot) {
        CommandEnum commandEnum = CommandEnum.E7;
        String commandTemplate = commandEnum.getCommandTemplate();
        // 获取帧计数器
        String frameCounterHex = StreamIdUtil.getControlFrameCounter(robot.getEui(), StreamIdConstant.E7);
        // 指令内容
        String value = ControlUtil.concatBoardcastParamData(false, robot);
        // 数据域长度
        String valueLengthHex = ControlUtil.getDataDomainLengthHex(value);
        // 机器人编号，并转换为16进制
        String robotNumberHex = NumberUtil.decToHex(robot.getRobotNumber(), 4);
        // 校验位前置条件：需要校验的字符串
        String checkStr = StrUtil.format(commandTemplate.split(StrUtil.COMMA)[0]
                , robotNumberHex, frameCounterHex, valueLengthHex, value);
        // 校验位
        String checkSum = NumberUtil.getCheckSum(checkStr);
        String commandContent = StrUtil.format(commandTemplate, robotNumberHex, frameCounterHex, valueLengthHex, value, checkSum);
        // 替换指令内容中的逗号为空，并转为大写
        commandContent = commandContent.replace(StrUtil.COMMA, StrUtil.EMPTY).toUpperCase();
        // 下发指令
        commandHandler.asyncReceiveDeviceRequest(robot.getId(), robot.getEui(), commandContent);
    }

    @Override
    public void refreshTime(BaseCommandDTO dto) {
        BladeUser user = AuthUtil.getUser();
        Long userId = user.getUserId();
        String createBy = user.getAccount();
        // 权限校验
        commandValidateService.commandAuth(userId);
        // 指令密码校验
        commandValidateService.commandPassword(dto.getPassword());
        // 指令协议
        CommandEnum commandEnum = CommandEnum.C2;
        // 操作内容
        String operationContent = "机器人刷新时间";
        // 批量发送指令
        sendCommandBatch(dto, operationContent, commandEnum, null, userId, createBy);
    }

    @Override
    public void refreshBaseData(BaseCommandDTO dto) {
        BladeUser user = AuthUtil.getUser();
        Long userId = user.getUserId();
        String createBy = user.getAccount();
        // 权限校验
        commandValidateService.commandAuth(userId);
        // 指令密码校验
        commandValidateService.commandPassword(dto.getPassword());
        // 指令协议
        CommandEnum commandEnum = CommandEnum.C4_REFRESH_BASE_DATA;
        // 操作内容
        String operationContent = commandEnum.getOperationContentTemplate();
        // 批量发送指令
        sendCommandBatch(dto, operationContent, commandEnum, null, userId, createBy);
    }

    @Override
    public void refreshParamData(BaseCommandDTO dto) {
        Assert.isFalse(Objects.isNull(dto.getRobotRegionalId()) && StrUtil.isBlank(dto.getRobotIds()), "缺少机器人参数");
        BladeUser user = AuthUtil.getUser();
        Long userId = user.getUserId();
        String createBy = user.getAccount();
        // 权限校验
        commandValidateService.commandAuth(userId);
        // 指令密码校验
        commandValidateService.commandPassword(dto.getPassword());
        // 机器人列表
        List<Robot> robotList = robotMapper.selectList(Wrappers.<Robot>lambdaQuery()
                .eq(Objects.nonNull(dto.getRobotRegionalId()), Robot::getRobotRegionalId, dto.getRobotRegionalId())
                .in(StrUtil.isNotBlank(dto.getRobotIds()), Robot::getId, Func.toLongList(dto.getRobotIds())));
        Assert.isTrue(CollUtil.isNotEmpty(robotList), "机器人不存在");
        // 校验机器人升级状态
        commandValidateService.robotUpgradeing(robotList);
        // 下发：C0、C1、C4 指令
        List<Integer> cList = Arrays.asList(0, 1, 4);
        robotList.forEach(robot -> {
            // 指令参数
            CommandParamDTO commandParam = new CommandParamDTO();
            commandParam.setOperationTitle("机器人刷新参数数据");
            commandParam.setOperationContent("机器人刷新参数数据");
            commandParam.setUserId(userId);
            commandParam.setCreateBy(createBy);
            // 下发指令
            commandHandler.asyncRefreshDeviceData(robot, commandParam, cList);
        });
    }

    @Override
    public void refreshCommonData(BaseCommandDTO dto) {
        Assert.isFalse(Objects.isNull(dto.getRobotRegionalId()) && StrUtil.isBlank(dto.getRobotIds()), "缺少机器人参数");
        BladeUser user = AuthUtil.getUser();
        Long userId = user.getUserId();
        String createBy = user.getAccount();
        // 权限校验
        commandValidateService.commandAuth(userId);
        // 指令密码校验
        //commandValidateService.commandPassword(dto.getPassword());
        // 机器人列表
        List<Robot> robotList = robotMapper.selectList(Wrappers.<Robot>lambdaQuery()
                .eq(Objects.nonNull(dto.getRobotRegionalId()), Robot::getRobotRegionalId, dto.getRobotRegionalId())
                .in(StrUtil.isNotBlank(dto.getRobotIds()), Robot::getId, Func.toLongList(dto.getRobotIds())));
        Assert.isTrue(CollUtil.isNotEmpty(robotList), "机器人不存在");
        // 校验机器人升级状态
        commandValidateService.robotUpgradeing(robotList);
        // 下发：C4 指令
        List<Integer> cList = Arrays.asList(4);
        robotList.forEach(robot -> {
            // 指令参数
            CommandParamDTO commandParam = new CommandParamDTO();
            commandParam.setOperationTitle("机器人刷新常用数据");
            commandParam.setOperationContent("机器人刷新常用数据");
            commandParam.setUserId(userId);
            commandParam.setCreateBy(createBy);
            // 下发指令
            commandHandler.asyncRefreshDeviceData(robot, commandParam, cList);
        });
    }

    @Override
    public boolean multicastGroupUpgrade(RobotUpgradeDTO dto) throws IOException {
        // 文件校验
        Assert.notNull(dto.getFile(), "文件不能为空");
        Assert.isTrue(dto.getFile().getOriginalFilename().toLowerCase().endsWith(".bin")
                , "文件格式不正确，请上传正确的文件");
        // 当前用户id
        Long userId = AuthUtil.getUserId();
        // 权限校验
        commandValidateService.commandAuth(userId);
        // 组播时间间隔校验
        commandValidateService.multicastGroupInterval();
        // 读取文件内容
        byte[] bytes = dto.getFile().getBytes();
        // base64编码
        String base64String = Base64.getEncoder().encodeToString(bytes);
        // 16进制字符串
        String hexString = NumberUtil.base64ToHexStr(base64String);
        // 80 长度切分
        List<String> hexStringList = new ArrayList<>();
        int length = 80;
        if (hexString.length() % length == 0) {
            for (int i = 0; i < hexString.length(); i += length) {
                hexStringList.add(hexString.substring(i, i + length));
            }
        } else {
            int n = hexString.length() - hexString.length() % length;
            for (int i = 0; i < n; i += length) {
                hexStringList.add(hexString.substring(i, i + length));
            }
            hexStringList.add(hexString.substring(n));
        }
        // 文件大小
        long size = dto.getFile().getSize();
        String fileSize = String.format("%08x", (int) size);
        // 组播升级
        robotUpgradeService.multicastGroupUpgrade(hexStringList, fileSize);
        return true;
    }

    /**
     * 批量发送指令
     *
     * @param dto              基础指令参数
     * @param operationContent 操作内容
     * @param commandEnum      指令枚举
     * @param value            指令值（16 进制）
     * @return void
     */
    private void sendCommandBatch(BaseCommandDTO dto, String operationContent
            , CommandEnum commandEnum, StringBuilder value, Long userId, String createBy) {
        Assert.isFalse(Objects.isNull(dto.getRobotRegionalId()) && StrUtil.isBlank(dto.getRobotIds()), "缺少机器人参数");
        // 机器人列表
        List<Robot> robotList = robotMapper.selectList(Wrappers.<Robot>lambdaQuery()
                .eq(Objects.nonNull(dto.getRobotRegionalId()), Robot::getRobotRegionalId, dto.getRobotRegionalId())
                .in(StrUtil.isNotBlank(dto.getRobotIds()), Robot::getId, Func.toLongList(dto.getRobotIds())));
        Assert.isTrue(CollUtil.isNotEmpty(robotList), "机器人不存在");
        // 校验机器人升级状态
        commandValidateService.robotUpgradeing(robotList);
        // 指令streamId
        String streamId = dto.getStreamId();
        if (StreamIdUtil.validateNeedGatewayAndRobotCount(streamId)) {
            value = Objects.isNull(value) ? new StringBuilder() : value;
            value.append(ControlUtil.getGatewayAndRobotCount());
        }
        StringBuilder finalValue = value;
        robotList.forEach(robot -> {
            // 指令参数
            CommandParamDTO commandParam = new CommandParamDTO();
            commandParam.setOperationTitle(commandEnum.getName());
            commandParam.setOperationContent(operationContent);
            commandParam.setUserId(userId);
            commandParam.setCreateBy(createBy);
            commandParam.setCommandEnum(commandEnum);
            commandParam.setValue(Objects.isNull(finalValue) ? null : finalValue.toString());
            // 控制streamId
            String controlStreamId = StreamIdUtil.getControlStreamId(robot.getEui(), streamId);
            commandParam.setControlStreamId(controlStreamId);
            // 发送指令
            sendCommand(robot, commandParam);
        });
    }

    /**
     * 组装指令内容并发送
     *
     * @param robot        机器人
     * @param commandParam 指令参数
     * @param license      授权
     * @return void
     */
    private void sendCommand(Robot robot, CommandParamDTO commandParam) {
        // 指令参数
        String controlStreamId = commandParam.getControlStreamId();
        CommandEnum commandEnum = commandParam.getCommandEnum();
        String value = commandParam.getValue();
        // 指令模板
        String commandTemplate = commandEnum.getCommandTemplate();
        // 机器人编号，并转换为16进制
        String robotNumber = robot.getRobotNumber();
        String robotNumberHex = NumberUtil.decToHex(robotNumber, 4);
        // 获取帧计数器
        String frameCounterHex = StreamIdUtil.getFrameCounterByControlStreamId(controlStreamId);
        // 数据域长度
        String valueLengthHex = ControlUtil.getDataDomainLengthHex(value);
        // 校验位前置条件：需要校验的字符串
        String checkStr = StrUtil.format(commandTemplate.split(StrUtil.COMMA)[0]
                , robotNumberHex, frameCounterHex, valueLengthHex, value);
        // 校验位
        String checkSum = NumberUtil.getCheckSum(checkStr);
        // 命令内容
        String commandContent;
        if (StrUtil.isBlank(value)) {
            commandContent = StrUtil.format(commandTemplate, robotNumberHex, frameCounterHex, valueLengthHex, checkSum);
        } else {
            commandContent = StrUtil.format(commandTemplate, robotNumberHex, frameCounterHex, valueLengthHex, value, checkSum);
        }
        // 替换指令内容中的逗号为空，并转为大写
        commandContent = commandContent.replace(StrUtil.COMMA, StrUtil.EMPTY).toUpperCase();
        commandParam.setCommandContent(commandContent);
        // 指令下发的系统时间
        commandParam.setTimestamp(System.currentTimeMillis());
        // 指令结果处理，判断指令是否执行成功，并记录操作日志
        commandHandler.asyncSendCommand(robot, commandParam);
    }

    /**
     * 发送启动请求
     *
     * @param robot           机器人
     * @param commandId       命令编号
     * @param frameCounterHex 帧计数器
     * @param startFlag       启动运行标志
     */
    private void sendStartRequest(Robot robot, String commandId, String frameCounterHex, int startFlag) {
        String startFlagHex = NumberUtil.decToHex(startFlag, 2);
        CommandEnum commandEnum = CommandEnum.getById(commandId);
        if (Objects.isNull(commandEnum)) {
            return;
        }
        String commandTemplate = commandEnum.getCommandTemplate();
        // 指令内容
        String value = ControlUtil.getStartRequestCommandContent(startFlagHex, robot);
        // 数据域长度
        String valueLengthHex = ControlUtil.getDataDomainLengthHex(value);
        // 机器人编号，并转换为16进制
        String robotNumberHex = NumberUtil.decToHex(robot.getRobotNumber(), 4);
        // 校验位前置条件：需要校验的字符串
        String checkStr = StrUtil.format(commandTemplate.split(StrUtil.COMMA)[0]
                , robotNumberHex, frameCounterHex, valueLengthHex, value);
        // 校验位
        String checkSum = NumberUtil.getCheckSum(checkStr);
        String commandContent = StrUtil.format(commandTemplate, robotNumberHex, frameCounterHex, valueLengthHex, value, checkSum);
        // 替换指令内容中的逗号为空，并转为大写
        commandContent = commandContent.replace(StrUtil.COMMA, StrUtil.EMPTY).toUpperCase();
        // 下发指令
        commandHandler.asyncReceiveDeviceRequest(robot.getId(), robot.getEui(), commandContent);
    }

    /**
     * 设置广播参数，并发送指令
     *
     * @param dto              指令参数
     * @param commandEnum      指令枚举协议
     * @param value            数据域-参数
     * @param operationContent 操作内容
     * @param userId           用户id
     */
    private void setMulticastGroupParamConfigAndSend(BaseCommandDTO dto, CommandEnum commandEnum
            , String value, String operationContent, Long userId, String createBy) {
        // 指令streamId
        String streamId = dto.getStreamId();
        if (StreamIdUtil.validateNeedGatewayAndRobotCount(streamId)) {
            value = StrUtil.isBlank(value) ? "" : value;
            value = value + ControlUtil.getGatewayAndRobotCount();
        }
        // 广播参数
        CommandParamDTO commandParam = new CommandParamDTO();
        // 控制streamId
        String controlStreamId = StreamIdUtil.getMulticastGroupControlStreamId(dto.getStreamId());
        commandParam.setControlStreamId(controlStreamId);
        commandParam.setCommandEnum(commandEnum);
        commandParam.setValue(value);
        commandParam.setOperationTitle(commandEnum.getName());
        commandParam.setOperationContent(operationContent);
        commandParam.setUserId(userId);
        commandParam.setCreateBy(createBy);
        LocalDateTime time = CommonUtil.getZoneTime();
        commandParam.setTime(time);
        Long timestamp = CommonUtil.localDateTimeToTimestamp(time);
        commandParam.setTimestamp(timestamp);
        // 发送广播指令
        commandHandler.asyncMulticastGroupParamConfig(commandParam);
    }

}

package org.springblade.thingcom.command.controller;

import cn.hutool.core.lang.Assert;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springblade.thingcom.command.dto.*;
import org.springblade.thingcom.command.service.RobotCommandService;
import org.springblade.thingcom.communication.constant.StreamIdConstant;
import org.springblade.thingcom.core.chirpStack.service.DeviceLogService;
import org.springblade.thingcom.device.entity.Robot;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.IOException;

/**
 * @ClassName RobotCommandController
 * @Description: 机器人指令控制层
 * @Author: zgq
 * @Date: 2024/1/20 14:57
 * @Version: 1.0
 **/
@RestController
@AllArgsConstructor
@RequestMapping("/robot-command")
public class RobotCommandController extends Blade<PERSON>ontroller {

    private final RobotCommandService robotCommandService;
    private final DeviceLogService deviceLogService;

    /**
     * 机器人编号设置
     *
     * @param robot
     * @return
     */
    @PostMapping("/number/set")
    public R saveNumberSet(@RequestBody Robot robot) {
        return status(robotCommandService.saveNumberSet(robot));
    }

    /**
     * 控制板参数设置
     *
     * @param dto
     * @return
     */
    @PostMapping("/control-board/set")
    public R saveControlBoardSet(@Valid @RequestBody ControlBoardDTO dto) {
        dto.setStreamId(StreamIdConstant.A0);
        return status(robotCommandService.saveControlBoardSet(dto));
    }

    /**
     * 广播设置控制板参数
     *
     * @param dto
     * @return
     */
    @PostMapping("/multicast-group/control-board/set")
    public R saveMulticastGroupControlBoardSet(@Valid @RequestBody ControlBoardDTO dto) {
        dto.setStreamId(StreamIdConstant.A0);
        return status(robotCommandService.saveMulticastGroupControlBoardSet(dto));
    }

    /**
     * 电池参数设置
     *
     * @param dto
     * @return
     */
    @PostMapping("/battery/set")
    public R saveBatterySet(@Valid @RequestBody BatteryDTO dto) {
        dto.setStreamId(StreamIdConstant.A1);
        return status(robotCommandService.saveBatterySet(dto));
    }

    /**
     * 广播设置电池参数
     *
     * @param dto
     * @return
     */
    @PostMapping("/multicast-group/battery/set")
    public R saveMulticastGroupBatterySet(@Valid @RequestBody BatteryDTO dto) {
        dto.setStreamId(StreamIdConstant.A1);
        return status(robotCommandService.saveMulticastGroupBatterySet(dto));
    }

    /**
     * 定时参数设置
     *
     * @param dto
     * @return
     */
    @PostMapping("/timer/set")
    public R saveTimerSet(@Valid @RequestBody TimerDTO dto) {
        dto.setStreamId(StreamIdConstant.A2);
        return status(robotCommandService.saveTimerSet(dto));
    }

    /**
     * 广播设置定时参数
     *
     * @param dto
     * @return
     */
    @PostMapping("/multicast-group/timer/set")
    public R saveMulticastGroupTimerSet(@Valid @RequestBody TimerDTO dto) {
        dto.setStreamId(StreamIdConstant.A2);
        return status(robotCommandService.saveMulticastGroupTimerSet(dto));
    }

    /**
     * 停机位设置
     *
     * @param dto
     * @return
     */
    @PostMapping("/stop-bit/set")
    public R saveStopBitSet(@Valid @RequestBody BaseCommandDTO dto) {
        dto.setStreamId(StreamIdConstant.A3);
        return status(robotCommandService.saveStopBitSet(dto));
    }

    /**
     * 广播设置停机位
     *
     * @param dto
     * @return
     */
    @PostMapping("/multicast-group/stop-bit/set")
    public R saveMulticastGroupStopBitSet(@Valid @RequestBody BaseCommandDTO dto) {
        dto.setStreamId(StreamIdConstant.A3);
        return status(robotCommandService.saveMulticastGroupStopBitSet(dto));
    }

    /**
     * Lora参数设置
     *
     * @param dto
     * @return
     */
    @PostMapping("/lora/set")
    public R saveLoraSet(@Valid @RequestBody LoraDTO dto) {
        dto.setStreamId(StreamIdConstant.A4);
        return status(robotCommandService.saveLoraSet(dto));
    }

    /**
     * 广播设置Lora参数
     *
     * @param dto
     * @return
     */
    @PostMapping("/multicast-group/lora/set")
    public R saveMulticastGroupLoraSet(@Valid @RequestBody LoraDTO dto) {
        dto.setStreamId(StreamIdConstant.A4);
        return status(robotCommandService.saveMulticastGroupLoraSet(dto));
    }

    /**
     * 风速设置
     *
     * @param dto
     * @return
     */
    @PostMapping("/wind/set")
    public R saveWindSet(@Valid @RequestBody WindDTO dto) {
        dto.setStreamId(StreamIdConstant.A5);
        return status(robotCommandService.saveWindSet(dto));
    }

    /**
     * 广播设置风速
     *
     * @param dto
     * @return
     */
    @PostMapping("/multicast-group/wind/set")
    public R saveMulticastGroupWindSet(@Valid @RequestBody WindDTO dto) {
        dto.setStreamId(StreamIdConstant.A5);
        return status(robotCommandService.saveMulticastGroupWindSet(dto));
    }

    /**
     * 解锁
     *
     * @param dto
     * @return
     */
    @PostMapping("/unlock")
    public R saveUnlock(@Valid @RequestBody BaseCommandDTO dto) {
        dto.setStreamId(StreamIdConstant.B0);
        return status(robotCommandService.saveUnlock(dto));
    }

    /**
     * 广播解锁
     *
     * @param dto
     * @return
     */
    @PostMapping("/multicast-group/unlock")
    public R saveMulticastGroupUnlock(@Valid @RequestBody BaseCommandDTO dto) {
        dto.setStreamId(StreamIdConstant.B0);
        return status(robotCommandService.saveMulticastGroupUnlock(dto));
    }

    /**
     * 锁定
     *
     * @param dto
     * @return
     */
    @PostMapping("/lock")
    public R saveLock(@Valid @RequestBody BaseCommandDTO dto) {
        dto.setStreamId(StreamIdConstant.B1);
        return status(robotCommandService.saveLock(dto));
    }

    /**
     * 广播锁定
     *
     * @param dto
     * @return
     */
    @PostMapping("/multicast-group/lock")
    public R saveMulticastGroupLock(@Valid @RequestBody BaseCommandDTO dto) {
        dto.setStreamId(StreamIdConstant.B1);
        return status(robotCommandService.saveMulticastGroupLock(dto));
    }

    /**
     * 主板温度参数设置
     *
     * @param dto
     * @return
     */
    @PostMapping("/mainboard-temperature/set")
    public R saveMainBoardTemperatureSet(@Valid @RequestBody MainBoardTemperatureDTO dto) {
        dto.setStreamId(StreamIdConstant.AE);
        return status(robotCommandService.saveMainBoardTemperatureSet(dto));
    }

    /**
     * 广播设置主板温度参数
     *
     * @param dto
     * @return
     */
    @PostMapping("/multicast-group/mainboard-temperature/set")
    public R saveMulticastGroupMainBoardTemperatureSet(@Valid @RequestBody MainBoardTemperatureDTO dto) {
        dto.setStreamId(StreamIdConstant.AE);
        return status(robotCommandService.saveMulticastGroupMainBoardTemperatureSet(dto));
    }

    /**
     * 启动
     *
     * @param dto
     * @return
     */
    @PostMapping("/start")
    public R saveStart(@Valid @RequestBody StartDTO dto) {
        dto.setStreamId(StreamIdConstant.B2);
        return status(robotCommandService.saveStart(dto));
    }

    /**
     * 前进
     *
     * @param dto
     * @return
     */
    @PostMapping("/forward")
    public R saveForward(@Valid @RequestBody BaseCommandDTO dto) {
        dto.setStreamId(StreamIdConstant.B3);
        return status(robotCommandService.saveForward(dto));
    }

    /**
     * 后退
     *
     * @param dto
     * @return
     */
    @PostMapping("/backup")
    public R saveBackup(@Valid @RequestBody BaseCommandDTO dto) {
        dto.setStreamId(StreamIdConstant.B4);
        return status(robotCommandService.saveBackup(dto));
    }

    /**
     * 停止
     *
     * @param dto
     * @return
     */
    @PostMapping("/stop")
    public R saveStop(@Valid @RequestBody BaseCommandDTO dto) {
        dto.setStreamId(StreamIdConstant.B5);
        return status(robotCommandService.saveStop(dto));
    }

    /**
     * 复位
     *
     * @param dto
     * @return
     */
    @PostMapping("/reset")
    public R saveReset(@Valid @RequestBody BaseCommandDTO dto) {
        dto.setStreamId(StreamIdConstant.B6);
        return status(robotCommandService.saveReset(dto));
    }

    /**
     * 重启
     *
     * @param dto
     * @return
     */
    @PostMapping("/restart")
    public R saveRestart(@Valid @RequestBody BaseCommandDTO dto) {
        dto.setStreamId(StreamIdConstant.BA);
        return status(robotCommandService.saveRestart(dto));
    }

    /**
     * 白天防误扫设置
     *
     * @param dto
     * @return
     */
    @PostMapping("/daytime-prevent-clean")
    public R setDaytimePreventClean(@RequestBody BaseCommandDTO dto) {
        dto.setStreamId(StreamIdConstant.A6);
        return status(robotCommandService.setDaytimePreventClean(dto));
    }

    /**
     * 广播设置白天防误扫
     *
     * @param dto
     * @return
     */
    @PostMapping("/multicast-group/daytime-prevent-clean")
    public R setMulticastGroupDaytimePreventClean(@RequestBody BaseCommandDTO dto) {
        dto.setStreamId(StreamIdConstant.A6);
        return status(robotCommandService.setMulticastGroupDaytimePreventClean(dto));
    }

    /**
     * License-授权
     *
     * @param dto
     * @return
     */
    @PostMapping("/license-auth")
    public R saveLicenseAuth(LicenseAuthDTO dto) {
        Assert.notNull(dto.getAuthType(), "授权类型不能为空");
        dto.setStreamId(StreamIdConstant.A9);
        return status(robotCommandService.saveLicenseAuth(dto));
    }

    /**
     * 机器人升级
     *
     * @param dto
     * @return
     */
    @PostMapping("/robot-upgrade")
    public R saveRobotUpgrade(RobotUpgradeDTO dto) throws IOException {
        return status(robotCommandService.saveRobotUpgrade(dto));
    }

    /**
     * 机器人组播升级
     *
     * @param dto
     * @return
     * @throws IOException
     */
    @PostMapping("/multicastGroup-upgrade")
    public R multicastGroupUpgrade(RobotUpgradeDTO dto) throws IOException {
        return status(robotCommandService.multicastGroupUpgrade(dto));
    }

    /**
     * 机器人日志测试
     *
     * @param devEui
     * @return
     */
    @GetMapping("/robot-log")
    public R robotTest(String devEui) {
        return data(deviceLogService.deviceFrames(devEui));
    }

    /**
     * 机器人校时
     *
     * @param dto
     * @return
     */
    @PostMapping("/sync/time")
    public R saveSyncTime(@RequestBody BaseCommandDTO dto) {
        dto.setStreamId(StreamIdConstant.A8);
        robotCommandService.syncTimeSingleSendBroadCast(dto);
        return status(true);
    }

    /**
     * 机器人刷新时间
     *
     * @param dto
     * @return
     */
    @PostMapping("/refresh/time")
    public R refreshTime(@RequestBody BaseCommandDTO dto) {
        dto.setStreamId(StreamIdConstant.C2);
        robotCommandService.refreshTime(dto);
        return status(true);
    }

    /**
     * 机器人基本数据刷新
     *
     * @param dto
     * @return
     */
    @PostMapping("/refresh/base-data")
    public R refreshBaseData(@RequestBody BaseCommandDTO dto) {
        dto.setStreamId(StreamIdConstant.C4);
        robotCommandService.refreshBaseData(dto);
        return status(true);
    }

    /**
     * 机器人参数数据刷新
     *
     * @param dto
     * @return
     */
    @PostMapping("/refresh/param-data")
    public R refreshParamData(@RequestBody BaseCommandDTO dto) {
        robotCommandService.refreshParamData(dto);
        return status(true);
    }

    /**
     * 机器人常用数据刷新
     *
     * @param dto
     * @return
     */
    @PostMapping("/refresh/common-data")
    public R refreshCommonData(@RequestBody BaseCommandDTO dto) {
        robotCommandService.refreshCommonData(dto);
        return status(true);
    }
}

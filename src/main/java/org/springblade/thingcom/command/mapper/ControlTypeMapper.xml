<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.thingcom.command.mapper.ControlTypeMapper">
    <resultMap type="org.springblade.thingcom.command.entity.ControlType" id="ControlTypeMap">
        <result column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="code" property="code"/>
        <result column="value" property="value"/>
        <result column="command_value" property="commandValue"/>
        <result column="sort" property="sort"/>
        <result column="remark" property="remark"/>
        <result column="creator_id" property="creatorId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_id" property="updateId"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="deleted"/>
    </resultMap>
</mapper>

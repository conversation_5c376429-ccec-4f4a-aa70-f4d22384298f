package org.springblade.thingcom.command.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.thingcom.communication.entity.Timer;

import javax.validation.Valid;
import java.util.List;

/**
 * @ClassName TimerDTO
 * @Description: 定时器
 * @Author: zgq
 * @Date: 2024/1/20 15:23
 * @Version: 1.0
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class TimerDTO extends BaseCommandDTO {
    private static final long serialVersionUID = -4157500227508328842L;

    /**
     * 定时器列表
     */
    @Valid
    private List<Timer> timerList;
}

package org.springblade.thingcom.command.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @ClassName StartDTO
 * @Description: 启动
 * @Author: zgq
 * @Date: 2024/3/29 14:11
 * @Version: 1.0
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class StartDTO extends BaseCommandDTO {
    private static final long serialVersionUID = -8078120633643840661L;

    /**
     * 启动次数，默认1次
     */
     private String startCount = "1";

     /**
      * 操作类型 1-启动 2-停止 3-前进 4-后退
      */
     private String operationType;
}

package org.springblade.thingcom.command.dto;

import lombok.Data;

/**
 * @ClassName MulticastGroupCommandDTO
 * @Description 广播组命令DTO
 * <AUTHOR>
 * @Date 2024/7/20 10:16
 * @Version 1.0
 **/
@Data
public class MulticastGroupCommandDTO {

    /**
     * 发送次数
     */
    private Integer sendCount;

    /**
     * 查询次数
     */
    private Integer queryCount;

    /**
     * 重试间隔（毫秒）
     */
    private Integer interval;
}

package org.springblade.thingcom.command.handle;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.cache.CacheNames;
import org.springblade.common.cache.ParamCache;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.ParamConstant;
import org.springblade.common.utils.CommonUtil;
import org.springblade.common.utils.NumberUtil;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.tool.utils.Func;
import org.springblade.thingcom.command.constant.CommandConstant;
import org.springblade.thingcom.command.dto.CommandParamDTO;
import org.springblade.thingcom.command.dto.MulticastGroupCommandDTO;
import org.springblade.thingcom.command.dto.MulticastGroupDataDTO;
import org.springblade.thingcom.command.dto.RemoteControlDTO;
import org.springblade.thingcom.command.enums.CollectorCommandEnum;
import org.springblade.thingcom.command.enums.CommandEnum;
import org.springblade.thingcom.command.enums.OperationStatusEnum;
import org.springblade.thingcom.command.utils.ControlUtil;
import org.springblade.thingcom.communication.constant.StreamIdConstant;
import org.springblade.thingcom.communication.utils.StreamIdUtil;
import org.springblade.thingcom.core.chirpStack.cache.CommonCache;
import org.springblade.thingcom.core.chirpStack.dto.DownLinkDTO;
import org.springblade.thingcom.core.chirpStack.dto.MulticastQueueItemDTO;
import org.springblade.thingcom.core.chirpStack.service.DeviceQueueService;
import org.springblade.thingcom.core.chirpStack.service.MulticastGroupService;
import org.springblade.thingcom.core.chirpStack.utils.SubscribeUtil;
import org.springblade.thingcom.core.dynamicDatasource.service.DynamicDatatableService;
import org.springblade.thingcom.core.mqtt.MqttClient;
import org.springblade.thingcom.core.websocket.WebSocketSendTypeEnum;
import org.springblade.thingcom.core.websocket.WebSocketService;
import org.springblade.thingcom.data.constant.RobotDataConstant;
import org.springblade.thingcom.data.enums.DynamicDatatableEnum;
import org.springblade.thingcom.data.utils.MonitorValueParseUtil;
import org.springblade.thingcom.device.cache.RobotRegionalCache;
import org.springblade.thingcom.device.entity.Robot;
import org.springblade.thingcom.device.entity.RobotRegional;
import org.springblade.thingcom.device.enums.UpgradeStatusEnum;
import org.springblade.thingcom.device.mapper.RobotMapper;
import org.springblade.thingcom.logs.MessageLogTypeEnum;
import org.springblade.thingcom.logs.entity.RobotMessageLog;
import org.springblade.thingcom.statistic.entity.RobotBroadcastOperationLog;
import org.springblade.thingcom.statistic.entity.RobotOperationLog;
import org.springblade.thingcom.translate.enums.LanguageTypeEnum;
import org.springblade.thingcom.translate.utils.TranslateUtil;
import org.springblade.thingcom.upgradeLog.entity.UpgradeTestLog;
import org.springblade.thingcom.upgradeLog.service.UpgradeTestLogService;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * @ClassName CommandHandler
 * @Description: 指令处理
 * @Author: zgq
 * @Date: 2024/4/10 11:43
 * @Version: 1.0
 **/
@Slf4j
@Service
@AllArgsConstructor
public class CommandHandler {

    private final MqttClient mqttClient;
    private final BladeRedis bladeRedis;
    private final WebSocketService webSocketService;
    private final RobotMapper robotMapper;
    private final MulticastGroupService multicastGroupService;
    private final DynamicDatatableService dynamicDatatableService;
    private final UpgradeTestLogService upgradeTestLogService;
    private final MulticastGroupResultHanler multicastGroupResultHanler;
    private final DeviceQueueService deviceQueueService;

    /**
     * 异步下发指令
     *
     * @param robot        机器人
     * @param commandParam 指令参数
     */
    @Async("asyncPoolTaskExecutor")
    public void asyncSendCommand(Robot robot, CommandParamDTO commandParam) {
        // 指令参数
        String controlStreamId = commandParam.getControlStreamId();
        String commandContent = commandParam.getCommandContent();
        long timestamp = commandParam.getTimestamp();
        // 根据 controlStreamId 获取指令类型
        String streamId = StreamIdUtil.getControlTypeByControlStreamId(controlStreamId);
        // 控制指令（true-控制指令，false-非控制指令）
        boolean controlCommand = StreamIdUtil.isControlCommand(streamId);
        // 控制数据（这里是下发的数据，用于校验嵌入式回复的数据是否一致）
        String controlData = MonitorValueParseUtil.getValue(streamId, commandContent);
        // 远程控制参数
        RemoteControlDTO remoteControlDTO = new RemoteControlDTO();
        String remoteControlJsonStr = ParamCache.getValue(ParamConstant.REMOTE_CONTROL);
        if (StrUtil.isNotBlank(remoteControlJsonStr)) {
            remoteControlDTO = JSONUtil.toBean(remoteControlJsonStr, RemoteControlDTO.class);
        }
        int sendCount = remoteControlDTO.getSendCount();
        int loopCount = remoteControlDTO.getLoopCount();
        int loopInterval = remoteControlDTO.getLoopInterval();
        // 获取控制类校验数据缓存key
        String controlValidateDataCacheKey = StreamIdUtil.getControlValidateDataCacheKey(robot.getEui());
        // 下发指令订阅的主题
        String commandDown = SubscribeUtil.getCommandDown();
        String topic = StrUtil.format(commandDown, robot.getEui());
        // 消息载荷
        DownLinkDTO payloadDto = new DownLinkDTO();
        // 16进制字符串转 base64
        String base64Data = NumberUtil.hexStrToBase64(commandContent);
        payloadDto.setData(base64Data);
        // 发送指令
        String payload = JSONUtil.toJsonStr(payloadDto);
        //log.error("机器人：{}，指令内容，{}", robot, payloadDto);
        // 操作结果状态
        Integer operationStatus = OperationStatusEnum.FAIL.getValue();
        // 下发失败，需要重发
        retryFlag:
        for (int sendNum = 0; sendNum < sendCount; sendNum++) {
            mqttClient.publish(topic, payload);
            // 保存报文日志
            saveMessageLog(robot.getId(), commandContent);
            // 循环等待回复
            for (int loopNum = 0; loopNum < loopCount; loopNum++) {
                ThreadUtil.sleep(loopInterval);
                // 从缓存中获取指令下发回复
                Dict controlResponseDict = bladeRedis.hGet(controlValidateDataCacheKey, controlStreamId);
                // 控制指令：只需要判断回复时间大于下发时间（设备 eui + 命令标识 + 帧计数器，可判断指令单唯一性）
                if (controlCommand) {
                    if (ControlUtil.validateControlResult(controlResponseDict, timestamp, controlStreamId, null)) {
                        operationStatus = OperationStatusEnum.SUCCESS.getValue();
                        // 成功了，不需要重试
                        break retryFlag;
                    }
                } else {
                    // 指令内容回复值和下发值相等，并且回复时间大于下发时间
                    if (ControlUtil.validateControlResult(controlResponseDict, timestamp, controlStreamId, controlData)) {
                        operationStatus = OperationStatusEnum.SUCCESS.getValue();
                        // 成功了，不需要重试
                        break retryFlag;
                    }
                }
            }
        }
        // 保存操作日志和 websocket 推送
        commandParam.setOperationStatus(operationStatus);
        saveOperationLogAndWebSocketSend(robot, commandParam);
        // 成功后，需要执行的操作
        //if (OperationStatusEnum.SUCCESS.getValue().equals(operationStatus)) {
        //    // License 授权设置，需要更新对应机器人的授权状态、以及 License 的数量
        //    if (Objects.nonNull(license)) {
        //        commandAfterService.updateRobotAuth(robot, license);
        //    }
        //}
        // 删除设备队列数据，避免持久化导致设备恢复后的重发
        deviceQueueService.deleteData(robot.getEui());
    }

    /**
     * 机器人升级
     *
     * @param robot            机器人
     * @param commandContent   指令内容
     * @param operationContent 操作内容
     * @param upgradeData      升级数据
     */
    public boolean upgradeHandler(Robot robot, String commandContent, String operationContent
            , String upgradeData, Long userId, String createBy) {
        log.info("指令为：{}", commandContent);
        // 下发指令订阅的主题
        String commandDown = SubscribeUtil.getCommandDown();
        String topic = StrUtil.format(commandDown, robot.getEui());
        // 消息载荷
        DownLinkDTO payloadDto = new DownLinkDTO();
        // 16进制字符串转 base64 需要先转为字节数组
        String base64Data = NumberUtil.hexStrToBase64(commandContent);
        log.info("编码内容：{}", base64Data);
        payloadDto.setData(base64Data);
        // 发送指令
        String payload = JSONUtil.toJsonStr(payloadDto);
        // 超时，重发 5 次
        for (int i = 0; i < 5; i++) {
            mqttClient.publish(topic, payload);
            // 保存报文日志
            saveMessageLog(robot.getId(), commandContent);
            // 每隔1秒验证一次，共验证10次
            for (int j = 0; j < 10; j++) {
                ThreadUtil.sleep(1000);
                String cacheKey = CacheNames.ROBOT_UPGRADE_RECEIVE + robot.getEui();
                Dict dict = bladeRedis.hGet(cacheKey, upgradeData);
                if (Objects.nonNull(dict)) {
                    String upgradeReceiveData = dict.getStr(RobotDataConstant.UPGRADE_DATA);
                    if (upgradeData.equals(upgradeReceiveData)) {
                        log.info("{}指令下发成功", operationContent);
                        return true;
                    }
                }
            }
        }
        // 升级失败记录操作日志，循环中判断是否成功，如果成功了直接 return 了，所以这里不会执行
        upgradeFailHandler(robot, userId, createBy, OperationStatusEnum.FAIL.getValue());
        return false;
    }

    /**
     * 机器人升级失败处理，记录操作日志（失败）、更新机器人升级状态（未升级-升级失败）
     *
     * @param robot    机器人
     * @param userId   用户id
     * @param createBy 创建人
     * @param status   操作状态
     */
    public void upgradeFailHandler(Robot robot, Long userId, String createBy, Integer status) {
        // 异常：升级失败记录操作日志
        RobotOperationLog robotOperationLog = new RobotOperationLog();
        robotOperationLog.setRobotId(robot.getId());
        robotOperationLog.setRobotRegionalId(robot.getRobotRegionalId());
        robotOperationLog.setRobotNumber(robot.getRobotNumber());
        robotOperationLog.setRobotName(robot.getName());
        RobotRegional robotRegional = RobotRegionalCache.getById(robot.getRobotRegionalId());
        if (Objects.nonNull(robotRegional)) {
            robotOperationLog.setRobotRegionalNumber(robotRegional.getRegionalNumber());
        }
        robotOperationLog.setTitle(CommandConstant.UPGRADE);
        robotOperationLog.setContent(CommandConstant.UPGRADE);
        robotOperationLog.setStatus(status);
        robotOperationLog.setCreatorId(userId);
        robotOperationLog.setCreateBy(createBy);
        robotOperationLog.setCreateTime(CommonUtil.getZoneTime());
        // 分库分表保存数据
        String tableNamePrefix = DynamicDatatableEnum.ROBOT_OPERATION_LOG.getValue();
        dynamicDatatableService.saveData(tableNamePrefix, CommonUtil.getZoneTime(), BeanUtil.beanToMap(robotOperationLog));
        Integer upgradeStatus = status.equals(OperationStatusEnum.FAIL.getValue()) ? UpgradeStatusEnum.UNUPGRADE.getValue() : UpgradeStatusEnum.UPGRADE.getValue();
        // 更新机器人升级状态
        robotMapper.update(new Robot(), Wrappers.<Robot>lambdaUpdate()
                .set(Robot::getUpgradeStatus, upgradeStatus)
                .eq(Robot::getId, robot.getId()));
        // 删除设备队列数据，避免持久化导致设备恢复后的重发
        deviceQueueService.deleteData(robot.getEui());
    }

    /**
     * 异步处理：初始化设备数据
     * 1. 下发：设置组地址，用于广播
     * 2. 下发：C0 ~ C4 指令，初始化数据
     *
     * @param mcAddr 组播地址
     * @param robot  机器人
     */
    @Async("asyncPoolTaskExecutor")
    public void asyncInitDeviceData(String mcAddr, Robot robot) {
        // 下发指令的间隔
        String intervalStr = ParamCache.getValue(ParamConstant.DEVICE_COMMAND_INTERVAL);
        int interval = Func.toInt(intervalStr, 10000);
        // 新增机器人后，间隔一定时间再下发
        ThreadUtil.sleep(interval);
        // 下发指令订阅的主题
        String commandDown = SubscribeUtil.getCommandDown();
        String topic = StrUtil.format(commandDown, robot.getEui());
        // 远程控制参数
        RemoteControlDTO remoteControlDTO = new RemoteControlDTO();
        String remoteControlJsonStr = ParamCache.getValue(ParamConstant.REMOTE_CONTROL);
        if (StrUtil.isNotBlank(remoteControlJsonStr)) {
            remoteControlDTO = JSONUtil.toBean(remoteControlJsonStr, RemoteControlDTO.class);
        }
        int sendCount = remoteControlDTO.getSendCount();
        int loopCount = remoteControlDTO.getLoopCount();
        int loopInterval = remoteControlDTO.getLoopInterval();
        // 设置组地址
        setGroupAddress(sendCount, loopCount, loopInterval, topic, mcAddr, robot);
        // 间隔一定时间再下发
        ThreadUtil.sleep(interval);
        // 初始化数据，下发 C0 ~ C4 指令
        initData(sendCount, loopCount, loopInterval, topic, robot);
    }

    /**
     * 设置组地址
     *
     * @param sendCount    发送次数
     * @param loopCount    循环次数
     * @param loopInterval 循环间隔时间
     * @param topic        topic
     * @param mcAddr       组播地址
     * @param robot        机器人
     */
    private void setGroupAddress(Integer sendCount, Integer loopCount, Integer loopInterval
            , String topic, String mcAddr, Robot robot) {
        // mcAddr，每两位一组，然后再反转
        String[] mcAddrArray = ArrayUtil.reverse(StrUtil.split(mcAddr, 2));
        StringBuilder groupData = new StringBuilder(StreamIdConstant.SET_GROUP_ADDRESS);
        for (String mcAddrReverse : mcAddrArray) {
            groupData.append(mcAddrReverse);
        }
        groupData.append(CommonCache.getSessionKey());
        groupData.append("00000000FFFFFF7F");
        // 16进制字符串转 base64
        String base64Data = NumberUtil.hexStrToBase64(groupData.toString());
        // 消息载荷
        DownLinkDTO payloadDto = new DownLinkDTO();
        payloadDto.setFPort(200);
        payloadDto.setData(base64Data);
        String payload = JSONUtil.toJsonStr(payloadDto);
        // 下发失败，需要重发
        retryFlag:
        for (int sendNum = 0; sendNum < sendCount; sendNum++) {
            long timestamp = System.currentTimeMillis();
            // 发送指令
            mqttClient.publish(topic, payload);
            // 保存报文日志
            saveMessageLog(robot.getId(), groupData.toString());
            // 指令必须有间隔，下一条指令必须等上一条指令有回复再下发
            for (int loopNum = 0; loopNum < loopCount; loopNum++) {
                ThreadUtil.sleep(loopInterval);
                if (ControlUtil.validateControlResult(robot.getEui(), StreamIdConstant.SET_GROUP_ADDRESS, timestamp, null)) {
                    // 成功了，不需要重发
                    break retryFlag;
                }
            }
        }
        // 删除设备队列数据，避免持久化导致设备恢复后的重发
        deviceQueueService.deleteData(robot.getEui());
    }

    /**
     * 初始化数据，下发 C0 ~ C4 指令
     *
     * @param sendCount    发送次数
     * @param loopCount    循环次数
     * @param loopInterval 循环间隔时间
     * @param topic        topic
     * @param robot        机器人
     */
    private void initData(Integer sendCount, Integer loopCount, Integer loopInterval
            , String topic, Robot robot) {
        String robotNumberHex = NumberUtil.decToHex(robot.getRobotNumber(), 4);
        // 消息载荷
        DownLinkDTO payloadDto = new DownLinkDTO();
        // 下发 C0 ~ C4 指令
        int cCount = StreamIdConstant.C_COUNT;
        for (int cnum = 0; cnum <= cCount; cnum++) {
            CommandEnum commandEnum = CommandEnum.getById(StrUtil.format("C{}", cnum));
            if (Objects.isNull(commandEnum)) {
                continue;
            }
            String streamId = StreamIdConstant.C + cnum;
            // 控制streamId
            String controlStreamId = StreamIdUtil.getControlStreamId(robot.getEui(), streamId);
            // 帧计数器
            String controlFrameCounterHex = StreamIdUtil.getFrameCounterByControlStreamId(controlStreamId);
            String commandTemplate = commandEnum.getCommandTemplate();
            // 数据域长度
            String valueLengthHex = ControlUtil.getDataDomainLengthHex(null);
            // 校验位前置条件：需要校验的字符串
            String checkStr = StrUtil.format(commandTemplate.split(StrUtil.COMMA)[0], robotNumberHex, controlFrameCounterHex, valueLengthHex);
            // 校验位
            String checkSum = NumberUtil.getCheckSum(checkStr);
            // 指令内容
            String commandContent = StrUtil.format(commandTemplate, robotNumberHex, controlFrameCounterHex, valueLengthHex, checkSum);
            // 替换指令中的逗号分隔符，并转为大写
            commandContent = commandContent.replace(StrUtil.COMMA, StrUtil.EMPTY).toUpperCase();
            // 16进制字符串转 base64 需要先转为字节数组
            payloadDto.setData(NumberUtil.hexStrToBase64(commandContent));
            // 发送指令
            String payload = JSONUtil.toJsonStr(payloadDto);
            //log.error("新增设备，下发指令：{}", payload);
            // 第一条指令失败，则停止后面的指令下发
            boolean isSuccess = false;
            // 获取控制类校验数据缓存key
            String controlValidateDataCacheKey = StreamIdUtil.getControlValidateDataCacheKey(robot.getEui());
            // 下发失败，需要重发
            retryFlag:
            for (int sendNum = 0; sendNum < sendCount; sendNum++) {
                long timestamp = System.currentTimeMillis();
                // 发送指令
                mqttClient.publish(topic, payload);
                // 保存报文日志
                saveMessageLog(robot.getId(), commandContent);
                // 指令必须有间隔，下一条指令必须等上一条指令有回复再下发
                for (int loopNum = 0; loopNum < loopCount; loopNum++) {
                    ThreadUtil.sleep(loopInterval);
                    // 从缓存中获取指令下发回复
                    Dict controlResponseDict = bladeRedis.hGet(controlValidateDataCacheKey, controlStreamId);
                    // 控制指令：只需要判断回复时间大于下发时间（设备 eui + 命令标识 + 帧计数器，可判断指令单唯一性）
                    if (ControlUtil.validateControlResult(controlResponseDict, timestamp, controlStreamId, null)) {
                        isSuccess = true;
                        // 成功了，不需要重试
                        break retryFlag;
                    }
                }
            }
            if (!isSuccess) {
                break;
            }
        }
        // 删除设备队列数据，避免持久化导致设备恢复后的重发
        deviceQueueService.deleteData(robot.getEui());
    }

    /**
     * 异步处理：刷新设备数据
     * 下发：C0、C1、C4 指令
     *
     * @param robot        机器人
     * @param commandParam 命令参数
     * @param cList        下发的 C 指令列表
     */
    @Async("asyncPoolTaskExecutor")
    public void asyncRefreshDeviceData(Robot robot, CommandParamDTO commandParam, List<Integer> cList) {
        String eui = robot.getEui();
        // 下发指令订阅的主题
        String commandDown = SubscribeUtil.getCommandDown();
        String topic = StrUtil.format(commandDown, eui);
        // 远程控制参数
        RemoteControlDTO remoteControlDTO = new RemoteControlDTO();
        String remoteControlJsonStr = ParamCache.getValue(ParamConstant.REMOTE_CONTROL);
        if (StrUtil.isNotBlank(remoteControlJsonStr)) {
            remoteControlDTO = JSONUtil.toBean(remoteControlJsonStr, RemoteControlDTO.class);
        }
        int sendCount = remoteControlDTO.getSendCount();
        int loopCount = remoteControlDTO.getLoopCount();
        int loopInterval = remoteControlDTO.getLoopInterval();
        // 机器人编号
        String robotNumberHex = NumberUtil.decToHex(robot.getRobotNumber(), 4);
        // 操作结果状态
        Integer operationStatus = OperationStatusEnum.SUCCESS.getValue();
        // 消息载荷
        DownLinkDTO payloadDto = new DownLinkDTO();
        for (Integer cnum : cList) {
            CommandEnum commandEnum = CommandEnum.getById(StrUtil.format("C{}", cnum));
            if (Objects.isNull(commandEnum)) {
                continue;
            }
            String streamId = StreamIdConstant.C + cnum;
            // 控制streamId
            String controlStreamId = StreamIdUtil.getControlStreamId(eui, streamId);
            // 帧计数器
            String controlFrameCounterHex = StreamIdUtil.getFrameCounterByControlStreamId(controlStreamId);
            // 数据域长度
            String valueLengthHex = ControlUtil.getDataDomainLengthHex(null);
            String commandTemplate = commandEnum.getCommandTemplate();
            // 校验位前置条件：需要校验的字符串
            String checkStr = StrUtil.format(commandTemplate.split(StrUtil.COMMA)[0], robotNumberHex, controlFrameCounterHex, valueLengthHex);
            // 校验位
            String checkSum = NumberUtil.getCheckSum(checkStr);
            // 指令内容
            String commandContent = StrUtil.format(commandTemplate, robotNumberHex, controlFrameCounterHex, valueLengthHex, checkSum);
            // 替换指令中的逗号分隔符，并转为大写
            commandContent = commandContent.replace(StrUtil.COMMA, StrUtil.EMPTY).toUpperCase();
            // 16进制字符串转 base64 需要先转为字节数组
            payloadDto.setData(NumberUtil.hexStrToBase64(commandContent));
            // 发送指令
            String payload = JSONUtil.toJsonStr(payloadDto);
            // 第一条指令失败，则停止后面的指令下发
            boolean isSuccess = false;
            // 获取控制类校验数据缓存key
            String controlValidateDataCacheKey = StreamIdUtil.getControlValidateDataCacheKey(eui);
            // 下发失败，需要重发
            retryFlag:
            for (int sendNum = 0; sendNum < sendCount; sendNum++) {
                long timestamp = System.currentTimeMillis();
                // 发送指令
                mqttClient.publish(topic, payload);
                // 保存报文日志
                saveMessageLog(robot.getId(), commandContent);
                // 指令必须有间隔，下一条指令必须等上一条指令有回复再下发
                for (int loopNum = 0; loopNum < loopCount; loopNum++) {
                    ThreadUtil.sleep(loopInterval);
                    // 从缓存中获取指令下发回复
                    Dict controlResponseDict = bladeRedis.hGet(controlValidateDataCacheKey, controlStreamId);
                    // 控制指令：只需要判断回复时间大于下发时间（设备 eui + 命令标识 + 帧计数器，可判断指令单唯一性）
                    if (ControlUtil.validateControlResult(controlResponseDict, timestamp, controlStreamId, null)) {
                        isSuccess = true;
                        // 成功了，不需要重试
                        break retryFlag;
                    }
                }
            }
            if (!isSuccess) {
                operationStatus = OperationStatusEnum.FAIL.getValue();
                break;
            }
        }
        // 保存操作日志和 websocket 推送
        commandParam.setOperationStatus(operationStatus);
        saveOperationLogAndWebSocketSend(robot, commandParam);
        // 删除设备队列数据，避免持久化导致设备恢复后的重发
        deviceQueueService.deleteData(robot.getEui());
    }

    /**
     * 保存操作日志和 websocket 推送
     *
     * @param robot        机器人
     * @param commandParam 指令参数
     * @return
     */
    private void saveOperationLogAndWebSocketSend(Robot robot, CommandParamDTO commandParam) {
        String operationTitle = commandParam.getOperationTitle();
        String operationContent = commandParam.getOperationContent();
        Integer operationStatus = commandParam.getOperationStatus();
        Long userId = commandParam.getUserId();
        String createBy = commandParam.getCreateBy();
        // 保存操作日志
        LocalDateTime now = CommonUtil.getZoneTime();
        RobotRegional robotRegional = RobotRegionalCache.getById(robot.getRobotRegionalId());
        RobotOperationLog robotOperationLog = new RobotOperationLog();
        robotOperationLog.setRobotId(robot.getId());
        robotOperationLog.setRobotRegionalId(robot.getRobotRegionalId());
        if (Objects.nonNull(robotRegional)) {
            robotOperationLog.setRobotRegionalNumber(robotRegional.getRegionalNumber());
        }
        robotOperationLog.setRobotNumber(robot.getRobotNumber());
        robotOperationLog.setRobotName(robot.getName());
        robotOperationLog.setTitle(operationTitle);
        robotOperationLog.setContent(operationContent);
        robotOperationLog.setStatus(operationStatus);
        robotOperationLog.setCreatorId(userId);
        robotOperationLog.setCreateBy(createBy);
        robotOperationLog.setCreateTime(now);
        // 分库分表保存数据
        String tableNamePrefix = DynamicDatatableEnum.ROBOT_OPERATION_LOG.getValue();
        dynamicDatatableService.saveData(tableNamePrefix, now, BeanUtil.beanToMap(robotOperationLog));
        // 操作结果
        String operationResult = OperationStatusEnum.translationValue(operationStatus);
        String operationTime = now.format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN));
        // 操作内容
        String msg = StrUtil.join(StrUtil.COMMA, CommandConstant.ROBOT_NUMBER + robot.getName()
                , operationTitle, operationResult
                , StrUtil.concat(true, CommandConstant.EXECUTE_TIME, StrUtil.COLON, operationTime));
        // 判断是否要翻译
        Object translationObj = bladeRedis.get(CacheNames.WEBSOCKET_SEND_TRANSLATION_CACHE_PREFIX);
        if (Objects.equals(String.valueOf(translationObj), LanguageTypeEnum.ENGLISH.getValue())) {
            msg = TranslateUtil.chineseToEnglish(msg);
        }
        // websocket 推送
        webSocketService.sendMoreMessage(WebSocketSendTypeEnum.COMMAND.getLabel() + userId, msg);
    }

    /**
     * 异步组播群组
     *
     * @param isResend 是否重发
     */
    @Async("asyncPoolTaskExecutor")
    public void asyncMulticastGroup(boolean isResend) {
        // 广播命令参数
        int sendCount = isResend ? 20 : 1;
        int interval = 10000;
        // 是否重发
        if (isResend) {
            String multicastGroupCommandJsonStr = ParamCache.getValue(ParamConstant.MULTICAST_GROUP_COMMAND);
            if (StrUtil.isNotBlank(multicastGroupCommandJsonStr)) {
                MulticastGroupCommandDTO multicastGroupCommandDTO
                        = JSONUtil.toBean(multicastGroupCommandJsonStr, MulticastGroupCommandDTO.class);
                sendCount = multicastGroupCommandDTO.getSendCount();
                interval = multicastGroupCommandDTO.getInterval();
            }
        }
        // 第一条指令内容（只记录第一条报文日志）
        String commandContent1 = null;
        // 下发广播命令
        for (int i = 0; i < sendCount; i++) {
            String commandContent = ControlUtil.getMulticastGroupCommandContent();
            if (i == 0) {
                commandContent1 = commandContent;
            }
            // 16进制字符串转 base64
            String base64Data = NumberUtil.hexStrToBase64(commandContent);
            // 请求参数
            MulticastQueueItemDTO dto = new MulticastQueueItemDTO();
            MulticastQueueItemDTO.MulticastQueueItem multicastQueueItem = new MulticastQueueItemDTO.MulticastQueueItem();
            multicastQueueItem.setData(base64Data);
            dto.setMulticastQueueItem(multicastQueueItem);
            multicastGroupService.itemMulticastGroup(dto);
            // 是否重发
            if (isResend) {
                // 间隔指定时间
                ThreadUtil.sleep(interval);
            }
        }
        if (StrUtil.isNotBlank(commandContent1)) {
            // 保存报文日志
            LocalDateTime time = CommonUtil.getZoneTime();
            List<Robot> robotList = robotMapper.selectList(Wrappers.lambdaQuery());
            for (Robot robot : robotList) {
                // 保存报文日志时间（获取最新的报文日志使用）
                bladeRedis.hSet(CacheNames.ROBOT_MESSAGE_LOG_TIME, robot.getId(), time);
                // 保存报文日志
                RobotMessageLog robotMessageLog = new RobotMessageLog();
                robotMessageLog.setRobotId(robot.getId())
                        .setContent(MessageLogTypeEnum.SEND.getLabel() + commandContent1)
                        .setTime(time).setCreateTime(time);
                // 分库分表保存数据
                String tableNamePrefixMessageLog = DynamicDatatableEnum.ROBOT_MESSAGE_LOG.getValue();
                dynamicDatatableService.saveData(tableNamePrefixMessageLog, time, BeanUtil.beanToMap(robotMessageLog));
            }
        }
    }

    /**
     * 异步组播群组（参数配置）
     *
     * @param commandParamDTO 指令参数
     */
    @Async("asyncPoolTaskExecutor")
    public void asyncMulticastGroupParamConfig(CommandParamDTO commandParamDTO) {
        // 指令参数
        String controlStreamId = commandParamDTO.getControlStreamId();
        CommandEnum commandEnum = commandParamDTO.getCommandEnum();
        String value = commandParamDTO.getValue();
        // 指令模板
        String commandTemplate = commandEnum.getCommandTemplate();
        // 机器人广播编号
        String robotNumberHex = CommandConstant.BROADCAST_NUMBER;
        // 获取帧计数器
        String frameCounterHex = StreamIdUtil.getFrameCounterByControlStreamId(controlStreamId);
        // 数据域长度
        String valueLengthHex = ControlUtil.getDataDomainLengthHex(value);
        // 校验位前置条件：需要校验的字符串
        String checkStr = StrUtil.format(commandTemplate.split(StrUtil.COMMA)[0]
                , robotNumberHex, frameCounterHex, valueLengthHex, value);
        // 校验位
        String checkSum = NumberUtil.getCheckSum(checkStr);
        // 命令内容
        String commandContent;
        if (StrUtil.isBlank(value)) {
            commandContent = StrUtil.format(commandTemplate, robotNumberHex, frameCounterHex, valueLengthHex, checkSum);
        } else {
            commandContent = StrUtil.format(commandTemplate, robotNumberHex, frameCounterHex, valueLengthHex, value, checkSum);
        }
        // 替换指令内容中的逗号为空，并转为大写
        commandContent = commandContent.replace(StrUtil.COMMA, StrUtil.EMPTY).toUpperCase();
        // 广播命令参数
        int sendCount = 5;
        int interval = 3000;
        String multicastGroupCommandJsonStr = ParamCache.getValue(ParamConstant.MULTICAST_GROUP_PARAM_CONFIG_COMMAND);
        if (StrUtil.isNotBlank(multicastGroupCommandJsonStr)) {
            MulticastGroupCommandDTO multicastGroupCommandDTO
                    = JSONUtil.toBean(multicastGroupCommandJsonStr, MulticastGroupCommandDTO.class);
            sendCount = multicastGroupCommandDTO.getSendCount();
            interval = multicastGroupCommandDTO.getInterval();
        }
        // 下发广播命令
        for (int i = 0; i < sendCount; i++) {
            // 16进制字符串转 base64
            String base64Data = NumberUtil.hexStrToBase64(commandContent);
            // 请求参数
            MulticastQueueItemDTO dto = new MulticastQueueItemDTO();
            MulticastQueueItemDTO.MulticastQueueItem multicastQueueItem = new MulticastQueueItemDTO.MulticastQueueItem();
            multicastQueueItem.setData(base64Data);
            dto.setMulticastQueueItem(multicastQueueItem);
            multicastGroupService.itemMulticastGroup(dto);
            // 第一次下发
            if (i == 0) {
                // 保存广播操作日志
                asyncSaveBroadcastOperationLog(commandParamDTO.getTime(), commandParamDTO);
                // 获取当前机器人列表（组播对象）
                List<Robot> robotList = robotMapper.selectList(Wrappers.lambdaQuery());
                JSONObject json = JSONUtil.createObj();
                json.set(RobotDataConstant.ROBOT_LIST, JSONUtil.toJsonStr(robotList));
                // 下发命令：6841 → 6882
                controlStreamId = controlStreamId.replace(StreamIdConstant.CONTROL_REQUEST_PREFIX
                        , StreamIdConstant.CONTROL_RESPONSE_PREFIX);
                json.set(RobotDataConstant.STREAM_ID, controlStreamId);
                // 控制数据（这里是下发的数据，用于校验嵌入式回复的数据是否一致）
                String controlData = MonitorValueParseUtil.getValue(controlStreamId, commandContent);
                json.set(RobotDataConstant.CONTROL_DATA, controlData);
                json.set(RobotDataConstant.TIMESTAMP, commandParamDTO.getTimestamp());
                json.set(RobotDataConstant.VALUE, commandContent);
                json.set(RobotDataConstant.OPERATION_TITLE, commandParamDTO.getOperationTitle());
                json.set(RobotDataConstant.OPERATION_CONTENT, commandParamDTO.getOperationContent());
                json.set(RobotDataConstant.USER_ID, commandParamDTO.getUserId());
                // 处理组播响应结果
                multicastGroupResultHanler.handleMulticastGroupResponse(json);
            }
            // 保存广播报文日志
            asyncSaveBroadcastMessageLog(commandContent);
            // 间隔指定时间
            ThreadUtil.sleep(interval);
        }
    }

    /**
     * 异步保存广播报文日志，使用异步，避免影响广播二次下发
     *
     * @param commandContent 指令内容
     */
    @Async("asyncPoolTaskExecutor")
    public void asyncSaveBroadcastMessageLog(String commandContent) {
        LocalDateTime time = CommonUtil.getZoneTime();
        // 机器人列表
        List<Robot> robotList = robotMapper.selectList(Wrappers.lambdaQuery());
        // 保存报文日志
        for (Robot robot : robotList) {
            // 保存报文日志时间（获取最新的报文日志使用）
            bladeRedis.hSet(CacheNames.ROBOT_MESSAGE_LOG_TIME, robot.getId(), time);
            // 保存报文日志
            RobotMessageLog robotMessageLog = new RobotMessageLog();
            robotMessageLog.setRobotId(robot.getId())
                    .setContent(MessageLogTypeEnum.SEND.getLabel() + commandContent)
                    .setTime(time).setCreateTime(time);
            // 分库分表保存数据
            String tableNamePrefixMessageLog = DynamicDatatableEnum.ROBOT_MESSAGE_LOG.getValue();
            dynamicDatatableService.saveData(tableNamePrefixMessageLog, time, BeanUtil.beanToMap(robotMessageLog));
        }
    }

    /**
     * 异步保存广播操作日志，使用异步，避免影响广播二次下发
     *
     * @param time         时间
     * @param commandParam 指令参数
     */
    @Async("asyncPoolTaskExecutor")
    public void asyncSaveBroadcastOperationLog(LocalDateTime time, CommandParamDTO commandParam) {
        String streamId = StreamIdUtil.getStreamId(commandParam.getControlStreamId());
        String value = commandParam.getValue();
        // 保存最新的时间和值
        String timeStr = time.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        MulticastGroupDataDTO multicastGroupDataDTO = new MulticastGroupDataDTO();
        multicastGroupDataDTO.setTime(timeStr);
        multicastGroupDataDTO.setValue(value);
        bladeRedis.hSet(CacheNames.MULTICAST_GROUP_TIME, streamId, JSONUtil.toJsonStr(multicastGroupDataDTO));
        // 机器人列表
        List<Robot> robotList = robotMapper.selectList(Wrappers.lambdaQuery());
        // 保存操作日志
        for (Robot robot : robotList) {
            RobotBroadcastOperationLog robotBroadcastOperationLog = new RobotBroadcastOperationLog();
            robotBroadcastOperationLog.setRobotId(robot.getId());
            robotBroadcastOperationLog.setRobotRegionalId(robot.getRobotRegionalId());
            RobotRegional robotRegional = RobotRegionalCache.getById(robot.getRobotRegionalId());
            if (Objects.nonNull(robotRegional)) {
                robotBroadcastOperationLog.setRobotRegionalNumber(robotRegional.getRegionalNumber());
            }
            robotBroadcastOperationLog.setRobotNumber(robot.getRobotNumber());
            robotBroadcastOperationLog.setRobotName(robot.getName());
            robotBroadcastOperationLog.setStreamId(streamId);
            robotBroadcastOperationLog.setValue(value);
            robotBroadcastOperationLog.setTitle(commandParam.getOperationTitle());
            robotBroadcastOperationLog.setContent(commandParam.getOperationContent());
            robotBroadcastOperationLog.setStatus(OperationStatusEnum.DEVICE_PROCESSING.getValue());
            robotBroadcastOperationLog.setCreateTime(time);
            robotBroadcastOperationLog.setCreatorId(commandParam.getUserId());
            robotBroadcastOperationLog.setCreateBy(commandParam.getCreateBy());
            // 分库分表保存数据
            String tableNamePrefixOperationLog = DynamicDatatableEnum.ROBOT_BROADCAST_OPERATION_LOG.getValue();
            dynamicDatatableService.saveData(tableNamePrefixOperationLog, time, BeanUtil.beanToMap(robotBroadcastOperationLog));
        }
    }

    /**
     * 广播指令下发后，判断单台设备，失败的进行单播重发
     *
     * @param robot        机器人
     * @param commandParam 指令参数
     */
    @Async
    public CompletableFuture<Boolean> multicastGroupFailSingleSend(Robot robot, CommandParamDTO commandParam) {
        // 结果
        boolean result = false;
        // 指令参数
        String controlStreamId = commandParam.getControlStreamId();
        String commandContent = commandParam.getCommandContent();
        // 指令参数
        long timestamp = commandParam.getTimestamp();
        // 根据 controlStreamId 获取指令类型
        String controlType = StreamIdUtil.getControlTypeByControlStreamId(controlStreamId);
        boolean controlCommand = controlType.startsWith(StreamIdConstant.B);
        // 控制数据（这里是下发的数据，用于校验嵌入式回复的数据是否一致）
        String controlData = MonitorValueParseUtil.getValue(controlStreamId, commandContent);
        // 远程控制参数
        RemoteControlDTO remoteControlDTO = new RemoteControlDTO();
        String remoteControlJsonStr = ParamCache.getValue(ParamConstant.REMOTE_CONTROL);
        if (StrUtil.isNotBlank(remoteControlJsonStr)) {
            remoteControlDTO = JSONUtil.toBean(remoteControlJsonStr, RemoteControlDTO.class);
        }
        int sendCount = remoteControlDTO.getSendCount();
        int loopCount = remoteControlDTO.getLoopCount();
        int loopInterval = remoteControlDTO.getLoopInterval();
        // 获取控制类校验数据缓存key
        String controlValidateDataCacheKey = StreamIdUtil.getControlValidateDataCacheKey(robot.getEui());
        // 下发指令订阅的主题
        String commandDown = SubscribeUtil.getCommandDown();
        String topic = StrUtil.format(commandDown, robot.getEui());
        // 消息载荷
        DownLinkDTO payloadDto = new DownLinkDTO();
        // 16进制字符串转 base64
        String base64Data = NumberUtil.hexStrToBase64(commandContent);
        payloadDto.setData(base64Data);
        // 发送指令
        String payload = JSONUtil.toJsonStr(payloadDto);
        // 下发失败，需要重发
        retryFlag:
        for (int sendNum = 0; sendNum < sendCount; sendNum++) {
            mqttClient.publish(topic, payload);
            // 保存报文日志
            saveMessageLog(robot.getId(), commandContent);
            // 轮询等待设备回复
            for (int loopNum = 0; loopNum < loopCount; loopNum++) {
                ThreadUtil.sleep(loopInterval);
                // 从缓存中获取指令下发回复
                Dict controlReceiveDict = bladeRedis.hGet(controlValidateDataCacheKey, controlStreamId);
                // 控制指令：只需要判断回复时间大于下发时间（设备 eui + 命令标识 + 帧计数器，可判断指令单唯一性）
                if (controlCommand) {
                    if (ControlUtil.validateControlResult(controlReceiveDict, timestamp, controlStreamId, null)) {
                        result = true;
                        // 成功了，不需要重试
                        break retryFlag;
                    }
                } else {
                    // 指令内容回复值和下发值相等，并且回复时间大于下发时间
                    if (ControlUtil.validateControlResult(controlReceiveDict, timestamp, controlStreamId, controlData)) {
                        result = true;
                        // 成功了，不需要重试
                        break retryFlag;
                    }
                }
            }
        }
        // 删除设备队列数据，避免持久化导致设备恢复后的重发
        deviceQueueService.deleteData(robot.getEui());
        return CompletableFuture.completedFuture(result);
    }

    /**
     * 保存报文日志
     *
     * @param robotId        机器人id
     * @param commandContent 指令内容
     */
    private void saveMessageLog(Long robotId, String commandContent) {
        LocalDateTime now = CommonUtil.getZoneTime();
        // 保存报文日志时间（获取最新的报文日志使用）
        bladeRedis.hSet(CacheNames.ROBOT_MESSAGE_LOG_TIME, robotId, now);
        // 保存报文日志
        RobotMessageLog robotMessageLog = new RobotMessageLog();
        robotMessageLog.setRobotId(robotId)
                .setContent(MessageLogTypeEnum.SEND.getLabel() + commandContent)
                .setTime(now).setCreateTime(now);
        // 分库分表保存数据
        String tableNamePrefix = DynamicDatatableEnum.ROBOT_MESSAGE_LOG.getValue();
        dynamicDatatableService.saveData(tableNamePrefix, now, BeanUtil.beanToMap(robotMessageLog));
    }

    /**
     * 异步单个发送广播协议指令
     *
     * @param robot            机器人
     * @param commandParam     指令参数
     * @param operationTitle   操作标题
     * @param operationContent 操作内容
     */
    @Async("asyncPoolTaskExecutor")
    public void asyncSingleSendBroadcast(Robot robot, CommandParamDTO commandParam
            , String operationTitle, String operationContent) {
        // 指令参数
        Long userId = commandParam.getUserId();
        String createBy = commandParam.getCreateBy();
        if (StrUtil.isBlank(createBy)) {
            createBy = CommonConstant.SYSTEM_AUTO_SEND;
        }
        String controlStreamId = commandParam.getControlStreamId();
        String commandContent = commandParam.getCommandContent();
        // 指令参数
        long timestamp = commandParam.getTimestamp();
        // 远程控制参数
        RemoteControlDTO remoteControlDTO = new RemoteControlDTO();
        String remoteControlJsonStr = ParamCache.getValue(ParamConstant.REMOTE_CONTROL);
        if (StrUtil.isNotBlank(remoteControlJsonStr)) {
            remoteControlDTO = JSONUtil.toBean(remoteControlJsonStr, RemoteControlDTO.class);
        }
        int sendCount = remoteControlDTO.getSendCount();
        int loopCount = remoteControlDTO.getLoopCount();
        int loopInterval = remoteControlDTO.getLoopInterval();
        // 获取控制类校验数据缓存key
        String controlValidateDataCacheKey = StreamIdUtil.getControlValidateDataCacheKey(robot.getEui());
        // 下发指令订阅的主题
        String commandDown = SubscribeUtil.getCommandDown();
        String topic = StrUtil.format(commandDown, robot.getEui());
        // 消息载荷
        DownLinkDTO payloadDto = new DownLinkDTO();
        // 16进制字符串转 base64
        String base64Data = NumberUtil.hexStrToBase64(commandContent);
        payloadDto.setData(base64Data);
        // 发送指令
        String payload = JSONUtil.toJsonStr(payloadDto);
        // 操作结果状态
        Integer operationStatus = OperationStatusEnum.FAIL.getValue();
        // 下发失败，需要重发
        retryFlag:
        for (int sendNum = 0; sendNum < sendCount; sendNum++) {
            mqttClient.publish(topic, payload);
            // 保存报文日志
            saveMessageLog(robot.getId(), commandContent);
            // 循环等待回复
            for (int loopNum = 0; loopNum < loopCount; loopNum++) {
                ThreadUtil.sleep(loopInterval);
                // 从缓存中获取指令下发回复
                Dict controlReceiveDict = bladeRedis.hGet(controlValidateDataCacheKey, controlStreamId);
                // 控制指令：只需要判断回复时间大于下发时间（设备 eui + 命令标识 + 帧计数器，可判断指令单唯一性）
                if (ControlUtil.validateControlResult(controlReceiveDict, timestamp, controlStreamId, null)) {
                    operationStatus = OperationStatusEnum.SUCCESS.getValue();
                    // 成功了，不需要重试
                    break retryFlag;
                }
            }
        }
        // 保存操作日志
        LocalDateTime now = CommonUtil.getZoneTime();
        RobotRegional robotRegional = RobotRegionalCache.getById(robot.getRobotRegionalId());
        RobotOperationLog robotOperationLog = new RobotOperationLog();
        robotOperationLog.setRobotId(robot.getId());
        robotOperationLog.setRobotRegionalId(robot.getRobotRegionalId());
        if (Objects.nonNull(robotRegional)) {
            robotOperationLog.setRobotRegionalNumber(robotRegional.getRegionalNumber());
        }
        robotOperationLog.setRobotNumber(robot.getRobotNumber());
        robotOperationLog.setRobotName(robot.getName());
        robotOperationLog.setTitle(operationTitle);
        robotOperationLog.setContent(operationContent);
        robotOperationLog.setStatus(operationStatus);
        robotOperationLog.setCreatorId(userId);
        robotOperationLog.setCreateBy(createBy);
        robotOperationLog.setCreateTime(now);
        // 分库分表保存数据
        String tableNamePrefix = DynamicDatatableEnum.ROBOT_OPERATION_LOG.getValue();
        dynamicDatatableService.saveData(tableNamePrefix, now, BeanUtil.beanToMap(robotOperationLog));
        // 发送 websocket 消息
        if (Objects.nonNull(userId)) {
            // 操作结果
            String operationResult = OperationStatusEnum.translationValue(operationStatus);
            String operationTime = now.format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN));
            // 操作内容
            String msg = StrUtil.join(StrUtil.COMMA, CommandConstant.ROBOT_NUMBER + robot.getName()
                    , operationTitle, operationResult
                    , StrUtil.concat(true, CommandConstant.EXECUTE_TIME, StrUtil.COLON, operationTime));
            // 判断是否要翻译
            Object translationObj = bladeRedis.get(CacheNames.WEBSOCKET_SEND_TRANSLATION_CACHE_PREFIX);
            if (Objects.equals(String.valueOf(translationObj), LanguageTypeEnum.ENGLISH.getValue())) {
                msg = TranslateUtil.chineseToEnglish(msg);
            }
            // websocket 推送
            webSocketService.sendMoreMessage(WebSocketSendTypeEnum.COMMAND.getLabel() + userId, msg);
        }
        // 删除设备队列数据，避免持久化导致设备恢复后的重发
        deviceQueueService.deleteData(robot.getEui());
    }

    /**
     * 异步回复设备主动请求的指令
     * 该命令没有回复，所以不需要重发，硬件有重试机制，收不到指令会重新上报数据（该命令是通过上报数据后触发的）
     *
     * @param robotId        机器人ID
     * @param eui            机器人 EUI
     * @param commandContent 指令内容
     */
    @Async("asyncPoolTaskExecutor")
    public void asyncReceiveDeviceRequest(Long robotId, String eui, String commandContent) {
        // 下发指令订阅的主题
        String commandDown = SubscribeUtil.getCommandDown();
        String topic = StrUtil.format(commandDown, eui);
        // 消息载荷
        DownLinkDTO payloadDto = new DownLinkDTO();
        // 16进制字符串转 base64
        String base64Data = NumberUtil.hexStrToBase64(commandContent);
        payloadDto.setData(base64Data);
        // 发送指令
        String payload = JSONUtil.toJsonStr(payloadDto);
        mqttClient.publish(topic, payload);
        // 保存报文日志
        saveMessageLog(robotId, commandContent);
        // 删除设备队列数据，避免持久化导致设备恢复后的重发
        deviceQueueService.deleteData(eui);
    }

    /**
     * 机器人升级组播群
     *
     * @param commandContent
     * @return
     */
    public boolean multicastGroupUpgradeHandler(String commandContent) {
        // 16进制字符串转 base64
        String base64Data = NumberUtil.hexStrToBase64(commandContent);
        // 请求参数
        MulticastQueueItemDTO dto = new MulticastQueueItemDTO();
        MulticastQueueItemDTO.MulticastQueueItem multicastQueueItem = new MulticastQueueItemDTO.MulticastQueueItem();
        multicastQueueItem.setData(base64Data);
        dto.setMulticastQueueItem(multicastQueueItem);
        log.info("机器人升级组播群消息：{}", commandContent);
        // 下发广播命令
        for (int i = 0; i < 5; i++) {
            multicastGroupService.itemMulticastGroup(dto);
            ThreadUtil.sleep(3000);
            // 组播升级日志记录，测试用后面删除
            UpgradeTestLog upgradeTestLog = new UpgradeTestLog();
            upgradeTestLog.setContent(commandContent);
            upgradeTestLog.setNumber(commandContent.substring(8, 10));
            upgradeTestLogService.save(upgradeTestLog);
        }
        LocalDateTime time = CommonUtil.getZoneTime();
        List<Robot> robotList = robotMapper.selectList(Wrappers.lambdaQuery());
        for (Robot robot : robotList) {
            // 保存报文日志
            RobotMessageLog robotMessageLog = new RobotMessageLog();
            robotMessageLog.setRobotId(robot.getId())
                    .setContent(MessageLogTypeEnum.SEND.getLabel() + commandContent)
                    .setTime(time).setCreateTime(time);
            // 分库分表保存数据
            String tableNamePrefix = DynamicDatatableEnum.ROBOT_MESSAGE_LOG.getValue();
            dynamicDatatableService.saveData(tableNamePrefix, time, BeanUtil.beanToMap(robotMessageLog));
        }
        return true;
    }

    /**
     * 发送采集器指令
     *
     * @param mac          采集器 mac
     * @param commandId    指令标识
     * @param commandValue 指令值
     * @return
     */
    public boolean sendCollectorCommand(String mac, String commandId, Integer commandValue) {
        CollectorCommandEnum collectorCommandEnum = CollectorCommandEnum.getById(commandId);
        if (Objects.isNull(collectorCommandEnum)) {
            return false;
        }
        String commandTemplate = collectorCommandEnum.getCommandTemplate();
        JSONObject jsonObject = JSONUtil.parseObj(commandTemplate);
        jsonObject.set(CommandConstant.VALUE, commandValue);
        String topic = StrUtil.format(collectorCommandEnum.getTopic(), mac);
        // 10 位随机数，用于校验指令结果
        String cmmdId = RandomUtil.randomString(CommandConstant.RANDOM_LENGTH);
        // 添加到指令 json 中，记录发送指令的内容
        jsonObject.set(CommandConstant.CMMD_ID, cmmdId);
        // 发送指令
        mqttClient.publish(topic, jsonObject.toString());
        // 远程控制参数
        RemoteControlDTO remoteControlDTO = new RemoteControlDTO();
        String remoteControlJsonStr = ParamCache.getValue(ParamConstant.COLLECTOR_REMOTE_CONTROL);
        if (StrUtil.isNotBlank(remoteControlJsonStr)) {
            remoteControlDTO = JSONUtil.toBean(remoteControlJsonStr, RemoteControlDTO.class);
        }
        int loopCount = remoteControlDTO.getLoopCount();
        int loopInterval = remoteControlDTO.getLoopInterval();
        // 轮询判断指令回复结果
        for (int loopNum = 0; loopNum < loopCount; loopNum++) {
            ThreadUtil.sleep(loopInterval);
            // 校验指令结果
            boolean cmdResult = ControlUtil.validateCollectorCommandResult(mac, cmmdId);
            if (cmdResult) {
                return true;
            }
        }
        return false;
    }

}

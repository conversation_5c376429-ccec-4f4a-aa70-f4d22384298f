package org.springblade.thingcom.statistic.vo;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.EqualsAndHashCode;
import org.springblade.thingcom.statistic.entity.GatewayOperationLog;

/**
 * 网关操作日志出参类
 *
 * <AUTHOR>
 * @since 2024-03-12 17:36:56
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties({"deleted", "updateTime", "updateId"})
public class GatewayOperationLogVO extends GatewayOperationLog {
    private static final long serialVersionUID = 1L;

    /**
     * 状态，详见字典表 battery_status
     */
    private String statusName;

}

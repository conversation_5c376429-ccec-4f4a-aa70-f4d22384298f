package org.springblade.thingcom.statistic.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.thingcom.statistic.entity.RobotMotor;

/**
 * 机器人电机实时数据出参类
 *
 * <AUTHOR>
 * @since 2024-03-08 14:01:58
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties({"deleted", "updateTime", "updateId"})
public class RobotMotorVO extends RobotMotor {
    private static final long serialVersionUID = 1L;

    /**
     * 状态，详见字典表 motor_status
     */
    private String statusName;

    /**
     * 机器人名称
     */
    public String robotName;

    /**
     * 机器人区域id
     */
    public Long robotRegionalId;

    /**
     * 机器人区域编号
     */
    public String robotRegionalNumber;

}

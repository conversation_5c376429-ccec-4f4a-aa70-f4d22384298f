package org.springblade.thingcom.statistic.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.thingcom.statistic.dto.MotorLogExportDTO;
import org.springblade.thingcom.statistic.dto.RobotMotorLogDTO;
import org.springblade.thingcom.statistic.service.RobotMotorLogService;
import org.springblade.thingcom.statistic.vo.RobotMotorLogVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 机器人电机数据日志控制层
 *
 * <AUTHOR>
 * @since 2024-03-08 16:02:47
 */
@AllArgsConstructor
@RestController
@RequestMapping("/robotMotorLog")
public class RobotMotorLogController extends BladeController {
    private final RobotMotorLogService robotMotorLogService;

    /**
     * 机器人电机数据日志分页查询
     *
     * @param query
     * @param dto
     * @return
     */
    @GetMapping("/page")
    public R<IPage<RobotMotorLogVO>> page(Query query, RobotMotorLogDTO dto) {
        return data(robotMotorLogService.page(Condition.getPage(query), dto));
    }

    /**
     * 导出机器人电机历史
     *
     * @param response
     * @param dto
     * @return void
     */
    @GetMapping("/export-data")
    public void exportData(HttpServletResponse response, MotorLogExportDTO dto) throws IOException {
        robotMotorLogService.exportData(response, dto);
    }

}

package org.springblade.thingcom.statistic.controller;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.log.annotation.ApiLog;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.thingcom.core.excel.handler.AutoColumnWidthHandler;
import org.springblade.thingcom.core.excel.handler.ExportHeaderCellHandler;
import org.springblade.thingcom.core.excel.utils.ThingcomExcelUtil;
import org.springblade.thingcom.statistic.dto.GatewayOperationLogDTO;
import org.springblade.thingcom.statistic.entity.GatewayOperationLog;
import org.springblade.thingcom.statistic.excel.GatewayOperationLogExcel;
import org.springblade.thingcom.statistic.service.GatewayOperationLogService;
import org.springblade.thingcom.statistic.vo.GatewayOperationLogVO;
import org.springblade.thingcom.statistic.wrapper.GatewayOperationLogWrapper;
import org.springblade.thingcom.translate.utils.TranslateUtil;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * 网关操作日志控制层
 *
 * <AUTHOR>
 * @since 2024-03-12 17:36:55
 */
@AllArgsConstructor
@RestController
@RequestMapping("/gatewayOperationLog")
public class GatewayOperationLogController extends BladeController {
    private final GatewayOperationLogService gatewayOperationLogService;

    /**
     * 网关操作日志分页查询
     *
     * @param query
     * @param dto
     * @return
     */
    @GetMapping("/page")
    public R<IPage<GatewayOperationLogVO>> page(Query query, GatewayOperationLogDTO dto) {
        LambdaQueryWrapper<GatewayOperationLog> wrapper = Wrappers.<GatewayOperationLog>lambdaQuery();
        wrapper.eq(StrUtil.isNotBlank(dto.getCreateBy()), GatewayOperationLog::getCreateBy, dto.getCreateBy());
        wrapper.like(StrUtil.isNotBlank(dto.getPhone()), GatewayOperationLog::getPhone, dto.getPhone());
        if (Objects.nonNull(dto.getBeginDate()) && Objects.nonNull(dto.getEndDate())) {
            wrapper.between(GatewayOperationLog::getCreateTime, dto.getBeginDate(), dto.getEndDate());
        }
        wrapper.orderByDesc(GatewayOperationLog::getCreateTime);
        IPage<GatewayOperationLog> iPage = gatewayOperationLogService.page(Condition.getPage(query), wrapper);
        return data(GatewayOperationLogWrapper.build().pageVO(iPage));
    }

    /**
     * 网关操作日志列表查询
     *
     * @param dto
     * @return
     */
    @GetMapping("/list")
    public R<List<GatewayOperationLogVO>> list(GatewayOperationLogDTO dto) {
        LambdaQueryWrapper<GatewayOperationLog> wrapper = GatewayOperationLogWrapper.build().setQueryParam(dto);
        List<GatewayOperationLog> list = gatewayOperationLogService.list(wrapper);
        return data(GatewayOperationLogWrapper.build().listVO(list));
    }

    /**
     * 导出通讯网关操作记录
     *
     * @param response
     * @param dto
     * @return void
     */
    @GetMapping("/export-data")
    public void exportData(HttpServletResponse response, GatewayOperationLogDTO dto) throws IOException {
        LambdaQueryWrapper<GatewayOperationLog> wrapper = GatewayOperationLogWrapper.build().setQueryParam(dto);
        List<org.springblade.thingcom.statistic.entity.GatewayOperationLog> list
                = gatewayOperationLogService.list(wrapper);
        // 数据列表转excel
        String sheetName = "通讯网关操作记录数据表";
        String fileName = "通讯网关操作记录数据表";

        // 判断是否需要翻译(该导出是在请求头里面带上了lang参数)
        boolean translate = TranslateUtil.isTranslate();
        // 表内数据常量翻译
        List<GatewayOperationLogExcel> data = GatewayOperationLogWrapper.build().listToExcel(list, translate);
        // 表头翻译
        List<List<String>> headerList = ThingcomExcelUtil.getHeadList(GatewayOperationLogExcel.class, translate);
        // translate为真，翻译
        if (translate) {
            // 表名的翻译
            sheetName = TranslateUtil.chineseToEnglish(sheetName);
            // 文件名翻译
            fileName = TranslateUtil.chineseToEnglish(fileName);
        }
        ThingcomExcelUtil.setExportDataFile(response, fileName);
        EasyExcel.write(response.getOutputStream(), GatewayOperationLogExcel.class)
                .inMemory(true)
                .registerWriteHandler(new AutoColumnWidthHandler())
                .head(headerList)
                .registerWriteHandler(new ExportHeaderCellHandler())
                .sheet(sheetName).doWrite(data);
    }

    /**
     * 查看网关操作日志详情
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public R<GatewayOperationLogVO> show(@PathVariable Serializable id) {
        GatewayOperationLog entity = gatewayOperationLogService.getById(id);
        return data(GatewayOperationLogWrapper.build().entityVO(entity));
    }

    /**
     * 新增网关操作日志
     *
     * @param entity
     * @return
     */
    @ApiLog("新增网关操作日志")
    @PostMapping
    public R save(@Valid @RequestBody GatewayOperationLog entity) {
        // 检查数据合法性
        gatewayOperationLogService.validateData(entity);
        return status(gatewayOperationLogService.save(entity));
    }

    /**
     * 修改网关操作日志
     *
     * @param entity
     * @return
     */
    @ApiLog("修改网关操作日志")
    @PutMapping
    public R update(@Valid @RequestBody GatewayOperationLog entity) {
        Assert.notNull(entity.getId(), "id不能为空");
        // 检查数据合法性
        gatewayOperationLogService.validateData(entity);
        return status(gatewayOperationLogService.updateById(entity));
    }

}

package org.springblade.thingcom.statistic.wrapper;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.thingcom.statistic.dto.ApiLogDTO;
import org.springblade.thingcom.statistic.entity.ApiLog;
import org.springblade.thingcom.statistic.excel.ApiLogExcel;
import org.springblade.thingcom.statistic.vo.ApiLogVO;
import org.springblade.thingcom.translate.utils.TranslateUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 接口日志包装类
 *
 * <AUTHOR>
 * @since 2024-03-12 16:57:14
 */
public class ApiLogWrapper extends BaseEntityWrapper<ApiLog, ApiLogVO> {

    public static ApiLogWrapper build() {
        return new ApiLogWrapper();
    }

    /**
     * 返回视图层所需的字段
     *
     * @param entity
     * @return ApiLogVO
     */
    @Override
    public ApiLogVO entityVO(ApiLog entity) {
        if (Objects.isNull(entity)) {
            return null;
        }
        return BeanUtil.copyProperties(entity, ApiLogVO.class);
    }

    /**
     * 设置查询参数
     *
     * @param dto
     * @return LambdaQueryWrapper<ApiLog>
     */
    public LambdaQueryWrapper<ApiLog> setQueryParam(ApiLogDTO dto) {
        LambdaQueryWrapper<ApiLog> lqw = new LambdaQueryWrapper<>();
        // 查询数据
        lqw.select(ApiLog::getCreateBy, ApiLog::getTitle, ApiLog::getCreateTime);
        // 上下线记录
        List<String> apiUrlList = new ArrayList<>(2);
        apiUrlList.add(CommonConstant.LOGIN_API);
        apiUrlList.add(CommonConstant.LOGOUT_API);
        lqw.in(ApiLog::getRequestUri, apiUrlList);
        // 运维人员
        lqw.eq(StrUtil.isNotBlank(dto.getCreateBy()), ApiLog::getCreateBy, dto.getCreateBy());
        // 时间
        if (Objects.nonNull(dto.getBeginDate()) && Objects.nonNull(dto.getEndDate())) {
            lqw.between(ApiLog::getCreateTime, dto.getBeginDate(), dto.getEndDate());
        }
        // 时间倒序排序
        lqw.orderByDesc(ApiLog::getCreateTime);
        return lqw;
    }

    public List<ApiLogExcel> listToExcel(List<ApiLog> list, Boolean translate) {
        return list.stream().map(m -> {
            ApiLogExcel excel = BeanUtil.copyProperties(m, ApiLogExcel.class);
            // translate为真，翻译
            if (translate && StrUtil.isNotBlank(excel.getTitle())) {
                excel.setTitle(TranslateUtil.chineseToEnglish(excel.getTitle()));
            }
            return excel;
        }).collect(Collectors.toList());
    }
}

package org.springblade.thingcom.statistic.wrapper;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.thingcom.communication.enums.BatteryStatusEnum;
import org.springblade.thingcom.statistic.entity.RobotBatteryLog;
import org.springblade.thingcom.statistic.vo.RobotBatteryLogVO;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 机器人电池数据日志包装类
 *
 * <AUTHOR>
 * @since 2024-03-08 14:43:21
 */
public class RobotBatteryLogWrapper extends BaseEntityWrapper<RobotBatteryLog, RobotBatteryLogVO> {

    public static RobotBatteryLogWrapper build() {
        return new RobotBatteryLogWrapper();
    }

    /**
     * 返回视图层所需的字段
     *
     * @param entity
     * @return RobotBatteryLogVO
     */
    @Override
    public RobotBatteryLogVO entityVO(RobotBatteryLog entity) {
        if (Objects.isNull(entity)) {
            return null;
        }
        RobotBatteryLogVO vo = BeanUtil.toBean(entity, RobotBatteryLogVO.class);
        // 电池状态
        vo.setStatusName(BatteryStatusEnum.translationValue(vo.getStatus()));
        return vo;
    }

    public RobotBatteryLogVO entityVoExt(Map<String, Object> map) {
        if (null == map) {
            return null;
        }
        RobotBatteryLogVO vo = BeanUtil.toBean(map, RobotBatteryLogVO.class);
        // 电池状态
        vo.setStatusName(BatteryStatusEnum.translationValue(vo.getStatus()));
        return vo;
    }

    public List<RobotBatteryLogVO> listVoExt(List<Map<String, Object>> list) {
        return list.stream().map(this::entityVoExt).collect(Collectors.toList());
    }

    public IPage<RobotBatteryLogVO> pageVoExt(IPage<Map<String, Object>> pages) {
        List<RobotBatteryLogVO> records = this.listVoExt(pages.getRecords());
        IPage<RobotBatteryLogVO> pageVo = new Page(pages.getCurrent(), pages.getSize(), pages.getTotal());
        pageVo.setRecords(records);
        return pageVo;
    }

}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.thingcom.statistic.mapper.RobotOperationLogMapper">

    <resultMap type="org.springblade.thingcom.statistic.entity.RobotOperationLog" id="RobotOperationLogMap">
        <result column="id" property="id"/>
        <result column="robot_id" property="robotId"/>
        <result column="content" property="content"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="creator_id" property="creatorId"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_id" property="updateId"/>
        <result column="is_deleted" property="deleted"/>
    </resultMap>

</mapper>

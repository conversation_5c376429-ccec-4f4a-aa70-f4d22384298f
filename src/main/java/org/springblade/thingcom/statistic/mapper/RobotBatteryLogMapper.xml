<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.thingcom.statistic.mapper.RobotBatteryLogMapper">
    <resultMap type="org.springblade.thingcom.statistic.entity.RobotBatteryLog" id="RobotBatteryLogMap">
        <result column="id" property="id"/>
        <result column="robot_id" property="robotId"/>
        <result column="robot_regional_id" property="robotRegionalId"/>
        <result column="robot_regional_number" property="robotRegionalNumber"/>
        <result column="robot_number" property="robotNumber"/>
        <result column="robot_name" property="robotName"/>
        <result column="clearing_path_number" property="clearingPathNumber"/>
        <result column="remaining_capacity" property="remainingCapacity"/>
        <result column="voltage" property="voltage"/>
        <result column="electricity" property="electricity"/>
        <result column="temperature" property="temperature"/>
        <result column="time" property="time"/>
        <result column="status" property="status"/>
        <result column="creator_id" property="creatorId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_id" property="updateId"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="deleted"/>
    </resultMap>
</mapper>

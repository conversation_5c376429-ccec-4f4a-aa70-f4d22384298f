package org.springblade.thingcom.statistic.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 机器人电机实时数据excel类
 *
 * <AUTHOR>
 * @since 2024-03-08 14:01:58
 */
@Data
@ColumnWidth(16)
@HeadRowHeight(52)
@ContentRowHeight(18)
public class RobotMotorExcel implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 机器人区域编号
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "机器人区域编号")
    public String robotRegionalNumber;

    /**
     * 机器人名称
     */
    @ExcelProperty(value = "机器人名称")
    public String robotName;

    /**
     * 主电机电流，单位 mA
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "主电机电流(mA)")
    private BigDecimal masterElectricity;

    /**
     * 从电机电流，单位 mA
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "从电机电流(mA)")
    private BigDecimal slaveElectricity;

    /**
     * 毛刷电流，单位 mA
     */
    //@ColumnWidth(25)
    //@ExcelProperty(value = "毛刷电流(mA)")
    //private BigDecimal brushElectricity;

    /**
     * 电压，单位 V
     */
    //@ColumnWidth(25)
    //@ExcelProperty(value = "电压(V)")
    //private BigDecimal voltage;

    /**
     * 机器运行速率，单位 m/s
     */
    //@ColumnWidth(25)
    //@ExcelProperty(value = "机器运行速率(m/s)")
    //private BigDecimal runRate;

    /**
     * 状态，详见字典表 motor_status
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "状态")
    private String status;

    /**
     * 时间
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "时间")
    private LocalDateTime time;
}

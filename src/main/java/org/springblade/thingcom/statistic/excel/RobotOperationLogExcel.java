package org.springblade.thingcom.statistic.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 机器人操作日志excel类
 *
 * <AUTHOR>
 * @since 2024-03-12 17:17:00
 */
@Data
@ColumnWidth(16)
@HeadRowHeight(52)
@ContentRowHeight(18)
public class RobotOperationLogExcel implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 机器人区域编号
     */
    @ExcelProperty(value = "机器人区域编号")
    private String robotRegionalNumber;

    /**
     * 机器人名称
     */
    @ExcelProperty(value = "机器人名称")
    public String robotName;

    /**
     * 操作
     */
    @ExcelProperty(value = "操作")
    private String title;

    /**
     * 操作内容
     */
    @ColumnWidth(35)
    @ExcelProperty(value = "操作内容")
    private String content;

    /**
     * 操作结果
     */
    @ExcelProperty(value = "操作结果")
    private String status;

    /**
     * 运维人员 （详见用户blade_user缓存表）
     */
    @ExcelProperty(value = "运维人员")
    private String createBy;

    /**
     * 时间
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "时间")
    private LocalDateTime createTime;

}

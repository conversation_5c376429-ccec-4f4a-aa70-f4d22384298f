package org.springblade.thingcom.statistic.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import org.springblade.thingcom.core.dynamicDatasource.service.DynamicDatatableService;
import org.springblade.thingcom.core.excel.handler.AutoColumnWidthHandler;
import org.springblade.thingcom.core.excel.utils.ThingcomExcelUtil;
import org.springblade.thingcom.core.mybatisPlus.impl.BaseServiceImpl;
import org.springblade.thingcom.data.enums.DynamicDatatableEnum;
import org.springblade.thingcom.device.entity.Robot;
import org.springblade.thingcom.device.service.RobotService;
import org.springblade.thingcom.statistic.dto.MotorLogExportDTO;
import org.springblade.thingcom.statistic.dto.RobotMotorLogDTO;
import org.springblade.thingcom.statistic.entity.RobotMotorLog;
import org.springblade.thingcom.statistic.mapper.RobotMotorLogMapper;
import org.springblade.thingcom.statistic.service.RobotMotorLogService;
import org.springblade.thingcom.statistic.vo.RobotBatteryLogVO;
import org.springblade.thingcom.statistic.vo.RobotMotorLogVO;
import org.springblade.thingcom.statistic.wrapper.RobotMotorLogWrapper;
import org.springblade.thingcom.translate.utils.TranslateUtil;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 机器人电机数据日志服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-08 16:02:48
 */
@Service
@AllArgsConstructor
public class RobotMotorLogServiceImpl extends BaseServiceImpl<RobotMotorLogMapper, RobotMotorLog> implements RobotMotorLogService {

    private final DynamicDatatableService dynamicDatatableService;
    private final RobotService robotService;

    @Override
    public IPage<RobotMotorLogVO> page(IPage<Object> page, RobotMotorLogDTO dto) {
        String tableNamePrefix = DynamicDatatableEnum.ROBOT_MOTOR_LOG.getValue();
        IPage<Map<String, Object>> iPage = dynamicDatatableService.page(page, tableNamePrefix, dto);
        return RobotMotorLogWrapper.build().pageVoExt(iPage);
    }

    @Override
    public void exportData(HttpServletResponse response, MotorLogExportDTO dto) throws IOException {
        // 判断是否需要翻译
        boolean translate = TranslateUtil.isTranslate(dto.getLang());
        Robot robot = robotService.getById(dto.getRobotId());
        // 设置表头
        List<List<String>> head = new ArrayList<>();
        List<String> headRow = new ArrayList<>();
        if (translate) {
            headRow.add("Time");
        } else {
            headRow.add("时间");
        }
        head.add(headRow);
        // 设置表体
        List<List<String>> body = new ArrayList<>();
        // 查询数据
        String tableNamePrefix = DynamicDatatableEnum.ROBOT_MOTOR_LOG.getValue();
        List<Map<String, Object>> list = dynamicDatatableService.list(tableNamePrefix, dto);
        List<RobotMotorLogVO> listWrapper = RobotMotorLogWrapper.build().listVoExt(list);
        // 根据机器人名称筛选机器人
        List<RobotMotorLogVO> dataList = listWrapper.stream().filter(d -> d.getRobotName().equals(robot.getName()))
                .collect(Collectors.toList());
        // 查询机器人名称，去重并排序
        List<String> robotNameList = dataList.stream().map(RobotMotorLog::getRobotName)
                .distinct().sorted().collect(Collectors.toList());
        // 查询时间，时间去重并倒序
        List<LocalDateTime> timeList = dataList.stream().map(RobotMotorLog::getTime)
                .distinct().sorted(Comparator.reverseOrder()).collect(Collectors.toList());
        // 根据时间获取当前时间的所有数据，添加表体数据
        for (LocalDateTime time : timeList) {
            List<String> bodyRow = new ArrayList<>();
            String timeStr = time.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            bodyRow.add(timeStr);
            // 查询数据列表中，该时间点对应的机器人数据
            List<RobotMotorLog> robotDataList = dataList.stream().filter(d -> d.getTime().equals(time))
                    .collect(Collectors.toList());
            for (String name : robotNameList) {
                // 查询该时间点、机器人名称对应的数据
                RobotMotorLog robotMotorLog = robotDataList.stream().filter(d -> d.getRobotName().equals(name))
                        .findFirst().orElse(null);
                // 主电机电流赋值
                if (StrUtil.isNotBlank(dto.getMasterElectricity())) {
                    BigDecimal masterElectricity = null;
                    if (Objects.nonNull(robotMotorLog)) {
                        masterElectricity = robotMotorLog.getMasterElectricity();
                    }
                    bodyRow.add(Objects.isNull(masterElectricity) ? "" : String.valueOf(masterElectricity));
                }
                // 从电机电流赋值
                if (StrUtil.isNotBlank(dto.getSlaveElectricity())) {
                    BigDecimal slaveElectricity = null;
                    if (Objects.nonNull(robotMotorLog)) {
                        slaveElectricity = robotMotorLog.getSlaveElectricity();
                    }
                    bodyRow.add(Objects.isNull(slaveElectricity) ? "" : String.valueOf(slaveElectricity));
                }
                // 毛刷电机电流赋值
                if (StrUtil.isNotBlank(dto.getBrushElectricity())) {
                    BigDecimal brushElectricity = null;
                    if (Objects.nonNull(robotMotorLog)) {
                        brushElectricity = robotMotorLog.getBrushElectricity();
                    }
                    bodyRow.add(Objects.isNull(brushElectricity) ? "" : String.valueOf(brushElectricity));
                }
                // 机器运行速率赋值
                if (dto.getRunRate() != null) {
                    BigDecimal runRate = null;
                    if (Objects.nonNull(robotMotorLog)) {
                        runRate = robotMotorLog.getRunRate();
                    }
                    bodyRow.add(Objects.isNull(runRate) ? "" : String.valueOf(runRate));
                }
            }
            body.add(bodyRow);
        }
        // 根据前端勾选四种导出方式添加动态表头
        for (String robotName : robotNameList) {
            if (StrUtil.isNotBlank(dto.getMasterElectricity())) {
                headRow = new ArrayList<>();
                // 判断是否翻译
                if (translate) {
                    headRow.add(robotName + "MasterElectricity");
                } else {
                    headRow.add(robotName + "主电机电流");
                }
                head.add(headRow);
            }
            if (StrUtil.isNotBlank(dto.getSlaveElectricity())) {
                headRow = new ArrayList<>();
                // 判断是否翻译
                if (translate) {
                    headRow.add(robotName + "SlaveElectricity");
                } else {
                    headRow.add(robotName + "从电机电流");
                }
                head.add(headRow);
            }
            if (StrUtil.isNotBlank(dto.getBrushElectricity())) {
                headRow = new ArrayList<>();
                // 判断是否翻译
                if (translate) {
                    headRow.add(robotName + "BrushElectricity");
                } else {
                    headRow.add(robotName + "毛刷电机电流");
                }
                head.add(headRow);
            }
            if (dto.getRunRate() != null) {
                headRow = new ArrayList<>();
                // 判断是否翻译
                if (translate) {
                    headRow.add(robotName + "Operating Rate");
                } else {
                    headRow.add(robotName + "运行速率");
                }
                head.add(headRow);
            }
        }
        String sheetName = "机器人电机历史数据表";
        String fileName = "机器人电机历史数据";
        if (translate) {
            sheetName = TranslateUtil.chineseToEnglish(sheetName);
            fileName = TranslateUtil.chineseToEnglish(fileName);
        }
        ThingcomExcelUtil.setExportDataFile(response, fileName);
        EasyExcel.write(response.getOutputStream())
                .inMemory(true)
                .registerWriteHandler(new AutoColumnWidthHandler())
                .head(head).sheet(sheetName).doWrite(body);
        // 清空输出流并关闭
        response.flushBuffer();
    }

}

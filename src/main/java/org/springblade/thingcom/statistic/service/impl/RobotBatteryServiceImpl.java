package org.springblade.thingcom.statistic.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.springblade.thingcom.communication.enums.BatteryStatusEnum;
import org.springblade.thingcom.core.mybatisPlus.impl.BaseServiceImpl;
import org.springblade.thingcom.data.utils.RobotDataUtil;
import org.springblade.thingcom.device.entity.Robot;
import org.springblade.thingcom.home.excel.RobotBatteryStatusExcel;
import org.springblade.thingcom.statistic.entity.RobotBattery;
import org.springblade.thingcom.statistic.mapper.RobotBatteryMapper;
import org.springblade.thingcom.statistic.service.RobotBatteryService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 机器人电池实时数据服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-06 14:14:56
 */
@Service
@AllArgsConstructor
public class RobotBatteryServiceImpl extends BaseServiceImpl<RobotBatteryMapper, RobotBattery> implements RobotBatteryService {

	@Override
    public List<RobotBatteryStatusExcel> exportData() {
        return baseMapper.exportData();
    }

    @Override
    public void offlineToOnline(Robot robot) {
        // 查询机器人电池是否为离线状态
        RobotBattery robotBattery = getOne(Wrappers.<RobotBattery>lambdaQuery()
                .eq(RobotBattery::getRobotId, robot.getId())
                .eq(RobotBattery::getStatus, BatteryStatusEnum.OFFLINE.getValue()).last("LIMIT 1"));
        if (Objects.nonNull(robotBattery)) {
            // 查询电池状态
	        Integer batteryStatus = RobotDataUtil.getBatteryStatus(robot.getEui());
            if (Objects.isNull(batteryStatus)) {
                batteryStatus = BatteryStatusEnum.NORMAL.getValue();
            }
	        // 更新机器人电池的状态
            update(Wrappers.<RobotBattery>lambdaUpdate()
                    .set(RobotBattery::getStatus, batteryStatus)
                    .eq(RobotBattery::getRobotId, robot.getId()));
        }
    }

}
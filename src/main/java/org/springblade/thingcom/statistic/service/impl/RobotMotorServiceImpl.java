package org.springblade.thingcom.statistic.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.springblade.thingcom.communication.enums.MotorStatusEnum;
import org.springblade.thingcom.core.mybatisPlus.impl.BaseServiceImpl;
import org.springblade.thingcom.data.constant.StatusAnalysisIdConstant;
import org.springblade.thingcom.statistic.entity.RobotMotor;
import org.springblade.thingcom.statistic.mapper.RobotMotorMapper;
import org.springblade.thingcom.statistic.service.RobotMotorService;
import org.springblade.thingcom.warning.entity.RobotAlarm;
import org.springblade.thingcom.warning.service.RobotAlarmService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 机器人电机实时数据服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-08 14:01:58
 */
@Service
@AllArgsConstructor
public class RobotMotorServiceImpl extends BaseServiceImpl<RobotMotorMapper, RobotMotor> implements RobotMotorService {

    private final RobotAlarmService robotAlarmService;

    @Override
    public void offlineToOnline(Long robotId) {
        // 机器人电机离线转上线，先查询是否存在电机故障
        List<RobotAlarm> robotMotorAlarmList = robotAlarmService.list(Wrappers.<RobotAlarm>lambdaQuery()
                .select(RobotAlarm::getId)
                .eq(RobotAlarm::getRobotId, robotId)
                .in(RobotAlarm::getStatusAnalysisId, StatusAnalysisIdConstant.MOTOR_ALARM_STATUS_ANALYSIS_ID_LIST));
        // 默认正常，有故障则设置为故障
        Integer status = MotorStatusEnum.NORMAL.getValue();
        if (CollUtil.isNotEmpty(robotMotorAlarmList)) {
            List<Long> idList = robotMotorAlarmList
                    .stream()
                    .map(RobotAlarm::getStatusAnalysisId)
                    .collect(Collectors.toList());
            status = idList.contains(StatusAnalysisIdConstant.ID_3009)
                    ? MotorStatusEnum.MASTER_CURRENT_UPPER.getValue()
                    : MotorStatusEnum.SLAVE_CURRENT_UPPER.getValue();
        }
        // 更新机器人电机的状态
        update(Wrappers.<RobotMotor>lambdaUpdate()
                .set(RobotMotor::getStatus, status)
                .eq(RobotMotor::getRobotId, robotId));
    }

    @Override
    public void relieveAlarm(Long robotId) {
        // 机器人电机离线转上线，先查询是否存在电机故障
        List<RobotAlarm> robotMotorAlarmList = robotAlarmService.list(Wrappers.<RobotAlarm>lambdaQuery()
                .select(RobotAlarm::getId)
                .eq(RobotAlarm::getRobotId, robotId)
                .in(RobotAlarm::getStatusAnalysisId, StatusAnalysisIdConstant.MOTOR_ALARM_STATUS_ANALYSIS_ID_LIST));
        // 不存在故障，则更新机器人电机的状态为正常
        if (CollUtil.isEmpty(robotMotorAlarmList)) {
            // 更新机器人电机的状态
            update(Wrappers.<RobotMotor>lambdaUpdate()
                    .set(RobotMotor::getStatus, MotorStatusEnum.NORMAL.getValue())
                    .eq(RobotMotor::getRobotId, robotId));
        }
    }

}
package org.springblade.thingcom.statistic.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.thingcom.core.base.dto.BaseDTO;

/**
 * 机器人电池数据日志导出类
 *
 * <AUTHOR>
 * @since 2024-03-08 14:43:21
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BatteryLogExportDTO extends BaseDTO {
    private static final long serialVersionUID = 1L;

    /**
     * 机器人id
     */
    private Long robotId;

    /**
     * 机器人分区id
     */
    private Long robotRegionalId;

    /**
     * 机器人区域编号
     */
    private String robotRegionalNumber;

    /**
     * 机器人名称
     */
    private String robotName;

    /**
     * 清扫路径编号
     */
    private String clearingPathNumber;

    /**
     * 电压，单位 V
     */
    private String voltage;

    /**
     * 温度，单位 ℃
     */
    private String temperature;

    /**
     * 剩余电量 %
     */
    private String remainingCapacity;

    /**
     * 电池状态，详见字典表
     */
    private String status;

}

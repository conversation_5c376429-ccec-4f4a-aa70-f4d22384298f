package org.springblade.thingcom.statistic.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.thingcom.core.base.dto.BaseDTO;

/**
 * 接口日志入参类
 *
 * <AUTHOR>
 * @since 2024-03-12 16:57:14
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ApiLogDTO extends BaseDTO {
    private static final long serialVersionUID = 1L;

    /**
     * 运维人员id
     */
    private Long createUserId;

    /**
     * 创建者（blade_user 表中的 account 字段）
     */
    private String createBy;
}

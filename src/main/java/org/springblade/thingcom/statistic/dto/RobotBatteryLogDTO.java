package org.springblade.thingcom.statistic.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.thingcom.core.base.dto.BaseDTO;

/**
 * 机器人电池数据日志入参类
 *
 * <AUTHOR>
 * @since 2024-03-08 14:43:21
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RobotBatteryLogDTO extends BaseDTO {
    private static final long serialVersionUID = 1L;

    /**
     * 机器人id
     */
    private Long robotId;

    /**
     * 机器人分区id
     */
    private Long robotRegionalId;

    /**
     * 机器人区域编号
     */
    private String robotRegionalNumber;

    /**
     * 机器人名称
     */
    private String robotName;

    /**
     * 清扫路径编号
     */
    private String clearingPathNumber;
}

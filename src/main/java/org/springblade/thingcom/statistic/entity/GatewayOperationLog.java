package org.springblade.thingcom.statistic.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;
import org.springblade.thingcom.core.mybatisPlus.entity.BaseEntity;

import java.io.Serializable;

/**
 * 网关操作日志实体类
 *
 * <AUTHOR>
 * @since 2024-03-13 15:42:11
 */
@Data
@TableName("tl_gateway_operation_log")
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class GatewayOperationLog extends BaseEntity<GatewayOperationLog> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 网关id
     */
    private Long gatewayId;

    /**
     * 网关编号
     */
    @Length(max = 50, message = "网关编号长度不能超过50")
    private String gatewayNumber;

    /**
     * 操作
     */
    @Length(max = 100, message = "操作长度不能超过100")
    private String title;

    /**
     * 操作内容
     */
    @Length(max = 200, message = "操作内容长度不能超过200")
    private String content;

    /**
     * 状态，1-成功；2-失败
     */
    private Integer status;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 创建人
     */
    private String createBy;
}

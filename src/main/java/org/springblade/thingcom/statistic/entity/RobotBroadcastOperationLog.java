package org.springblade.thingcom.statistic.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springblade.thingcom.core.mybatisPlus.entity.BaseEntity;
import org.springblade.thingcom.translate.annotation.TranslateCrtl;

import java.io.Serializable;

/**
 * 机器人广播操作日志表
 *
 * <AUTHOR>
 * @since 2024-12-18 16:41:37
 */
@Data
@TableName("tl_robot_broadcast_operation_log")
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class RobotBroadcastOperationLog extends BaseEntity<RobotBroadcastOperationLog> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 机器人id
     */
    private Long robotId;

    /**
     * 机器人分区id
     */
    private Long robotRegionalId;

    /**
     * 机器人分区编号
     */
    private String robotRegionalNumber;

    /**
     * 机器人编号
     */
    private String robotNumber;

    /**
     * 机器人名称
     */
    private String robotName;

    /**
     * 监控点id
     */
    private String streamId;

    /**
     * 监控点值
     */
    private String value;

    /**
     * 操作
     */
    private String title;

    /**
     * 操作内容
     */
    @TranslateCrtl(translateMode = 2)
    private String content;

    /**
     * 状态，0-失败；1-成功
     */
    private Integer status;

    /**
     * 创建人
     */
    private String createBy;
}

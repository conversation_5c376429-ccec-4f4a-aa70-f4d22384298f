package org.springblade.thingcom.translate.aspect;

import com.fasterxml.jackson.databind.introspect.AnnotatedClass;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springblade.core.tool.api.R;
import org.springblade.thingcom.translate.annotation.TranslateCrtl;
import org.springblade.thingcom.translate.enums.TranslateModeEnum;
import org.springblade.thingcom.translate.utils.TranslateUtil;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Field;
import java.util.*;

/**
 * 翻译统一拦截处理类
 *
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
@ConditionalOnProperty(prefix = "translate", name = "open", havingValue = "true")
//@ConfigurationProperties(prefix = "untranslated")
public class LanguageAspect {

    private final List<String> myPackageNameList = new ArrayList<>();

    @Pointcut("@annotation(org.springframework.web.bind.annotation.GetMapping)||" +
            "@annotation(org.springframework.web.bind.annotation.PostMapping)||" +
            "@annotation(org.springframework.web.bind.annotation.PutMapping)")
    public void translateReturnInfo() {
    }

    @Pointcut("execution(* org.springblade.thingcom..*.*(..)) && !within(org.springblade.thingcom.core..*)")
    public void pointcut() {
    }

    /**
     * 异常信息翻译
     *
     * @param joinPoint
     * @return
     * @throws Throwable
     */
    @Around("pointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        try {
            return joinPoint.proceed();
        } catch (Exception e) {
            //从获取RequestAttributes中获取HttpServletRequest的信息
            //HttpServletRequest request = (HttpServletRequest) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())
            //        .resolveReference(RequestAttributes.REFERENCE_REQUEST);

            RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
            if (requestAttributes != null) {
                HttpServletRequest request = (HttpServletRequest) requestAttributes
                        .resolveReference(RequestAttributes.REFERENCE_REQUEST);
                if (Objects.nonNull(request)) {
                    boolean isTranslate = TranslateUtil.isTranslate();
                    if (isTranslate) {
                        String messageTranlate = TranslateUtil.chineseToEnglish(e.getMessage());
                        throw new RuntimeException(messageTranlate);
                    } else {
                        log.error("翻译切面异常", e);
                        throw new RuntimeException(e.getMessage());
                    }
                }
            }
            return joinPoint.proceed();
        }
    }

    /**
     * 在构造函数中进行初始化，后面可从配置文件中初始化
     */
    public LanguageAspect() {
        myPackageNameList.add("org.springblade.modules.");
        myPackageNameList.add("org.springblade.thingcom.");
        myPackageNameList.add("org.springblade.core.");
        myPackageNameList.add("com.baomidou.mybatisplus.");
    }

    /**
     * 拦截controller层返回的结果，修改msg字段
     *
     * @param point
     * @param obj
     */
    @AfterReturning(pointcut = "translateReturnInfo()", returning = "obj")
    public void translateReturnInfo(JoinPoint point, Object obj) {
        Object backObj = obj;
        try {
            //从获取RequestAttributes中获取HttpServletRequest的信息
            HttpServletRequest request = (HttpServletRequest) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())
                    .resolveReference(RequestAttributes.REFERENCE_REQUEST);
            if (request == null) return;
            boolean isTranslate = TranslateUtil.isTranslate();
            String objPreFix = "R";
            if (isTranslate && Objects.nonNull(obj) && obj.toString().startsWith(objPreFix)) {
                // 目前只翻译成英语
                if (obj instanceof R) {
                    translate(obj);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            //重新赋值给obj，防止try中途修改原始的值
            obj = backObj;
        }
    }

    /**
     * 判断是否是框架自带或者用户自己定义的类
     *
     * @param object
     * @return
     */
    private boolean isMyClass(Object object) {
        String targetName = object.getClass().getPackage().getName();
        for (String packageName : myPackageNameList) {
            if (targetName.startsWith(packageName)) return true;
        }
        return false;
    }

    /**
     * 对返回值进行中译英
     *
     * @param obj
     */
    private void translate(Object obj) throws NoSuchFieldException, IllegalAccessException {
        // Map类型数据
        if (obj instanceof Map) {
            translateMap(obj);
        }
        // Collection类型数据
        else if (obj instanceof Collection) {
            translateCollection(obj);
        }
        // 框架自带或者用户自己定义的类
        else if (isMyClass(obj)) {
            translateClass(obj);
        }
    }

    /**
     * 对非基本类中的中文字符进行翻译
     *
     * @param obj
     * @throws NoSuchFieldException
     * @throws IllegalAccessException
     */
    private void translateClass(Object obj) throws NoSuchFieldException, IllegalAccessException {
        Class<?> targetClass = obj.getClass();
        // 获取类的注解
        Class<AnnotatedClass> clazz = AnnotatedClass.class;
        if (targetClass.isAnnotationPresent(TranslateCrtl.class)) {
            TranslateCrtl annotation = clazz.getAnnotation(TranslateCrtl.class);
        }
        while (targetClass != null) {
            Field[] fields = targetClass.getDeclaredFields();
            for (Field field : fields) {
                if (getField(obj, targetClass, field.getName()) == null) {
                    continue;
                }
                Integer translateMode = TranslateModeEnum.FULL_TRANSLATE.getValue();
                if (field.getType() == String.class) {
                    field.setAccessible(true);
                    // 如果注解中isTranslate为0，则不翻译
                    if (field.isAnnotationPresent(TranslateCrtl.class)) {
                        TranslateCrtl annotation = field.getAnnotation(TranslateCrtl.class);
                        translateMode = annotation.translateMode();
                    }
                    setFieldValue(obj, targetClass, field.getName(), TranslateUtil.chineseToEnglish((String) getField(obj, targetClass, field.getName())
                            , translateMode));
                }
                // 遍历所有子类
                else {
                    translate(getField(obj, targetClass, field.getName()));
                }
            }
            targetClass = targetClass.getSuperclass();
        }
    }

    /**
     * 对集合中的中文字符进行翻译
     *
     * @param valueObject
     */
    private void translateCollection(Object valueObject) throws NoSuchFieldException, IllegalAccessException {
        if (valueObject instanceof Collection) {
            Collection<Object> collection = (Collection<Object>) valueObject;
            for (Object object : collection) {
                if (object instanceof String) {
                    collection.remove(object);
                    collection.add(TranslateUtil.chineseToEnglish((String) object));
                } else {
                    translate(object);
                }
            }
        }
    }

    /**
     * 对Map中的中文字符进行翻译
     *
     * @param object
     */
    private void translateMap(Object object) throws NoSuchFieldException, IllegalAccessException {
        for (Map.Entry<String, Object> entry : ((Map<String, Object>) object).entrySet()) {
            if (entry.getValue() == null) continue;
            if (entry.getValue() instanceof String) {
                entry.setValue(TranslateUtil.chineseToEnglish((String) entry.getValue()));
            } else {
                translate(entry.getValue());
            }
        }
    }

    /**
     * 设置字段的值
     *
     * @param obj
     * @param fieldName
     * @param value
     * @throws NoSuchFieldException
     * @throws IllegalAccessException
     */
    private void setFieldValue(Object obj, Class targetClass, String fieldName, Object value)
            throws NoSuchFieldException, IllegalAccessException {
        Field field = targetClass.getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(obj, value);
    }

    /**
     * 根据字段名获取对应的值
     *
     * @param object
     * @param fieldName
     * @return
     * @throws NoSuchFieldException
     * @throws IllegalAccessException
     */
    private Object getField(Object object, Class targetClass, String fieldName)
            throws NoSuchFieldException, IllegalAccessException {
        Field fields = targetClass.getDeclaredField(fieldName);
        fields.setAccessible(true);
        return fields.get(object);
    }

}

package org.springblade.thingcom.translate.wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.thingcom.translate.dto.TranslateDTO;
import org.springblade.thingcom.translate.entity.Translate;
import org.springblade.thingcom.translate.vo.TranslateVO;

import java.util.Objects;

/**
 * 翻译包装类
 *
 * <AUTHOR>
 * @since 2024-06-11 21:24:33
 */
public class TranslateWrapper extends BaseEntityWrapper<Translate, TranslateVO> {

	public static TranslateWrapper build() {
		return new TranslateWrapper();
	}

    /**
	 * 返回视图层所需的字段
	 *
	 * @param entity
	 * @return TranslateVO
	 */
	@Override
	public TranslateVO entityVO(Translate entity) {
	    if (Objects.isNull(entity)) {
	        return null;
	    }
		TranslateVO vo = BeanUtil.copy(entity, TranslateVO.class);
		return vo;
	}

    /**
	 * 设置查询参数
	 *
	 * @param dto
	 * @return LambdaQueryWrapper<Translate>
	 */
	public static LambdaQueryWrapper<Translate> setQueryParam(TranslateDTO dto) {
		LambdaQueryWrapper<Translate> lqw = new LambdaQueryWrapper<>();
		return lqw;
	}
}

package org.springblade.thingcom.core.scheduling.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 定时任务枚举
 */
@Getter
@AllArgsConstructor
public enum ScheduledCronEnum {

    /**
     * 设备在线监测
     */
    DEVICE_ONLINE_CHECK("deviceOnlineCheck", "设备在线监测"),

    /**
     * 第三方定时任务
     */
    THIRD_TIMER("thirdTimer", "第三方定时任务"),

    ;

    private final String value;
    private final String label;

    /**
     * 翻译编码
     */
    public static String translationValue(String value) {
        for (ScheduledCronEnum e : ScheduledCronEnum.values()) {
            if (e.value.equals(value)) {
                return e.label;
            }
        }
        return null;
    }
}

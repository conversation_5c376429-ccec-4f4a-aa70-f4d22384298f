package org.springblade.thingcom.core.server.utils;

import com.jcraft.jsch.ChannelExec;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import org.springblade.common.utils.CommonUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.thingcom.operationMaintenance.entity.ServerRunStatis;
import oshi.SystemInfo;
import oshi.hardware.HardwareAbstractionLayer;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @since 2024-04-24 14:03
 */
public class ServerUtil {

    public static void main(String[] args) {
        //try {
        ServerRunStatis en = remoteCpuUsage();
        System.out.println("通过ip获取：" + en);

        //System.out.println("------------------------------------");
        //
        //System.out.println("本地服务器：");
        //
        //// 本地服务器
        //BigDecimal cpuUsage = ServerUtil.cpuUsage();
        //System.out.println("CPU使用率: " + cpuUsage);
        //BigDecimal memoryUsage = ServerUtil.memoryUsage();
        //System.out.println("内存使用率: " + memoryUsage);
        //List<String> diskUsage = ServerUtil.diskUsage();
        //System.out.println("硬盘使用率: " + diskUsage);
        //List<BigDecimal> networkUsage = ServerUtil.networkUsage();
        //System.out.println("网络使用率: " + networkUsage);
        //} catch (IOException | InterruptedException e) {
        //    e.printStackTrace();
        //}
    }

    /**
     * 内存使用率
     *
     * @return
     */
    public static BigDecimal memoryUsage() {
        // 创建系统信息实例
        SystemInfo systemInfo = new SystemInfo();
        // 获取硬件抽象层，可以访问CPU、内存等信息
        HardwareAbstractionLayer hal = systemInfo.getHardware();
        // 获取内存使用率
        long availableMemory = hal.getMemory().getAvailable();
        long totalMemory = hal.getMemory().getTotal();
        double memoryUsage = ((totalMemory - availableMemory) / (double) totalMemory) * 100;
        return new BigDecimal(memoryUsage).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * CPU使用率
     *
     * @return
     */
    public static BigDecimal cpuUsage() {
        try {
            String osName = getOsName();
            String command;
            if (osName.contains("windows")) {
                // Windows命令，获取CPU负载百分比
                command = "wmic cpu get loadpercentage";
            } else if (osName.contains("linux") || osName.contains("mac")) {
                // Linux和MacOS命令，获取非空闲CPU使用率
                command = "top -bn1 | sed -n 's/.*Cpu\\(s\\).*, *\\([0-9.]*\\)%* id,.*/\\1/p'";
            } else {
                throw new UnsupportedOperationException("Unsupported operating system: " + osName);
            }
            ProcessBuilder processBuilder = new ProcessBuilder(command.split(" "));
            processBuilder.redirectErrorStream(true);
            Process process = processBuilder.start();

            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            double cpuUsage = -1.0; // 默认值，表示无法获取
            while ((line = reader.readLine()) != null) {
                // 使用正则表达式匹配CPU使用率的数字
                Pattern pattern = Pattern.compile("\\d+\\.\\d+|\\d+");
                Matcher matcher = pattern.matcher(line);
                if (matcher.find()) {
                    cpuUsage = Double.parseDouble(matcher.group());
                    break;
                }
            }
            reader.close();
            process.waitFor();

            if (cpuUsage != -1.0) {
                System.out.println("CPU使用率: " + cpuUsage + "%");
                return new BigDecimal(cpuUsage).setScale(2, RoundingMode.HALF_UP);
            } else {
                System.out.println("Could not determine CPU usage.");
                return new BigDecimal(0);
            }

        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }
        return new BigDecimal(0);
    }

    /**
     * 硬盘使用率
     *
     * @return
     */
    public static List<String> diskUsage() {
        try {
            String osName = getOsName();
            double diskUsage = 0.0;
            List<String> list = new ArrayList<>();
            Process process;
            String command;
            if (osName.contains("win")) {
                // Windows系统
                command = "wmic logicaldisk get size,freespace,caption";
            } else {
                // Linux或类Unix系统
                command = "df -h / | tail -n 1 | awk '{print $5}'";
            }

            process = Runtime.getRuntime().exec(command);
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            StringBuilder output = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line);
            }
            if (osName.contains("win")) {
                // Windows系统输出解析
                //Pattern pattern = Pattern.compile("([0-9\\.]+)%");
                //Matcher matcher = pattern.matcher(line);
                //if (matcher.find()) {
                //    diskUsage = Double.parseDouble(matcher.group(1));
                //}
                String s = output.toString();
                String[] token = s.split("\\s+");
                BigDecimal diskTotalBg = new BigDecimal(token[5]);
                BigDecimal diskAvailBg = new BigDecimal(token[4]);
                BigDecimal diskUse = diskTotalBg.subtract(diskAvailBg);
                BigDecimal disUseBigDecimal = diskUse.divide(diskTotalBg, 4, BigDecimal.ROUND_HALF_UP);
                list.add(diskTotalBg.divide(new BigDecimal(1000000000)).setScale(2, RoundingMode.HALF_UP).toString());
                list.add(diskUse.divide(new BigDecimal(1000000000)).setScale(2, RoundingMode.HALF_UP).toString());
                list.add(diskAvailBg.divide(new BigDecimal(1000000000)).setScale(2, RoundingMode.HALF_UP).toString());
                list.add(disUseBigDecimal.multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP).toString());
            } else {
                // Linux系统输出解析
                // 使用正则表达式去除%符号
                //diskUsage = Double.parseDouble(line.replaceAll("%", ""));
                String[] token = output.toString().split("\\s+");
                String diskTotal = token[7];
                String diskUsed = token[8];
                String diskAvail = token[9];
                // 使用率
                String diskUse = token[10];
                // 使用率转化为BigDecimal
                //BigDecimal disUseBigDecimal = new BigDecimal(diskUse.split("%")[0]).setScale(2, RoundingMode.HALF_UP);
                list.add(diskTotal);
                list.add(diskUsed);
                list.add(diskAvail);
                list.add(diskUse);
            }
            System.out.println("硬盘使用率: " + diskUsage + "%");
            return list;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new ArrayList<>();
    }

    /**
     * 网络使用率
     *
     * @return
     */
    public static List<BigDecimal> networkUsage() {
        String osName = getOsName();
        String interfaceName = "eth0"; // Linux系统中的常见网络接口名称，Windows系统中可能是"Local Area Connection"
        String command = "";
        List<BigDecimal> list = new ArrayList<>();
        // 根据操作系统选择命令
        if (osName.contains("win")) {
            // Windows系统
            command = "netstat -i \"" + interfaceName + "\"";
        } else {
            // Linux系统
            command = "ip -s link show " + interfaceName;
        }

        try {
            Process process = Runtime.getRuntime().exec(command);
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));

            String line;
            Pattern pattern = Pattern.compile("\\b" + interfaceName + "\\b");
            long bytesReceived = 0;
            long bytesSent = 0;

            while ((line = reader.readLine()) != null) {
                Matcher matcher = pattern.matcher(line);
                if (matcher.find()) {
                    if (osName.contains("win")) {
                        // Windows系统
                        String[] tokens = line.split("\\s+");
                        bytesReceived = Long.parseLong(tokens[3]);
                        bytesSent = Long.parseLong(tokens[9]);
                    } else {
                        // Linux系统
                        String[] tokens = line.split("\\s+");
                        bytesReceived = Long.parseLong(tokens[8]);
                        bytesSent = Long.parseLong(tokens[10]);
                    }
                    break;
                }
            }
            reader.close();
            System.out.println("上行流量 (接收到的字节数): " + bytesReceived);
            System.out.println("下行流量 (发送的字节数): " + bytesSent);
            list.add(new BigDecimal(bytesReceived).setScale(2, RoundingMode.HALF_UP));
            list.add(new BigDecimal(bytesSent).setScale(2, RoundingMode.HALF_UP));
            return list;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return list;
    }

    /**
     * 获取操作系统名称
     */
    public static String getOsName() {
        return System.getProperty("os.name").toLowerCase();
    }

    /**
     * 远程服务器CPU、内存、硬盘、网络使用率
     *
     * @return
     */
    public static ServerRunStatis remoteCpuUsage() {
        String user = "root";
        String host = "**************";
        String password = "Arc-Tech@2023!";
        int port = 22;

        //String user = "administrator";
        //String host = "**************";
        //String password = "Xxb_3168683";
        //int port = 2022;

        ServerRunStatis entity = new ServerRunStatis();
        JSch jsch = new JSch();
        Session session = null;
        try {
            // 创建会话
            session = jsch.getSession(user, host, port);
            session.setPassword(password);
            java.util.Properties config = new java.util.Properties();
            config.put("StrictHostKeyChecking", "no");
            session.setConfig(config);
            session.connect();

            // 获取CPU使用率
            String cpuUsageCommand = getCpuUsageCommand(session);
            String cpuUsage = command(session, cpuUsageCommand);
            System.out.println("CPU Usage: " + cpuUsage + "%");

            // 获取内存使用率
            String memoryUsageCommand = getMemoryUsageCommand(session);
            String memoryUsage = command(session, memoryUsageCommand);
            System.out.println("Memory Usage: " + memoryUsage + "%");

            // 获取硬盘使用率
            String diskUsageCommand = getDiskUsageCommand(session);
            String diskUsage = command(session, diskUsageCommand);
            String[] token = diskUsage.split("\\s+");
            String diskTotal = token[7];
            String diskUsed = token[8];
            String diskAvail = token[9];
            String diskUse = token[10];
            String hardDiskStore = diskUse.split("%")[0];
            System.out.println("Disk Usage: " + diskUse);

            // 获取网络使用率
            String networkUsageCommand = getNetworkUsageCommand(session, "eth0");
            String output4 = command(session, networkUsageCommand);
            String[] tokens = output4.split("\\s+");
            long bytesReceived = Long.parseLong(tokens[26]);
            long bytesSent = Long.parseLong(tokens[39]);
            BigDecimal receive = new BigDecimal(bytesReceived).divide(new BigDecimal(1000), 1, RoundingMode.HALF_UP);
            BigDecimal sent = new BigDecimal(bytesSent).divide(new BigDecimal(1000), 1, RoundingMode.HALF_UP);
            System.out.println("Network Usage: " + receive + " kb received, " + sent + " kb sent");

            if (Func.toInt(hardDiskStore) < 100) {
                entity.setHardDiskStatus(1);
            } else {
                entity.setHardDiskStatus(2);
            }
            entity.setCpuUseRate(cpuUsage);
            entity.setMemoryUseRate(memoryUsage);
            entity.setUplinkFlow(Func.toStr(receive));
            entity.setDownlinkFlow(Func.toStr(sent));
            entity.setTime(CommonUtil.getZoneTime());
            entity.setHardDiskTotal(diskTotal);
            entity.setHardDiskUsed(diskUsed);
            entity.setHardDiskFree(diskAvail);
            return entity;
        } catch (JSchException e) {
            e.printStackTrace();
            System.out.println(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            if (session != null && session.isConnected()) {
                session.disconnect();
            }
        }
        return null;
    }

    /**
     * 执行远程命令
     *
     * @param session
     * @param command
     * @return
     * @throws IOException
     * @throws JSchException
     */
    public static String command(Session session, String command) throws IOException, JSchException {
        // 执行命令
        ChannelExec channelExec = (ChannelExec) session.openChannel("exec");
        channelExec.setCommand(command);
        channelExec.setInputStream(null);
        InputStream in = channelExec.getInputStream();
        channelExec.setErrStream(System.err);

        channelExec.connect();

        // 读取命令输出
        BufferedReader reader = new BufferedReader(new InputStreamReader(in));
        StringBuilder output = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null) {
            output.append(line);
        }
        return output.toString();
    }

    /**
     * 获取远程服务器CPU使用率命令
     *
     * @param session
     * @return
     * @throws JSchException
     * @throws IOException
     */
    public static String getCpuUsageCommand(Session session) throws JSchException, IOException {
        String osName = getRemoteOsName(session);
        if (osName != null) {
            if (osName.contains("Windows")) {
                // Windows系统
                return "wmic cpu get loadpercentage";
            } else if (osName.contains("Linux")) {
                // Linux系统
                return "top -bn1 | grep 'Cpu(s)' | sed 's/.*, *\\([0-9.]*\\)%* id,.*/\\1/' | awk '{print 100 - $1}'";
            }
        }
        // 如果无法确定操作系统类型，则返回null
        return null;
    }

    /**
     * 获取远程服务器内存使用率命令
     *
     * @param session
     * @return
     * @throws JSchException
     * @throws IOException
     */
    public static String getMemoryUsageCommand(Session session) throws JSchException, IOException {
        String osName = getRemoteOsName(session);
        if (osName != null) {
            if (osName.contains("Windows")) {
                // Windows系统命令，获取内存使用率
                return "wmic OS get FreePhysicalMemory /Value";
            } else if (osName.contains("Linux")) {
                // Linux系统命令，获取内存使用率
                return "free -m | awk 'NR==2{printf \"\"$3/$2*100, \"%\" }'";
            }
        }
        // 如果无法识别操作系统，则返回null
        return null;
    }

    /**
     * 获取远程服务器硬盘使用率命令
     *
     * @param session
     * @return
     * @throws JSchException
     * @throws IOException
     */
    public static String getDiskUsageCommand(Session session) throws JSchException, IOException {
        String osName = getRemoteOsName(session);
        if (osName != null) {
            if (osName.contains("Windows")) {
                // Windows系统命令，获取内存使用率
                return "wmic logicaldisk where deviceid='C:' get size, freespace";
            } else if (osName.contains("Linux")) {
                // Linux系统命令，获取内存使用率
                return "df -h /";
            }
        }
        // 如果无法识别操作系统，则返回null
        return null;
    }

    /**
     * 获取远程服务器网络使用率命令
     *
     * @param session
     * @param interfaceName
     * @return
     * @throws JSchException
     * @throws IOException
     */
    public static String getNetworkUsageCommand(Session session, String interfaceName) throws JSchException, IOException {
        String osName = getRemoteOsName(session);
        if (osName != null) {
            if (osName.contains("Windows")) {
                // Windows系统命令，获取内存使用率
                return "netstat -i \"" + interfaceName + "\"";
            } else if (osName.contains("Linux")) {
                // Linux系统命令，获取内存使用率
                return "ip -s link show " + interfaceName;
            }
        }
        // 如果无法识别操作系统，则返回null
        return null;
    }

    /**
     * 获取远程服务器操作系统名称
     *
     * @param session
     * @return
     * @throws JSchException
     * @throws IOException
     */
    public static String getRemoteOsName(Session session) throws JSchException, IOException {
        String osNameCommand = "uname -s";
        ChannelExec channelExec = (ChannelExec) session.openChannel("exec");
        channelExec.setCommand(osNameCommand);
        channelExec.setInputStream(null);
        channelExec.connect();
        InputStream in = channelExec.getInputStream();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(in))) {
            String line;
            while ((line = reader.readLine()) != null) {
                return line;
            }
        } catch (IOException e) {
            throw new JSchException("Error reading remote OS name", e);
        } finally {
            channelExec.disconnect();
        }
        return null;
    }
}

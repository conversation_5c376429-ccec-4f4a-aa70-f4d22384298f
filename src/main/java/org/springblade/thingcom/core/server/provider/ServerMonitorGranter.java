package org.springblade.thingcom.core.server.provider;

import org.springblade.thingcom.core.server.dto.ServerDTO;
import org.springblade.thingcom.core.server.vo.ServerRunDataVO;

public interface ServerMonitorGranter {

    /**
     * 获取服务器运行数据
     *
     * @param dto
     * @return ServerRunDataVO
     */
    ServerRunDataVO getServerRunData(ServerDTO dto);

    /**
     * 获取本机服务器运行数据
     *
     * @return ServerRunDataVO
     */
    ServerRunDataVO getLocalhostRunData();

    /**
     * 获取远程服务器运行数据
     *
     * @param dto
     * @return ServerRunDataVO
     */
    ServerRunDataVO getRemoteServerRunData(ServerDTO dto);
}

package org.springblade.thingcom.core.server.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.jcraft.jsch.ChannelExec;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.utils.Func;
import org.springblade.thingcom.core.server.constant.ParamConstant;
import org.springblade.thingcom.core.server.dto.ServerDTO;
import org.springblade.thingcom.core.server.enums.MonitorTypeEnum;
import org.springblade.thingcom.core.server.enums.OperationSystemEnum;
import org.springblade.thingcom.core.server.vo.ServerRunDataVO;
import oshi.SystemInfo;
import oshi.hardware.HardwareAbstractionLayer;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Properties;

/**
 * @ClassName ServerMonitorUtil
 * @Description: 服务器监控器工具类
 * @Author: zgq
 * @Date: 2024/5/21 11:22
 * @Version: 1.0
 **/
@Slf4j
public class ServerMonitorUtil {

    public static void main(String[] args) {
        // 远程服务器信息
        //String host = "ip";
        //String user = "user";
        //String password = "password";
        //int port = 80;
        //
        //ServerDTO dto = new ServerDTO();
        //dto.setHost(host);
        //dto.setUserName(user);
        //dto.setPassword(password);
        //dto.setPort(port);
        //dto.setOsName(OperationSystemEnum.LINUX.getOsName());

        String osName = OperationSystemEnum.WINDOWS.getOsName();
        ServerRunDataVO localhostServerRunData = getLocalhostServerRunData(osName);
        System.out.println("本地服务器运行信息：" + JSONUtil.toJsonStr(localhostServerRunData));

        //ServerRunDataVO remoteServerRunData = getRemoteServerRunData(dto);
        //System.out.println("远程服务器运行信息：" + JSONUtil.toJsonStr(remoteServerRunData));
    }

    /**
     * 获取本机服务器运行数据
     *
     * @return ServerRunDataVO
     */
    public static ServerRunDataVO getLocalhostServerRunData(String osName) {
        // 返回 vo
        ServerRunDataVO vo = new ServerRunDataVO();
        // 获取操作系统名称
        //String osName = System.getProperty(ParamConstant.OS_NAME).toLowerCase();
        // 提取 osName 字母部分（比如：windows 11 -> windows）
        //osName = osName.replaceAll(ParamConstant.LETTER_REGEX, "");
        OperationSystemEnum os = OperationSystemEnum.getByOsName(osName);
        if (Objects.isNull(os)) {
            return vo;
        }
        // 获取cpu使用率命令
        String cpuUseRateCommand = os.getCpuUse();
        if (StrUtil.isNotBlank(cpuUseRateCommand)) {
            // 获取cpu数据
            List<ServerRunDataVO.Cpu> cpuList = getLocalhostCpuData(osName, cpuUseRateCommand);
            vo.setCpuList(cpuList);
        }
        // 获取内存使用率
        BigDecimal memoryUseRate = getLocalhostMemoryUseRate();
        vo.setMemoryUseRate(memoryUseRate);
        // 获取硬盘使用率命令
        String hardDiskUseRateCommand = os.getHarddiskUse();
        if (StrUtil.isNotBlank(hardDiskUseRateCommand)) {
            // 获取硬盘数据
            List<ServerRunDataVO.HardDiskPartition> hardDiskPartitionList = getLocalhostHardDiskData(osName, hardDiskUseRateCommand);
            vo.setHardDiskPartitionList(hardDiskPartitionList);
        }
        return vo;
    }

    /**
     * 获取本机 cpu 数据
     *
     * @param command
     * @return BigDecimal
     */
    public static List<ServerRunDataVO.Cpu> getLocalhostCpuData(String osName, String command) {
        Process process = null;
        BufferedReader reader = null;
        try {
            ProcessBuilder processBuilder = new ProcessBuilder(command.split(ParamConstant.SPACE_SPLIT));
            processBuilder.redirectErrorStream(true);
            process = processBuilder.start();
            reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            StringBuilder output = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line);
            }
            process.waitFor();
            // 执行命令后，获取到的数据
            String data = output.toString();
            return cpuDataProcess(osName, data);
        } catch (IOException | InterruptedException e) {
            log.error("获取 cpu 使用率失败", e);
        } finally {
            try {
                if (reader != null) {
                    reader.close();
                }
                if (process != null && !process.isAlive()) {
                    process.destroy();
                }
            } catch (Exception ignored) {
            }
        }
        return null;
    }

    /**
     * 获取本机内存使用率
     *
     * @return BigDecimal
     */
    public static BigDecimal getLocalhostMemoryUseRate() {
        // 创建系统信息实例
        SystemInfo systemInfo = new SystemInfo();
        // 获取硬件抽象层，可以访问CPU、内存等信息
        HardwareAbstractionLayer hal = systemInfo.getHardware();
        // 获取内存使用率
        long availableMemory = hal.getMemory().getAvailable();
        long totalMemory = hal.getMemory().getTotal();
        double memoryUseRate = ((totalMemory - availableMemory) / (double) totalMemory) * 100;
        return new BigDecimal(memoryUseRate).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 获取本机硬盘数据
     *
     * @param osName
     * @param command
     * @return List<ServerRunDataVO.HardDiskPartition>
     */
    public static List<ServerRunDataVO.HardDiskPartition> getLocalhostHardDiskData(String osName, String command) {
        Process process = null;
        BufferedReader reader = null;
        try {
            process = Runtime.getRuntime().exec(command);
            reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            StringBuilder output = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line);
            }
            process.waitFor();
            // 执行命令后，获取到的数据
            String data = output.toString();
            return hardDiskDataProcess(osName, data);
        } catch (IOException | InterruptedException e) {
            log.error("获取硬盘使用率失败", e);
        } finally {
            try {
                if (reader != null) {
                    reader.close();
                }
                if (process != null && !process.isAlive()) {
                    process.destroy();
                }
            } catch (Exception ignored) {
            }
        }
        return null;
    }

    /**
     * 获取远程服务器运行数据
     *
     * @param dto
     * @return ServerRunDataVO
     */
    public static ServerRunDataVO getRemoteServerRunData(ServerDTO dto) {
        // 返回 vo
        ServerRunDataVO vo = new ServerRunDataVO();
        // 操作系统
        String osName = dto.getOsName();
        OperationSystemEnum os = OperationSystemEnum.getByOsName(osName);
        if (Objects.isNull(os)) {
            return vo;
        }
        JSch jsch = new JSch();
        Session session = null;
        try {
            // 创建会话
            session = jsch.getSession(dto.getUserName(), dto.getHost(), dto.getPort());
            session.setPassword(dto.getPassword());
            Properties config = new Properties();
            config.put("StrictHostKeyChecking", "no");
            session.setConfig(config);
            session.connect();
            // 获取cpu使用率命令
            String cpuUseRateCommand = os.getCpuUse();
            if (StrUtil.isNotBlank(cpuUseRateCommand)) {
                // 执行命令，获取CPU数据
                String data = commandExec(session, cpuUseRateCommand);
                List<ServerRunDataVO.Cpu> cpuList = cpuDataProcess(osName, data);
                vo.setCpuList(cpuList);
            }
            // 获取内存使用率命令
            String memoryUseRateCommand = os.getMemoryUse();
            if (StrUtil.isNotBlank(memoryUseRateCommand)) {
                // 执行命令，获取内存使用率
                String data = commandExec(session, memoryUseRateCommand);
                BigDecimal memoryUseRate = memoryDataProcess(osName, data);
                vo.setMemoryUseRate(memoryUseRate);
            }
            // 获取硬盘使用率命令
            String hardDiskUseRateCommand = os.getHarddiskUse();
            if (StrUtil.isNotBlank(hardDiskUseRateCommand)) {
                // 执行命令，获取硬盘数据
                String data = commandExec(session, hardDiskUseRateCommand);
                List<ServerRunDataVO.HardDiskPartition> hardDiskPartitionList = hardDiskDataProcess(osName, data);
                vo.setHardDiskPartitionList(hardDiskPartitionList);
            }
            return vo;
        } catch (Exception e) {
            log.error("远程服务器信息获取失败,{}", e.getMessage());
        } finally {
            try {
                if (session != null && session.isConnected()) {
                    session.disconnect();
                }
            } catch (Exception ignored) {
            }
        }
        return null;
    }

    /**
     * 执行远程命令
     *
     * @param session
     * @param command
     * @return
     */
    public static String commandExec(Session session, String command) {
        ChannelExec channelExec = null;
        BufferedReader reader = null;
        try {
            channelExec = (ChannelExec) session.openChannel(ParamConstant.OPEN_CHANNEL_TYPE);
            channelExec.setCommand(command);
            channelExec.setInputStream(null);
            InputStream in = channelExec.getInputStream();
            channelExec.setErrStream(System.err);
            channelExec.connect();
            reader = new BufferedReader(new InputStreamReader(in));
            StringBuilder output = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line);
            }
            return output.toString();
        } catch (JSchException | IOException e) {
            log.error("远程命令执行失败,{}", e.getMessage());
        } finally {
            try {
                if (channelExec != null && channelExec.isConnected()) {
                    channelExec.disconnect();
                }
                if (reader != null) {
                    reader.close();
                }
            } catch (Exception ignored) {
            }
        }
        return "0";
    }

    /**
     * cpu数据处理（支持多个 cpu）
     *
     * @param osName
     * @param data
     * @return List<ServerRunDataVO.Cpu>
     */
    public static List<ServerRunDataVO.Cpu> cpuDataProcess(String osName, String data) {
        List<ServerRunDataVO.Cpu> cpuList = new ArrayList<>(0);
        if (StrUtil.isBlank(data)) {
            return cpuList;
        }
        String cpuLabel = MonitorTypeEnum.CPU.getLabel();
        if (OperationSystemEnum.WINDOWS.getOsName().equals(osName)) {
            /*
            数据格式：
                     LoadPercentage
                     10（cpu-1）
                     12（cpu-2）
            */
            String[] cpuDataArray = data.split(ParamConstant.SPACE_SPLIT);
            int cpuCount = cpuDataArray.length - 1;
            int i = 1;
            for (String str : cpuDataArray) {
                // 排除标题：LoadPercentage
                if (!ParamConstant.LOADPERCENTAGE.equalsIgnoreCase(str) && str.matches(ParamConstant.NUMBER_REGEX)) {
                    ServerRunDataVO.Cpu cpu = new ServerRunDataVO.Cpu();
                    String cpuName = cpuCount == 1 ? cpuLabel : cpuLabel + "-" + i;
                    cpu.setName(cpuName);
                    // 避免异常
                    double cpuUseRateDouble = Func.toDouble(str, 0D);
                    cpu.setUseRate(new BigDecimal(cpuUseRateDouble).setScale(2, RoundingMode.HALF_UP));
                    cpuList.add(cpu);
                    i++;
                }
            }
        }
        return cpuList;
    }

    /**
     * 内存使用率处理
     *
     * @param osName
     * @param data
     * @return BigDecimal
     */
    private static BigDecimal memoryDataProcess(String osName, String data) {
        if (OperationSystemEnum.WINDOWS.getOsName().equals(osName)) {
            /*
            数据格式：
                     FreePhysicalMemory  TotalVisibleMemorySize
                     7704092             24842996
            */
            String[] memoryDataArray = data.split(ParamConstant.SPACE_SPLIT);
            // 可用内存
            double availableMemory = Func.toDouble(memoryDataArray[2], 0D);
            // 总内存
            double totalMemory = Func.toDouble(memoryDataArray[3], 0D);
            if (totalMemory == 0 || totalMemory - availableMemory < 0) {
                return BigDecimal.ZERO;
            }
            double memoryUseRate = ((totalMemory - availableMemory) / totalMemory) * 100;
            return new BigDecimal(memoryUseRate).setScale(2, RoundingMode.HALF_UP);
        }
        return BigDecimal.ZERO;
    }

    /**
     * 硬盘数据处理
     *
     * @param osName
     * @param data
     * @return List<ServerRunDataVO.HardDiskPartition>
     */
    public static List<ServerRunDataVO.HardDiskPartition> hardDiskDataProcess(String osName, String data) {
        // 硬盘分区列表
        List<ServerRunDataVO.HardDiskPartition> hardDiskPartitionList = new ArrayList<>(3);
        // 按照空格符分割，获取硬盘分区信息
        String[] partitionArray = data.split(ParamConstant.SPACE_SPLIT);
        int partitionArrayLength = partitionArray.length;
        // Windows系统输出解析
        if (OperationSystemEnum.WINDOWS.getOsName().equals(osName)) {
            /*
            数据格式：
                     Caption    FreeSpace       Size(Byte)
                     C:         109412429824    213845536768
                     D:         303014830080    404802760704
                     E:         344460906496    404246302720
            */
            // BYTE转换为GB
            BigDecimal byteToGb = new BigDecimal(ParamConstant.BYTE_TO_GB);
            // 分区分组长度（一组为 3 项数据）
            int partitionGroupLength = 3;
            // 计算分区数量（排除标题项，所以减 1）
            int partitionNum = (partitionArrayLength / partitionGroupLength) - 1;
            for (int i = 1; i <= partitionNum; i++) {
                ServerRunDataVO.HardDiskPartition partition = new ServerRunDataVO.HardDiskPartition();
                // 分组索引
                int groupIndex = i * partitionGroupLength;
                // 分区名称
                if (groupIndex < partitionArrayLength) {
                    String name = partitionArray[i * partitionGroupLength];
                    partition.setName(name.replace(":", "") + "盘");
                }
                // 剩余容量
                long free = 0;
                int freeIndex = groupIndex + 1;
                if (freeIndex < partitionArrayLength) {
                    free = Func.toLong(partitionArray[freeIndex]);
                }
                partition.setFree(new BigDecimal(free).divide(byteToGb, 2, RoundingMode.HALF_UP));
                // 总容量
                long totalSpace = 0;
                int totalSpaceIndex = groupIndex + 2;
                if (totalSpaceIndex < partitionArrayLength) {
                    totalSpace = Func.toLong(partitionArray[totalSpaceIndex]);
                }
                partition.setTotal(new BigDecimal(totalSpace).divide(byteToGb, 2, RoundingMode.HALF_UP));
                // 使用容量
                partition.setUse(partition.getTotal().subtract(partition.getFree()));
                if (partition.getTotal().compareTo(BigDecimal.ZERO) > 0) {
                    // 使用率
                    partition.setUseRate(partition.getUse().divide(partition.getTotal(), 2, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal(100)));
                }
                hardDiskPartitionList.add(partition);
            }
        }
        return hardDiskPartitionList;
    }

}

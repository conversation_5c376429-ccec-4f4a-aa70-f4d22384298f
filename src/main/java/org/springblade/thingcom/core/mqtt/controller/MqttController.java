package org.springblade.thingcom.core.mqtt.controller;

import cn.hutool.json.JSONObject;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.tool.api.R;
import org.springblade.thingcom.core.chirpStack.service.DeviceQueueService;
import org.springblade.thingcom.core.mqtt.MqttClient;
import org.springblade.thingcom.data.async.AsyncMonitorDataHandler;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @description Mqtt测试
 * @since 2024/1/17 11:46
 */
@RestController
@AllArgsConstructor
@RequestMapping("/mqtt")
public class MqttController {

    private final Logger log = LoggerFactory.getLogger(MqttController.class);

    private final MqttClient mqttClient;
    private final AsyncMonitorDataHandler asyncMonitorDataHandler;
    private final DeviceQueueService deviceQueueService;

    /**
     * 接收消息（上行、数据上报），模拟机器人数据上报
     *
     * @param json
     */
    @PostMapping("/robot/arrived")
    public R robotMessageArrived(@RequestBody JSONObject json) {
        try {
            asyncMonitorDataHandler.saveRobotData(json);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
        return R.status(true);
    }

    /**
     * 接收消息（上行、数据上报），模拟网关数据上报
     *
     * @param json
     */
    @PostMapping("/gps-gateway/arrived")
    public R gatewayMessageArrived(@RequestBody JSONObject json) {
        try {
            asyncMonitorDataHandler.saveGpsGatewayData(json);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
        return R.status(true);
    }

    /**
     * 推送消息（下行、发送指令）
     *
     * @param topic   主题
     * @param payload 消息
     */
    @GetMapping("/publish")
    public R publish(String topic, String payload) {
        log.info("推送主题：{}，推送内容：{}", topic, payload);
        try {
            mqttClient.publish(topic, payload, 2, false);
            // 删除设备队列数据，避免持久化导致设备恢复后的重发
            //deviceQueueService.deleteData("3635373469477C82");
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
        return R.success("操作成功");
    }

    /**
     * 订阅主题
     *
     * @param topic 主题
     * @param qos   Qos等级
     */
    @GetMapping("/subscribe")
    public R subscribe(String topic, int qos) {
        log.info("订阅主题：{}", topic);
        mqttClient.subscribe(topic, qos);
        return R.success("操作成功");
    }

    /**
     * 取消订阅
     *
     * @param topic 主题
     */
    @GetMapping("/unsubscribe")
    public R unsubscribe(String topic) {
        log.info("取消订阅：{}", topic);
        mqttClient.unsubscribe(topic);
        return R.success("操作成功");
    }
}

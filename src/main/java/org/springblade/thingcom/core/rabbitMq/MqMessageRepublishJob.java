package org.springblade.thingcom.core.rabbitMq;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.SystemUtil;
import org.springblade.core.tool.utils.SpringUtil;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.Map;

/**
 * @Description: 重新发送消息定时任务
 * @Author: xulj
 * @Date: 2022-02-14 17:03
 */
@Slf4j
public class MqMessageRepublishJob implements Runnable {

    private static final RedisTemplate redisTemplate;
    private static final RabbitMqService rabbitMqService;
    private static final SystemUtil systemUtil;

    static {
        redisTemplate = SpringUtil.getBean("redisTemplate", RedisTemplate.class);
        rabbitMqService = SpringUtil.getBean(RabbitMqService.class);
        systemUtil = SpringUtil.getBean(SystemUtil.class);
    }

    @Override
    public void run() {
        boolean scheduledEnabled = systemUtil.getTaskEnabled();
        if (!scheduledEnabled) {
            return;
        }
        // 把returnMap移到confirmMap
        Map<Object, Object> returnMap = redisTemplate.opsForHash().entries(RabbitMqConfig.CACHE_MESSAGE_RETURN);
        if (CollUtil.isNotEmpty(returnMap)) {
            redisTemplate.opsForHash().putAll(RabbitMqConfig.CACHE_MESSAGE_CONFIRM, returnMap);
            returnMap.forEach((key, value) -> {
                redisTemplate.opsForHash().delete(RabbitMqConfig.CACHE_MESSAGE_RETURN, key);
            });
        }
        // 发送confirmMap里的消息
        redisTemplate.opsForHash().entries(RabbitMqConfig.CACHE_MESSAGE_CONFIRM).forEach((key, value) -> {
            JSONObject json = JSONUtil.parseObj(value);
            String routingKey = json.getStr(RabbitMqConfig.ROUTING_KEY);
            rabbitMqService.resendMessage(routingKey, (String) key, json);
        });
    }
}
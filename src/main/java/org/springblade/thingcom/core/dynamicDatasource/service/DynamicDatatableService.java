package org.springblade.thingcom.core.dynamicDatasource.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.thingcom.core.base.dto.BaseDTO;
import org.springblade.thingcom.core.dynamicDatasource.entity.DynamicDatatable;
import org.springblade.thingcom.core.mybatisPlus.IBaseService;
import org.springblade.thingcom.operationMaintenance.entity.RobotClearingLog;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 数据表管理服务接口
 *
 * <AUTHOR>
 * @since 2024-05-06 19:32:05
 */
public interface DynamicDatatableService extends IBaseService<DynamicDatatable> {

    /**
     * 保存数据
     *
     * @param tableNamePrefix 表名前缀
     * @param time            数据采集时间
     * @param dataMap         数据 map
     * @return void
     */
    void saveData(String tableNamePrefix, LocalDateTime time, Map<String, Object> dataMap);

    /**
     * 更新告警日志
     *
     * @param dbNameAndTableName 表名（包括数据库，database.table）
     * @param handleStatus       处理状态
     * @param duration           持续时间
     * @param wrapper            条件
     * @return void
     */
    void updateAlarmLog(String dbNameAndTableName, Integer handleStatus, Long duration, String wrapper);

    /**
     * 更新机器人广播操作日志状态
     *
     * @param dbNameAndTableName 表名（包括数据库，database.table）
     * @param status             状态
     * @param wrapper            条件
     * @return void
     */
    void updateRobotBroadcastOperationLog(String dbNameAndTableName, Integer status, String wrapper);

    /**
     * 获取表名（直接查询对应数据库）
     *
     * @param dbName    数据库名称
     * @param tableName 表名
     * @return 存在返回表名，不存在返回null
     */
    String getTable(String dbName, String tableName);

    /**
     * 分页查询数据
     *
     * @param page            分页参数
     * @param tableNamePrefix 表名前缀
     * @param dto             查询参数
     * @return IPage
     */
    IPage<Map<String, Object>> page(IPage<Object> page, String tableNamePrefix, BaseDTO dto);

    /**
     * 列表查询数据
     *
     * @param tableNamePrefix 表名前缀
     * @param dto             查询参数
     * @return List
     */
    List<Map<String, Object>> list(String tableNamePrefix, BaseDTO dto);

    /**
     * 查询单条数据
     *
     * @param dbNameAndTableName 表名（包括数据库，database.table）
     * @param queryWrapper       条件
     * @return void
     */
    Map<String, Object> getOne(String dbNameAndTableName, String queryWrapper);

    /**
     * 更新机器人清扫日志
     *
     * @param dbNameAndTableName 表名（包括数据库，database.table）
     * @param robotClearingLog   机器人清扫日志
     * @return void
     */
    void updateClearingLog(String dbNameAndTableName, RobotClearingLog robotClearingLog);
}

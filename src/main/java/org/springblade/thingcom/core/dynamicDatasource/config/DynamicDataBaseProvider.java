package org.springblade.thingcom.core.dynamicDatasource.config;

import com.baomidou.dynamic.datasource.provider.AbstractJdbcDataSourceProvider;
import com.baomidou.dynamic.datasource.provider.DynamicDataSourceProvider;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DataSourceProperty;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.thingcom.core.dynamicDatasource.constant.ParamConstant;
import org.springblade.thingcom.core.dynamicDatasource.enums.DataSourceConfigEnum;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;

import javax.annotation.Resource;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.HashMap;
import java.util.Map;

/**
 * ds提供者
 *
 * <AUTHOR>
 * @date 2021/11/16
 */
@Primary
@Slf4j
@Configuration
public class DynamicDataBaseProvider {
    @Resource
    private DataBaseConfig dataBaseConfig;
    @Resource
    private Environment environment;

    @Bean
    public DynamicDataSourceProvider jdbcDynamicDataSourceProvider() {
        return new AbstractJdbcDataSourceProvider(dataBaseConfig.getDriverClassName(), dataBaseConfig.getUrl()
                , dataBaseConfig.getUsername(), dataBaseConfig.getPassword()) {
            @Override
            protected Map<String, DataSourceProperty> executeStmt(Statement statement) {
                Map<String, DataSourceProperty> dataSourcePropertiesMap = new HashMap<>();
                ResultSet rs = null;
                Boolean urlReplace = environment.getProperty("dynamic-dataSource.url-provider", Boolean.class);
                if (urlReplace == null) {
                    urlReplace = false;
                }
                try {
                    rs = statement.executeQuery(ParamConstant.DYNAMIC_DATASOURCE_SQL);
                    while (rs.next()) {
                        String name = rs.getString(ParamConstant.DATASOURCE_STATEMENT_NAME);
                        DataSourceProperty property = new DataSourceProperty();
                        property.setDriverClassName(rs.getString(DataSourceConfigEnum.DS_DRIVER.getName()));
                        String url = rs.getString(DataSourceConfigEnum.DS_URL.getName());
                        // windows 环境下，默认为：dev，客户的服务器为 windows
                        if (urlReplace) {
                            // 本地调试时启用
                            url = url.replace(ParamConstant.TARGET_DATASOURCE_IP_PORT, ParamConstant.PROXY_DATASOURCE_IP_PORT);
                        }
                        property.setUrl(url);
                        property.setUsername(rs.getString(DataSourceConfigEnum.DS_USERNAME.getName()));
                        property.setPassword(rs.getString(DataSourceConfigEnum.DS_PASSWORD.getName()));
                        dataSourcePropertiesMap.put(name, property);
                    }
                    if (dataSourcePropertiesMap.isEmpty()) {
                        DataSourceProperty dataSourceProperty = new DataSourceProperty();
                        dataSourceProperty.setDriverClassName(dataBaseConfig.getDriverClassName());
                        dataSourceProperty.setUrl(dataBaseConfig.getUrl());
                        dataSourceProperty.setUsername(dataBaseConfig.getUsername());
                        dataSourceProperty.setPassword(dataBaseConfig.getPassword());
                        dataSourcePropertiesMap.put(dataBaseConfig.getName(), dataSourceProperty);
                    }
                } catch (SQLException e) {
                    throw new ServiceException(e.getMessage());
                } finally {
                    try {
                        if (rs != null) {
                            rs.close();
                        }
                    } catch (SQLException e) {
                        log.error(e.getMessage());
                    }
                    try {
                        statement.close();
                    } catch (SQLException e) {
                        log.error(e.getMessage());
                    }
                }
                return dataSourcePropertiesMap;
            }
        };
    }
}
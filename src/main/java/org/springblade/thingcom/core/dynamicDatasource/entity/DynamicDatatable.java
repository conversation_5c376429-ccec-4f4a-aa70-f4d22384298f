package org.springblade.thingcom.core.dynamicDatasource.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springblade.thingcom.core.mybatisPlus.entity.BaseEntity;

import java.io.Serializable;

/**
 * 数据表管理实体类
 *
 * <AUTHOR>
 * @since 2024-05-06 19:32:05
 */
@Data
@TableName("tb_dynamic_datatable")
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class DynamicDatatable extends BaseEntity<DynamicDatatable> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 数据表名称
     */
    private String name;

    /**
     * 创建表SQL
     */
    private String createTableSql;

    /**
     * 插入数据SQL
     */
    private String insertDataSql;

}

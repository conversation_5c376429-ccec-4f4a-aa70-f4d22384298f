package org.springblade.thingcom.core.dynamicDatasource.vo;

import lombok.Data;
import org.springblade.thingcom.core.dynamicDatasource.enums.DataTableTimeScopeEnum;

/**
 * @ClassName DynamicDataTimeScopeVO
 * @Description: 动态数据时间范围VO
 * @Author: zgq
 * @Date: 2024/4/17 16:37
 * @Version: 1.0
 **/
@Data
public class DynamicDataTimeScopeVO {

    /**
     * 时间范围
     * @see DataTableTimeScopeEnum
     */
    private String timeScope;

    /**
     * 时间范围值
     * 月：1-12
     * 季度：1-4
     * 半年：1-2
     * 年：2024-
     */
    private Integer timeScopeValue;
}

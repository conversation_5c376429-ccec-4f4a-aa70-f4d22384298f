package org.springblade.thingcom.core.dynamicDatasource.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @ClassName DataExportEnum
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/7/21 16:40
 * @Version 1.0
 **/
@Getter
@AllArgsConstructor
public enum DataSourceTypeEnum {

    /**
     * 主库
     */
    MASTER(1, "系统业务数据", "arctech_trackrobot"),

    /**
     * 从库，根据年度分库，例如：arctech_trackrobot_log_2024
     */
    SLAVE(2, "历史数据", "arctech_trackrobot_log_"),

    ;

    private final Integer code;
    private final String label;
    private final String dataBaseName;
}

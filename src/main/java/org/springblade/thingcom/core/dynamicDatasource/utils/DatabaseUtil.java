package org.springblade.thingcom.core.dynamicDatasource.utils;

import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DataSourceProperty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.sql.DataSource;

/**
 * <p>
 * 数据库工具类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-12
 */
@Slf4j
@Component
public class DatabaseUtil {

    @Resource
    private DataSource dataSource;
    @Resource
    private DefaultDataSourceCreator dataSourceCreator;

    public static String replaceDatabase(String url, String database) {
        return url.contains("?") ?
                url.replaceAll(("(?<=\\d/).*(?=\\?)"), database) :
                url.replaceAll(("(?<=\\d/).*"), database);
    }

    /**
     * @param database   数据库名
     * @param dsProperty 数据库模板属性
     */
    public void addDatasourceWithCurrent(String database, DataSourceProperty dsProperty) {
        DynamicRoutingDataSource ds = (DynamicRoutingDataSource) dataSource;

        // url里加入数据库名称
        dsProperty.setUrl(DatabaseUtil.replaceDatabase(dsProperty.getUrl(), database));

        // 数据库列表里不存在
        if (!ds.getDataSources().containsKey(dsProperty.getPoolName())) {
            // 创建数据库连接并添加到列表
            DataSource dataSource = dataSourceCreator.createDataSource(dsProperty);
            ds.addDataSource(dsProperty.getPoolName(), dataSource);
            log.info("++++++++++: " + ds.getDataSources().keySet());
        }
    }

}

package org.springblade.thingcom.core.dynamicDatasource.service.impl;

import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DataSourceProperty;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.enums.DictEnum;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.modules.system.entity.Dict;
import org.springblade.modules.system.service.IDictService;
import org.springblade.thingcom.core.dynamicDatasource.entity.DynamicDatasource;
import org.springblade.thingcom.core.dynamicDatasource.enums.DataSourceConfigEnum;
import org.springblade.thingcom.core.dynamicDatasource.mapper.DynamicDatasourceMapper;
import org.springblade.thingcom.core.dynamicDatasource.service.DynamicDatasourceService;
import org.springblade.thingcom.core.dynamicDatasource.utils.DataTimeScopeUtil;
import org.springblade.thingcom.core.dynamicDatasource.utils.DatabaseUtil;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.List;
import java.util.Objects;

/**
 * 动态数据源服务实现类
 *
 * <AUTHOR>
 * @since 2022-11-16 17:23:04
 */
@Slf4j
@Service
@AllArgsConstructor
public class DynamicDatasourceServiceImpl extends ServiceImpl<DynamicDatasourceMapper, DynamicDatasource> implements DynamicDatasourceService {

    private final DatabaseUtil databaseUtil;
    private final IDictService dictService;

    @Override
    public String getDynamicDatasourceName(Integer year) {
        // 数据源名称
        String dbName = DataTimeScopeUtil.buildDbName(year);
        synchronized (DynamicDatasource.class) {
            // 获取数据源
            DynamicDatasource dynamicDatasource = baseMapper.selectOne(Wrappers.<DynamicDatasource>lambdaQuery()
                    .like(DynamicDatasource::getName, dbName)
                    .orderByDesc(DynamicDatasource::getCreateTime).last("limit 1"));
            // 没有数据源，则新建
            if (Objects.isNull(dynamicDatasource)) {
                createDataSource(dbName);
            } else {
                dbName = dynamicDatasource.getName();
            }
        }
        return dbName;
    }

    private void createDataSource(String dbName) {
        // 获取数据源信息
        DataSourceProperty dsProperty = getDataSourceProperty(dbName);
        Connection conn = null;
        Statement stmt = null;
        boolean commit = false;
        try {
            Class.forName(dsProperty.getDriverClassName());
            conn = DriverManager.getConnection(dsProperty.getUrl(), dsProperty.getUsername(), dsProperty.getPassword());
            stmt = conn.createStatement();
            stmt.executeUpdate(String.format("CREATE DATABASE IF NOT EXISTS `%s` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci", dbName));
            databaseUtil.addDatasourceWithCurrent(dbName, dsProperty);
            // 数据库管理表里插入数据
            DynamicDatasource dds = BeanUtil.copy(dsProperty, DynamicDatasource.class);
            dds.setName(dsProperty.getPoolName());
            dds.setDriver(dsProperty.getDriverClassName());
            baseMapper.insert(dds);
            commit = true;
        } catch (ClassNotFoundException | SQLException e) {
            throw new ServiceException(e.getMessage());
        } finally {
            if (stmt != null) {
                if (!commit) {
                    try {
                        // 事务中动态创建的数据库和表无法回滚，所以要手动删除
                        stmt.executeUpdate(String.format("DROP DATABASE IF EXISTS `%s`", dbName));
                    } catch (SQLException e) {
                        log.error("删除数据源异常，{}", e.getMessage());
                    }
                }
                try {
                    stmt.close();
                } catch (SQLException e) {
                    log.error("", e);
                }
            }
            try {
                if (conn != null) {
                    conn.close();
                }
            } catch (SQLException e) {
                log.error("", e);
            }
        }
    }

    private DataSourceProperty getDataSourceProperty(String dbName) {
        DataSourceProperty dataSourceProperty = new DataSourceProperty();
        dataSourceProperty.setPoolName(dbName);
        List<Dict> dictList = dictService.getList(DictEnum.DATA_SOURCE.getName());
        dictList.forEach(dict -> {
            if (DataSourceConfigEnum.DS_DRIVER.getName().equals(dict.getDictKey())) {
                dataSourceProperty.setDriverClassName(dict.getDictValue());
            } else if (DataSourceConfigEnum.DS_URL.getName().equals(dict.getDictKey())) {
                dataSourceProperty.setUrl(dict.getDictValue());
            } else if (DataSourceConfigEnum.DS_USERNAME.getName().equals(dict.getDictKey())) {
                dataSourceProperty.setUsername(dict.getDictValue());
            } else if (DataSourceConfigEnum.DS_PASSWORD.getName().equals(dict.getDictKey())) {
                dataSourceProperty.setPassword(dict.getDictValue());
            }
        });
        return dataSourceProperty;
    }

}

package org.springblade.thingcom.core.mybatisPlus.handler;

import com.baomidou.mybatisplus.core.metadata.TableFieldInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.extension.plugins.handler.TenantLineHandler;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.StringValue;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tenant.BladeTenantProperties;
import org.springblade.core.tenant.annotation.TableExclude;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springframework.beans.factory.SmartInitializingSingleton;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 自定义多租户逻辑
 *
 * <AUTHOR>
 * @since 2022/9/14 15:45
 */
@Component
public class ThingcomTenantHandler implements TenantLineHandler, SmartInitializingSingleton {
    private final List<String> tenantTableList = new ArrayList();
    private final List<String> excludeTableList = Arrays.asList("act_de_model");
    private final BladeTenantProperties tenantProperties;

    @Override
    public Expression getTenantId() {
        return new StringValue(Func.toStr(AuthUtil.getTenantId(), BladeConstant.ADMIN_TENANT_ID));
    }

    @Override
    public String getTenantIdColumn() {
        return this.tenantProperties.getColumn();
    }

    @Override
    public boolean ignoreTable(String tableName) {
        return !this.tenantTableList.contains(tableName) || StringUtil.isBlank(AuthUtil.getTenantId());
    }

    @Override
    public void afterSingletonsInstantiated() {
		ApplicationContext context = SpringUtil.getContext();
		if (this.tenantProperties.getAnnotationExclude() && context != null) {
			Map<String, Object> tables = context.getBeansWithAnnotation(TableExclude.class);
			List<String> excludeTables = this.tenantProperties.getExcludeTables();
			Iterator var4 = tables.values().iterator();

			while(var4.hasNext()) {
				Object o = var4.next();
				TableExclude annotation = (TableExclude)o.getClass().getAnnotation(TableExclude.class);
				String value = annotation.value();
				excludeTables.add(value);
			}
		}

		List<TableInfo> tableInfos = TableInfoHelper.getTableInfos();
		Iterator var11 = tableInfos.iterator();

		while(true) {
			while(true) {
				TableInfo tableInfo;
				String tableName;
				do {
					do {
						do {
							if (!var11.hasNext()) {
								return;
							}

							tableInfo = (TableInfo)var11.next();
							tableName = tableInfo.getTableName();
						} while(this.tenantProperties.getExcludeTables().contains(tableName));
					} while(this.excludeTableList.contains(tableName.toLowerCase()));
				} while(this.excludeTableList.contains(tableName.toUpperCase()));

				List<TableFieldInfo> fieldList = tableInfo.getFieldList();
				Iterator var15 = fieldList.iterator();

				while(var15.hasNext()) {
					TableFieldInfo fieldInfo = (TableFieldInfo)var15.next();
					String column = fieldInfo.getColumn();
					if (this.tenantProperties.getColumn().equals(column)) {
						this.tenantTableList.add(tableName);
						break;
					}
				}
			}
		}
    }

    public ThingcomTenantHandler(final BladeTenantProperties tenantProperties) {
        this.tenantProperties = tenantProperties;
    }
}
package org.springblade.thingcom.core.mybatisPlus;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Description
 * @date 2022/4/8 11:48
 */
public interface IBaseService<T> extends IService<T> {

    boolean removeByIdWithFill(Serializable id);

    boolean removeWithFill(@Param(Constants.ENTITY) T param, @Param(Constants.WRAPPER) Wrapper<T> wrapper);

    /**
     * 底层的批量插入<br/>
     * 注意：<b>如果字段为null，数据库设置了默认值，默认值无法自动填充！！！<b/>
     *
     * @param entityList
     * @return
     */
    boolean saveBatchSomeColumn(List<T> entityList);
}
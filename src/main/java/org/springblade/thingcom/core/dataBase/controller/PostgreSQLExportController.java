package org.springblade.thingcom.core.dataBase.controller;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springblade.thingcom.core.dataBase.dto.PostgreSQLExportDTO;
import org.springblade.thingcom.core.dataBase.service.PostgreSQLExportService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;

/**
 * @ClassName PostgreSQLExportController
 * @Description: PostgreSQL导出控制器
 * @Author: Augment
 * @Date: 2024/7/25
 * @Version: 1.0
 **/
@RestController
@AllArgsConstructor
@RequestMapping("/postgresql-export")
@Validated
public class PostgreSQLExportController extends BladeController {

    private final PostgreSQLExportService postgreSQLExportService;

    /**
     * 导出PostgreSQL数据库（完整功能）
     *
     * @param response HTTP响应
     * @param exportDTO 导出参数
     */
    @SneakyThrows
    @PostMapping(value = "/export")
    public void exportDatabase(HttpServletResponse response, @Valid @RequestBody PostgreSQLExportDTO exportDTO) {
        postgreSQLExportService.exportDatabase(response, exportDTO);
    }

    /**
     * 快速导出PostgreSQL数据库（使用默认配置）
     *
     * @param response HTTP响应
     * @param dbName 数据库名称
     */
    @SneakyThrows
    @GetMapping("/export/quick")
    public void exportDatabaseQuick(HttpServletResponse response,
                                   @RequestParam String dbName,
                                   @RequestParam(required = false) String host,
                                   @RequestParam(required = false) String port,
                                   @RequestParam(required = false) String username,
                                   @RequestParam(required = false) String password) {
        PostgreSQLExportDTO exportDTO = new PostgreSQLExportDTO();
        exportDTO.setDbName(dbName);
        exportDTO.setHost(host);
        exportDTO.setPort(port);
        exportDTO.setUsername(username);
        exportDTO.setPassword(password);
        exportDTO.setExportType(PostgreSQLExportDTO.ExportType.FULL);

        postgreSQLExportService.exportDatabase(response, exportDTO);
    }

    /**
     * 导出PostgreSQL数据库结构
     *
     * @param response HTTP响应
     * @param dbName 数据库名称
     */
    @SneakyThrows
    @GetMapping("/export/schema")
    public void exportDatabaseSchema(HttpServletResponse response,
                                    @RequestParam String dbName,
                                    @RequestParam(required = false) String host,
                                    @RequestParam(required = false) String port,
                                    @RequestParam(required = false) String username,
                                    @RequestParam(required = false) String password) {
        PostgreSQLExportDTO exportDTO = new PostgreSQLExportDTO();
        exportDTO.setDbName(dbName);
        exportDTO.setHost(host);
        exportDTO.setPort(port);
        exportDTO.setUsername(username);
        exportDTO.setPassword(password);
        exportDTO.setExportType(PostgreSQLExportDTO.ExportType.SCHEMA);

        postgreSQLExportService.exportDatabase(response, exportDTO);
    }

    /**
     * 导出PostgreSQL数据库数据
     *
     * @param response HTTP响应
     * @param dbName 数据库名称
     */
    @SneakyThrows
    @GetMapping("/export/data")
    public void exportDatabaseData(HttpServletResponse response,
                                  @RequestParam String dbName,
                                  @RequestParam(required = false) String host,
                                  @RequestParam(required = false) String port,
                                  @RequestParam(required = false) String username,
                                  @RequestParam(required = false) String password) {
        PostgreSQLExportDTO exportDTO = new PostgreSQLExportDTO();
        exportDTO.setDbName(dbName);
        exportDTO.setHost(host);
        exportDTO.setPort(port);
        exportDTO.setUsername(username);
        exportDTO.setPassword(password);
        exportDTO.setExportType(PostgreSQLExportDTO.ExportType.DATA);

        postgreSQLExportService.exportDatabase(response, exportDTO);
    }

    /**
     * 导出PostgreSQL指定表
     *
     * @param response HTTP响应
     * @param dbName 数据库名称
     * @param tables 表名列表，用逗号分隔
     */
    @SneakyThrows
    @GetMapping("/export/tables")
    public void exportDatabaseTables(HttpServletResponse response,
                                    @RequestParam String dbName,
                                    @RequestParam String tables,
                                    @RequestParam(required = false) String host,
                                    @RequestParam(required = false) String port,
                                    @RequestParam(required = false) String username,
                                    @RequestParam(required = false) String password) {
        if (StrUtil.isBlank(tables)) {
            throw new IllegalArgumentException("表名不能为空");
        }

        PostgreSQLExportDTO exportDTO = new PostgreSQLExportDTO();
        exportDTO.setDbName(dbName);
        exportDTO.setHost(host);
        exportDTO.setPort(port);
        exportDTO.setUsername(username);
        exportDTO.setPassword(password);
        exportDTO.setExportType(PostgreSQLExportDTO.ExportType.TABLES);
        //exportDTO.setTableNames(List.of(tables.split(",")));

        postgreSQLExportService.exportDatabase(response, exportDTO);
    }

    /**
     * 测试数据库连接
     *
     * @param host 主机
     * @param port 端口
     * @param dbName 数据库名
     * @param username 用户名
     * @param password 密码
     * @return 连接测试结果
     */
    @GetMapping("/test-connection")
    public R<Boolean> testConnection(@RequestParam(required = false) String host,
                                    @RequestParam(required = false) String port,
                                    @RequestParam String dbName,
                                    @RequestParam(required = false) String username,
                                    @RequestParam(required = false) String password) {
        boolean connected = postgreSQLExportService.testConnection(host, port, dbName, username, password);
        return data(connected);
    }

    /**
     * 获取数据库表列表
     *
     * @param host 主机
     * @param port 端口
     * @param dbName 数据库名
     * @param username 用户名
     * @param password 密码
     * @return 表名列表
     */
    @GetMapping("/tables")
    public R<List<String>> getTableList(@RequestParam(required = false) String host,
                                       @RequestParam(required = false) String port,
                                       @RequestParam String dbName,
                                       @RequestParam(required = false) String username,
                                       @RequestParam(required = false) String password) {
        List<String> tables = postgreSQLExportService.getTableList(host, port, dbName, username, password);
        return data(tables);
    }

    /**
     * 获取数据库大小
     *
     * @param host 主机
     * @param port 端口
     * @param dbName 数据库名
     * @param username 用户名
     * @param password 密码
     * @return 数据库大小（字节）
     */
    @GetMapping("/database-size")
    public R<Long> getDatabaseSize(@RequestParam(required = false) String host,
                                  @RequestParam(required = false) String port,
                                  @RequestParam String dbName,
                                  @RequestParam(required = false) String username,
                                  @RequestParam(required = false) String password) {
        long size = postgreSQLExportService.getDatabaseSize(host, port, dbName, username, password);
        return data(size);
    }

    /**
     * 导出数据库到服务器文件
     *
     * @param exportDTO 导出参数
     * @param filePath 文件路径
     * @return 导出结果
     */
    @PostMapping(value = "/export-to-file", consumes = {"application/json", "application/json;charset=UTF-8"})
    public R<String> exportDatabaseToFile(@Valid @RequestBody PostgreSQLExportDTO exportDTO,
                                         @RequestParam String filePath) {
        String result = postgreSQLExportService.exportDatabaseToFile(exportDTO, filePath);
        return data(result);
    }

    /**
     * 使用表单参数导出PostgreSQL数据库（避免Content-Type问题）
     *
     * @param response HTTP响应
     * @param host 主机
     * @param port 端口
     * @param dbName 数据库名
     * @param username 用户名
     * @param password 密码
     * @param exportType 导出类型
     * @param format 导出格式
     * @param compress 是否压缩
     * @param tableNames 表名列表（逗号分隔）
     */
    @SneakyThrows
    @PostMapping("/export-form")
    public void exportDatabaseForm(HttpServletResponse response,
                                  @RequestParam(required = false) String host,
                                  @RequestParam(required = false) String port,
                                  @RequestParam String dbName,
                                  @RequestParam(required = false) String username,
                                  @RequestParam(required = false) String password,
                                  @RequestParam(required = false, defaultValue = "FULL") String exportType,
                                  @RequestParam(required = false, defaultValue = "PLAIN") String format,
                                  @RequestParam(required = false, defaultValue = "false") Boolean compress,
                                  @RequestParam(required = false) String tableNames) {

        PostgreSQLExportDTO exportDTO = new PostgreSQLExportDTO();
        exportDTO.setHost(host);
        exportDTO.setPort(port);
        exportDTO.setDbName(dbName);
        exportDTO.setUsername(username);
        exportDTO.setPassword(password);
        exportDTO.setCompress(compress);

        // 设置导出类型
        try {
            exportDTO.setExportType(PostgreSQLExportDTO.ExportType.valueOf(exportType.toUpperCase()));
        } catch (IllegalArgumentException e) {
            exportDTO.setExportType(PostgreSQLExportDTO.ExportType.FULL);
        }

        // 设置导出格式
        try {
            exportDTO.setFormat(PostgreSQLExportDTO.ExportFormat.valueOf(format.toUpperCase()));
        } catch (IllegalArgumentException e) {
            exportDTO.setFormat(PostgreSQLExportDTO.ExportFormat.PLAIN);
        }

        // 设置表名列表
        if (StrUtil.isNotBlank(tableNames) && exportDTO.getExportType() == PostgreSQLExportDTO.ExportType.TABLES) {
            exportDTO.setTableNames(Arrays.asList(tableNames.split(",")));
        }

        postgreSQLExportService.exportDatabase(response, exportDTO);
    }
}

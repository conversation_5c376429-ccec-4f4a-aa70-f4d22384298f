package org.springblade.thingcom.core.dataBase.sevice;

import cn.hutool.db.Db;
import org.springblade.thingcom.core.dynamicDatasource.enums.DataSourceTypeEnum;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * @ClassName DataBaseService
 * @Description 数据库服务接口
 * <AUTHOR>
 * @Date 2024/7/21 10:01
 * @Version 1.0
 **/
public interface DataBaseService {

    /**
     * 获取数据库
     *
     * @return
     */
    Db getDataBase();

    /**
     * 导出数据
     *
     * @param response
     * @param type，1-系统业务数据；2-历史数据
     * @return
     * @see DataSourceTypeEnum
     */
    void exportData(HttpServletResponse response, Integer type);

    /**
     * 导入数据
     *
     * @param file
     * @return
     */
    void importData(MultipartFile file) throws IOException;

    /**
     * 导出 PostgresSQL 数据
     *
     * @param response
     * @return
     */
    /**
     * 导出PostgreSQL数据库（使用默认配置）
     *
     * @param response HTTP响应
     */
    void exportDataPostgresSQL(HttpServletResponse response);

    /**
     * 导出PostgreSQL数据库
     *
     * @param response HTTP响应
     * @param host 数据库主机
     * @param port 数据库端口
     * @param dbName 数据库名称
     */
    void exportDataPostgresSQL(HttpServletResponse response, String host, String port, String dbName);

    /**
     * 导出PostgreSQL数据库（完整参数）
     *
     * @param response HTTP响应
     * @param host 数据库主机
     * @param port 数据库端口
     * @param dbName 数据库名称
     * @param user 用户名
     * @param password 密码
     */
    void exportDataPostgresSQL(HttpServletResponse response, String host, String port, String dbName, String user, String password);

    /**
     * 导出PostgreSQL数据库表结构
     *
     * @param response HTTP响应
     * @param host 数据库主机
     * @param port 数据库端口
     * @param dbName 数据库名称
     * @param user 用户名
     * @param password 密码
     */
    void exportPostgreSQLSchema(HttpServletResponse response, String host, String port, String dbName, String user, String password);

    /**
     * 导出PostgreSQL数据库数据（仅数据，不包含结构）
     *
     * @param response HTTP响应
     * @param host 数据库主机
     * @param port 数据库端口
     * @param dbName 数据库名称
     * @param user 用户名
     * @param password 密码
     */
    void exportPostgreSQLDataOnly(HttpServletResponse response, String host, String port, String dbName, String user, String password);

    /**
     * 导出PostgreSQL指定表
     *
     * @param response HTTP响应
     * @param host 数据库主机
     * @param port 数据库端口
     * @param dbName 数据库名称
     * @param user 用户名
     * @param password 密码
     * @param tableNames 表名列表
     */
    void exportPostgreSQLTables(HttpServletResponse response, String host, String port, String dbName, String user, String password, String... tableNames);
}

package org.springblade.thingcom.core.dataBase.sevice;

import cn.hutool.db.Db;
import org.springblade.thingcom.core.dynamicDatasource.enums.DataSourceTypeEnum;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * @ClassName DataBaseService
 * @Description 数据库服务接口
 * <AUTHOR>
 * @Date 2024/7/21 10:01
 * @Version 1.0
 **/
public interface DataBaseService {

    /**
     * 获取数据库
     *
     * @return
     */
    Db getDataBase();

    /**
     * 导出数据
     *
     * @param response
     * @param type，1-系统业务数据；2-历史数据
     * @return
     * @see DataSourceTypeEnum
     */
    void exportData(HttpServletResponse response, Integer type);

    /**
     * 导入数据
     *
     * @param file
     * @return
     */
    void importData(MultipartFile file) throws IOException;

    void exportDataPostgresSQL(HttpServletResponse response);
}

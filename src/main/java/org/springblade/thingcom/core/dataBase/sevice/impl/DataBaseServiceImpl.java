package org.springblade.thingcom.core.dataBase.sevice.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.io.file.FileWriter;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.StrBuilder;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.Db;
import cn.hutool.db.DbUtil;
import cn.hutool.db.Entity;
import cn.hutool.db.ds.DSFactory;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.apache.tools.ant.Project;
import org.apache.tools.ant.taskdefs.SQLExec;
import org.apache.tools.ant.types.EnumeratedAttribute;
import org.springblade.common.enums.DictEnum;
import org.springblade.common.utils.CommonUtil;
import org.springblade.modules.system.entity.Dict;
import org.springblade.modules.system.service.IDictService;
import org.springblade.thingcom.core.dataBase.constant.ParamConstant;
import org.springblade.thingcom.core.dataBase.mapper.DataBaseMapper;
import org.springblade.thingcom.core.dataBase.sevice.DataBaseService;
import org.springblade.thingcom.core.dynamicDatasource.entity.DynamicDatasource;
import org.springblade.thingcom.core.dynamicDatasource.enums.DataSourceConfigEnum;
import org.springblade.thingcom.core.dynamicDatasource.enums.DataSourceTypeEnum;
import org.springblade.thingcom.core.dynamicDatasource.enums.DataTableTimeScopeEnum;
import org.springblade.thingcom.core.dynamicDatasource.service.DynamicDatasourceService;
import org.springblade.thingcom.core.dynamicDatasource.utils.DataTimeScopeUtil;
import org.springblade.thingcom.core.dynamicDatasource.vo.DynamicDataTimeScopeVO;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * @ClassName DataBaseServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/7/21 10:02
 * @Version 1.0
 **/
@Service
@AllArgsConstructor
public class DataBaseServiceImpl implements DataBaseService {

    private final Environment environment;
    private final DynamicDatasourceService dynamicDatasourceService;
    private final DataBaseMapper dataBaseMapper;
    private final IDictService dictService;

    @SneakyThrows
    @Override
    public Db getDataBase() {
        // 获取数据库配置
        String dbSetting = ParamConstant.DB_SETTING_PREFIX + environment.getActiveProfiles()[0];
        Db db = DbUtil.use(DSFactory.get(dbSetting));
        // 获取数据库下所有的表
        //List<Entity> tableEntityList = db.query("SHOW TABLES FROM " + "dataBaseName");
        // 创建表 DDL 语句
        //Entity createTableEntity = db.queryOne("SHOW CREATE TABLE " + "tableName");
        // 查询表数据
        //List<Entity> dataEntityList = db.query("SELECT * FROM " + "tableName");
        // 获取数据库配置
        //String dbSetting = ParamConstant.DB_SETTING_PREFIX + environment.getActiveProfiles()[0];
        //Setting setting = new Setting(ParamConstant.DB_SETTING).getSetting(dbSetting);
        //String driver = setting.getAndRemoveStr(DSFactory.KEY_ALIAS_DRIVER);
        //String url = setting.getAndRemoveStr(DSFactory.KEY_ALIAS_URL);
        //String userId = setting.getAndRemoveStr(DSFactory.KEY_ALIAS_USER);
        //String password = setting.getAndRemoveStr(DSFactory.KEY_ALIAS_PASSWORD);
        return db;
    }

    @SneakyThrows
    @Override
    public void exportData(HttpServletResponse response, Integer type) {
        if (DataSourceTypeEnum.MASTER.getCode().equals(type)) {
            // 导出主库数据
            exportDataMaster(response, DataSourceTypeEnum.MASTER.getDataBaseName());
        } else {
            // 导出从库数据
            exportDataSlave(response);
        }
    }

    @SneakyThrows
    public void exportDataMaster(HttpServletResponse response, String dbName) {
        // 获取数据库下所有的表
        List<String> tableNameList = dataBaseMapper.listTableName(dbName, null);
        if (CollUtil.isEmpty(tableNameList)) {
            return;
        }
        // 导出表数据
        exportTableData(response, dbName, tableNameList);
    }

    @SneakyThrows
    public void exportDataSlave(HttpServletResponse response) {
        // 当前时间
        LocalDateTime now = CommonUtil.getZoneTime();
        int year = now.getYear();
        // 查询从库的数据库前缀
        String dataBasePrefix = DataSourceTypeEnum.SLAVE.getDataBaseName();
        String dbNameCurrentYear = dataBasePrefix + year;
        String dbNameYesterdatYear = dataBasePrefix + (year - 1);
        // 查询当年和去年的从库，避免当年没有数据
        List<DynamicDatasource> datasourceList = dynamicDatasourceService.list(Wrappers.<DynamicDatasource>lambdaQuery()
                .in(DynamicDatasource::getName, dbNameCurrentYear, dbNameYesterdatYear)
                .orderByDesc(DynamicDatasource::getId));
        Assert.notEmpty(datasourceList, "暂无数据");
        // 获取最新有数据的从库
        DynamicDatasource dataSource = datasourceList.get(0);
        String dbName = dataSource.getName();
        // 获取数据时间域
        DynamicDataTimeScopeVO dataTimeScope = DataTimeScopeUtil.getDataTimeScope(now.toLocalDate());
        String timeScope = dataTimeScope.getTimeScope();
        Integer timeScopeValue = dataTimeScope.getTimeScopeValue();
        String tableDataScope = timeScope + "_" + timeScopeValue;
        // 获取数据库下所有的表
        List<String> tableNameList = dataBaseMapper.listTableName(dbName, tableDataScope);
        // 如果没有对应数据，查询上一个的数据时间域
        if (CollUtil.isEmpty(tableNameList)) {
            // 年：去年
            if (DataTableTimeScopeEnum.YEAR.getLabel().equals(timeScope)) {
                timeScopeValue = timeScopeValue - 1;
            } else {
                // 今年-1月：去年-12月；今年-1季度：去年-4季度；今年-上半年：去年-下半年
                if (timeScopeValue - 1 == 0) {
                    // 没有去年的数据
                    Assert.isTrue(datasourceList.size() > 1, "暂无数据");
                    if (DataTableTimeScopeEnum.MONTH.getLabel().equals(timeScope)) {
                        timeScopeValue = 12;
                    } else if (DataTableTimeScopeEnum.QUARTER.getLabel().equals(timeScope)) {
                        timeScopeValue = 4;
                    } else if (DataTableTimeScopeEnum.HALF_YEAR.getLabel().equals(timeScope)) {
                        timeScopeValue = 2;
                    }
                } else {
                    // 上一个的数据时间域
                    timeScopeValue = timeScopeValue - 1;
                }
                dataSource = datasourceList.get(1);
                dbName = dataSource.getName();
            }
            tableDataScope = timeScope + "_" + timeScopeValue;
            tableNameList = dataBaseMapper.listTableName(dbName, tableDataScope);
            Assert.notEmpty(tableNameList, "暂无数据");
        }
        // 导出表数据
        exportTableData(response, dbName, tableNameList);
    }

    @Override
    public void importData(MultipartFile multipartFile) throws IOException {
        // 校验文件
        Assert.notNull(multipartFile, "文件不能为空");
        Assert.isTrue(multipartFile.getOriginalFilename().endsWith(ParamConstant.SQL_SUFFIX), "文件格式不正确");
        // 创建 SQLExec
        SQLExec sqlExec = new SQLExec();
        // 获取数据库配置
        List<Dict> dictList = dictService.getList(DictEnum.DATA_SOURCE.getName());
        for (Dict dict : dictList) {
            if (DataSourceConfigEnum.DS_DRIVER.getName().equals(dict.getDictKey())) {
                sqlExec.setDriver(dict.getDictValue());
            } else if (DataSourceConfigEnum.DS_URL.getName().equals(dict.getDictKey())) {
                sqlExec.setUrl(dict.getDictValue());
            } else if (DataSourceConfigEnum.DS_USERNAME.getName().equals(dict.getDictKey())) {
                sqlExec.setUserid(dict.getDictValue());
            } else if (DataSourceConfigEnum.DS_PASSWORD.getName().equals(dict.getDictKey())) {
                sqlExec.setPassword(dict.getDictValue());
            }
        }
        File tempFile = File.createTempFile("importData-", ".sql");
        multipartFile.transferTo(tempFile);
        sqlExec.setSrc(tempFile);
        // 处理错误
        sqlExec.setOnerror((SQLExec.OnError) (EnumeratedAttribute.getInstance(SQLExec.OnError.class, "continue")));
        sqlExec.setPrint(false);
        // 执行
        sqlExec.setProject(new Project());
        try {
            sqlExec.execute();
        } finally {
            // 确保文件删除
            tempFile.delete();
        }
    }

    @Override
    public void exportDataPostgresSQL(HttpServletResponse response) {
        //String host = "*************";
        String host = "localhost";
        String port = "5432";
        String dbName = "loraserver_ns";
        String user = "postgres";
        String password = "postgres";
        try {
            String command = String.format("pg_dump -h %s -p %s -U %s -d %s --format=p --no-password", host, port, user, dbName);

            ProcessBuilder processBuilder = new ProcessBuilder("cmd.exe", "/c", command);
            processBuilder.environment().put("PGPASSWORD", password);
            processBuilder.redirectErrorStream(true);
            Process process = processBuilder.start();

            // 设置响应头
            String fileName = dbName + "-" + LocalDateTime.now() + ".sql";
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));

            // 直接写入响应流
            try (InputStream inputStream = process.getInputStream();
                 OutputStream outputStream = response.getOutputStream()) {

                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
            }

            int exitCode = process.waitFor();
            if (exitCode != 0) {
                throw new RuntimeException("数据库导出失败，退出码：" + exitCode);
            }

        } catch (Exception e) {
            throw new RuntimeException("导出数据库失败：" + e.getMessage(), e);
        }
    }

    /**
     * 获取文件名
     *
     * @param dataName 数据库名
     * @return 文件名
     */
    private static String getFileName(String dataName) {
        LocalDateTime now = CommonUtil.getZoneTime();
        String fileName = dataName + "_" + LocalDateTimeUtil.format(now, DatePattern.PURE_DATETIME_PATTERN);
        fileName = fileName + ParamConstant.SQL_SUFFIX;
        return fileName;
    }

    /**
     * 获取文件名
     *
     * @param dataName 数据库名
     * @return 文件名
     */
    private static String getFileNameZip(String dataName) {
        LocalDateTime now = CommonUtil.getZoneTime();
        return dataName + "_" + LocalDateTimeUtil.format(now, DatePattern.PURE_DATETIME_PATTERN);
    }

    /**
     * 导出表数据
     *
     * @param response
     * @param dbName
     * @param tableNameList
     * @return
     */
    private void exportTableDataV1(HttpServletResponse response, String dbName, List<String> tableNameList) throws IOException {
        // 文件名
        String fileName = getFileName(dbName);
        String filePathName = ParamConstant.FILE_PATH + fileName;
        // 创建 sql 文件
        FileWriter sqlFileWriter = FileWriter.create(new File(filePathName));
        sqlFileWriter.write("");
        sqlFileWriter.append("USE " + dbName + ";\n");
        sqlFileWriter.append("SET NAMES utf8mb4;\n");
        sqlFileWriter.append("SET FOREIGN_KEY_CHECKS = 0;\n");
        for (String tableName : tableNameList) {
            sqlFileWriter.append("\n\n\n");
            // DROP TABLE，创建表
            sqlFileWriter.append("DROP TABLE IF EXISTS `" + tableName + "`;\n");
            // 数据库名称.表名
            String dbNameAndTableName = DataTimeScopeUtil.buildDbNameAndTableName(dbName, tableName);
            // CREATE TABLE DDL
            Entity createTableEntity = dataBaseMapper.queryCreateTableDDL(dbNameAndTableName);
            sqlFileWriter.append((String) createTableEntity.get("Create Table"));
            sqlFileWriter.append(";\n");
            // 查询表数据
            List<Entity> dataEntityList = dataBaseMapper.queryTableData(dbNameAndTableName);
            for (Entity dataEntity : dataEntityList) {
                StrBuilder field = StrBuilder.create();
                StrBuilder data = StrBuilder.create();
                dataEntity.forEach((key, value) -> {
                    String valueStr = StrUtil.toStringOrNull(value);
                    field.append("`").append(key).append("`").append(", ");
                    if (ObjectUtil.isNotNull(valueStr)) {
                        // 值包含 ' 转义处理
                        valueStr = StrUtil.replace(valueStr, "'", "\\'");
                        // boolean 值处理
                        if (StrUtil.equals("true", valueStr)) {
                            data.append("b'1'");
                        } else if (StrUtil.equals("false", valueStr)) {
                            data.append("b'0'");
                        } else {
                            data.append("'").append(valueStr).append("'");
                        }
                    } else {
                        data.append("NULL");
                    }
                    data.append(", ");
                });
                sqlFileWriter.append("INSERT INTO `" + tableName + "`(");
                String fieldStr = field.subString(0, field.length() - 2);
                sqlFileWriter.append(fieldStr);
                sqlFileWriter.append(") VALUES (");
                String dataStr = data.subString(0, data.length() - 2);
                sqlFileWriter.append(dataStr);
                sqlFileWriter.append(");\n");
            }
        }
        sqlFileWriter.append("\n\n\n");
        sqlFileWriter.append("SET FOREIGN_KEY_CHECKS = 1;\n");
        // 下载文件
        OutputStream outStream = null;
        try {
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
            byte[] fileData = Files.readAllBytes(Paths.get(filePathName));
            outStream = response.getOutputStream();
            outStream.write(fileData);
            outStream.flush();
            outStream.close();
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            if (outStream != null) {
                outStream.flush();
                outStream.close();
            }
        }
    }

    /**
     * 导出表数据
     *
     * @param response      HTTP 响应对象
     * @param dbName        数据库名称
     * @param tableNameList 表名列表
     */
    private void exportTableDataV2(HttpServletResponse response, String dbName, List<String> tableNameList) {
        // 文件名
        String fileName = getFileName(dbName);
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
        // 直接写入响应流
        try (OutputStream outStream = response.getOutputStream()) {
            // 写入头部信息
            writeHeader(outStream, dbName);
            // 处理每个表的数据
            for (String tableName : tableNameList) {
                outStream.write("\n\n\n".getBytes());
                // DROP TABLE 和 CREATE TABLE
                writeTableDDL(outStream, dbName, tableName);
                // 查询并写入表数据
                writeTableData(outStream, dbName, tableName);
            }
            // 写入尾部信息
            writeFooter(outStream);
        } catch (IOException e) {
            throw new RuntimeException("导出数据失败", e);
        }
    }

    /**
     * 导出表数据
     *
     * @param response      HTTP 响应对象
     * @param dbName        数据库名称
     * @param tableNameList 表名列表
     */
    private void exportTableData(HttpServletResponse response, String dbName, List<String> tableNameList) {
        // 文件名（不带扩展名）
        String baseFileName = getFileNameZip(dbName);
        String zipFileName = baseFileName + ParamConstant.ZIP_SUFFIX;
        String sqlFileName = baseFileName + ParamConstant.SQL_SUFFIX;
        response.setContentType("application/zip");
        response.setHeader("Content-Disposition", "attachment; filename=" + zipFileName);
        // 使用 ByteArrayOutputStream 存储 SQL 内容
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try (ZipOutputStream zipOut = new ZipOutputStream(baos)) {
            // 创建一个 ZipEntry
            ZipEntry zipEntry = new ZipEntry(sqlFileName);
            zipOut.putNextEntry(zipEntry);
            // 写入头部信息
            writeHeader(zipOut, dbName);
            // 处理每个表的数据
            for (String tableName : tableNameList) {
                zipOut.write("\n\n\n".getBytes());
                // DROP TABLE 和 CREATE TABLE
                writeTableDDL(zipOut, dbName, tableName);
                // 查询并写入表数据
                writeTableData(zipOut, dbName, tableName);
            }
            // 写入尾部信息
            writeFooter(zipOut);
            // 关闭当前 ZipEntry
            zipOut.closeEntry();
        } catch (IOException e) {
            throw new RuntimeException("导出数据失败", e);
        }
        // 将压缩内容写入响应流
        try (OutputStream responseStream = response.getOutputStream()) {
            baos.writeTo(responseStream);
        } catch (IOException e) {
            throw new RuntimeException("写入响应流失败", e);
        }
    }

    /**
     * 写入 SQL 文件头部信息
     *
     * @param outStream 输出流
     * @param dbName    数据库名称
     */
    private void writeHeader(OutputStream outStream, String dbName) throws IOException {
        outStream.write(("USE `" + dbName + "`;\n").getBytes());
        outStream.write("SET NAMES utf8mb4;\n".getBytes());
        outStream.write("SET FOREIGN_KEY_CHECKS = 0;\n".getBytes());
    }

    /**
     * 写入表的 DDL 信息
     *
     * @param outStream 输出流
     * @param dbName    数据库名称
     * @param tableName 表名
     */
    private void writeTableDDL(OutputStream outStream, String dbName, String tableName) throws IOException {
        // DROP TABLE IF EXISTS
        outStream.write(("DROP TABLE IF EXISTS `" + tableName + "`;\n").getBytes());
        // 获取 CREATE TABLE DDL
        String dbNameAndTableName = DataTimeScopeUtil.buildDbNameAndTableName(dbName, tableName);
        Entity createTableEntity = dataBaseMapper.queryCreateTableDDL(dbNameAndTableName);
        outStream.write(((String) createTableEntity.get("Create Table")).getBytes());
        outStream.write(";\n".getBytes());
    }

    /**
     * 写入表数据
     *
     * @param outStream 输出流
     * @param dbName    数据库名称
     * @param tableName 表名
     */
    private void writeTableData(OutputStream outStream, String dbName, String tableName) throws IOException {
        String dbNameAndTableName = DataTimeScopeUtil.buildDbNameAndTableName(dbName, tableName);
        int batchSize = 1000; // 每批次处理的记录数
        int offset = 0;
        while (true) {
            // 分批次查询数据
            List<Entity> dataEntityList = dataBaseMapper.queryTableDataBatch(dbNameAndTableName, offset, batchSize);
            if (CollUtil.isEmpty(dataEntityList)) {
                break; // 没有更多数据
            }
            // 拼接并写入 INSERT 语句
            for (Entity dataEntity : dataEntityList) {
                StrBuilder field = StrBuilder.create();
                StrBuilder data = StrBuilder.create();
                dataEntity.forEach((key, value) -> {
                    String valueStr = StrUtil.toStringOrNull(value);
                    field.append("`").append(key).append("`").append(", ");
                    if (ObjectUtil.isNotNull(valueStr)) {
                        valueStr = StrUtil.replace(valueStr, "'", "\\'");
                        if (StrUtil.equals("true", valueStr)) {
                            data.append("b'1'");
                        } else if (StrUtil.equals("false", valueStr)) {
                            data.append("b'0'");
                        } else {
                            data.append("'").append(valueStr).append("'");
                        }
                    } else {
                        data.append("NULL");
                    }
                    data.append(", ");
                });
                // 构建完整的 INSERT 语句
                String insertSQL = "INSERT INTO `" + tableName + "`(" +
                        field.subString(0, field.length() - 2) + ") VALUES (" +
                        data.subString(0, data.length() - 2) + ");\n";
                outStream.write(insertSQL.getBytes());
            }
            offset += batchSize;
        }
    }

    /**
     * 写入 SQL 文件尾部信息
     *
     * @param outStream 输出流
     */
    private void writeFooter(OutputStream outStream) throws IOException {
        outStream.write("\n\n\n".getBytes());
        outStream.write("SET FOREIGN_KEY_CHECKS = 1;\n".getBytes());
    }

}

package org.springblade.thingcom.core.dataBase.service;

import org.springblade.thingcom.core.dataBase.dto.PostgreSQLExportDTO;

import javax.servlet.http.HttpServletResponse;

/**
 * @ClassName PostgreSQLExportService
 * @Description: PostgreSQL导出服务接口
 * @Author: Augment
 * @Date: 2024/7/25
 * @Version: 1.0
 **/
public interface PostgreSQLExportService {

    /**
     * 导出PostgreSQL数据库
     *
     * @param response HTTP响应
     * @param exportDTO 导出参数
     */
    void exportDatabase(HttpServletResponse response, PostgreSQLExportDTO exportDTO);

    /**
     * 导出PostgreSQL数据库到文件
     *
     * @param exportDTO 导出参数
     * @param filePath 文件路径
     * @return 导出结果信息
     */
    String exportDatabaseToFile(PostgreSQLExportDTO exportDTO, String filePath);

    /**
     * 测试数据库连接
     *
     * @param host 主机
     * @param port 端口
     * @param dbName 数据库名
     * @param username 用户名
     * @param password 密码
     * @return 连接是否成功
     */
    boolean testConnection(String host, String port, String dbName, String username, String password);

    /**
     * 获取数据库表列表
     *
     * @param host 主机
     * @param port 端口
     * @param dbName 数据库名
     * @param username 用户名
     * @param password 密码
     * @return 表名列表
     */
    java.util.List<String> getTableList(String host, String port, String dbName, String username, String password);

    /**
     * 获取数据库大小
     *
     * @param host 主机
     * @param port 端口
     * @param dbName 数据库名
     * @param username 用户名
     * @param password 密码
     * @return 数据库大小（字节）
     */
    long getDatabaseSize(String host, String port, String dbName, String username, String password);
}

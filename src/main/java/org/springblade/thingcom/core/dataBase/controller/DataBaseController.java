package org.springblade.thingcom.core.dataBase.controller;

import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springblade.thingcom.core.dataBase.sevice.DataBaseService;
import org.springblade.thingcom.core.dynamicDatasource.enums.DataSourceTypeEnum;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * @ClassName DataBaseController
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/7/21 10:16
 * @Version 1.0
 **/
@AllArgsConstructor
@RestController
@RequestMapping("/data-base")
public class DataBaseController extends BladeController {

    private final DataBaseService dataBaseService;

    /**
     * 导出数据
     *
     * @param response
     * @param type，1-系统业务数据；2-历史数据
     * @return
     * @see DataSourceTypeEnum
     */
    @SneakyThrows
    @GetMapping("/export-data")
    public void exportData(HttpServletResponse response, Integer type) {
        // 默认导出主库数据
        if (type == null) {
            type = DataSourceTypeEnum.MASTER.getCode();
        }
        dataBaseService.exportData(response, type);
    }

    /**
     * 导入数据
     *
     * @param file 上传的文件
     * @return
     * @see DataSourceTypeEnum
     */
    @PostMapping("/import-data")
    public R importData(MultipartFile file) throws IOException {
        dataBaseService.importData(file);
        return status(true);
    }

    /**
     * 导出 PostgresSQL 数据
     *
     * @param response
     * @return
     */
    @SneakyThrows
    @GetMapping("/export-data/postgressql")
    public void exportDataPostgresSQL(HttpServletResponse response) {
        dataBaseService.exportDataPostgresSQL(response);
    }

}

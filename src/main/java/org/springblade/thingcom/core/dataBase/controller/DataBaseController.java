package org.springblade.thingcom.core.dataBase.controller;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springblade.thingcom.core.dataBase.sevice.DataBaseService;
import org.springblade.thingcom.core.dynamicDatasource.enums.DataSourceTypeEnum;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * @ClassName DataBaseController
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/7/21 10:16
 * @Version 1.0
 **/
@AllArgsConstructor
@RestController
@RequestMapping("/data-base")
public class DataBaseController extends BladeController {

    private final DataBaseService dataBaseService;

    /**
     * 导出数据
     *
     * @param response
     * @param type，1-系统业务数据；2-历史数据
     * @return
     * @see DataSourceTypeEnum
     */
    @SneakyThrows
    @GetMapping("/export-data")
    public void exportData(HttpServletResponse response, Integer type) {
        // 默认导出主库数据
        if (type == null) {
            type = DataSourceTypeEnum.MASTER.getCode();
        }
        dataBaseService.exportData(response, type);
    }

    /**
     * 导入数据
     *
     * @param file 上传的文件
     * @return
     * @see DataSourceTypeEnum
     */
    @PostMapping("/import-data")
    public R importData(MultipartFile file) throws IOException {
        dataBaseService.importData(file);
        return status(true);
    }

    /**
     * 导出 PostgreSQL 数据（使用默认配置）
     *
     * @param response HTTP响应
     */
    @SneakyThrows
    @GetMapping("/export-data/postgresql")
    public void exportDataPostgresSQL(HttpServletResponse response) {
        dataBaseService.exportDataPostgresSQL(response);
    }

    /**
     * 导出 PostgreSQL 数据（自定义配置）
     *
     * @param response HTTP响应
     * @param host 数据库主机
     * @param port 数据库端口
     * @param dbName 数据库名称
     * @param user 用户名
     * @param password 密码
     */
    @SneakyThrows
    @GetMapping("/export-data/postgresql/custom")
    public void exportDataPostgreSQLCustom(HttpServletResponse response,
                                          String host,
                                          String port,
                                          String dbName,
                                          String user,
                                          String password) {
        dataBaseService.exportDataPostgresSQL(response, host, port, dbName, user, password);
    }

    /**
     * 导出 PostgreSQL 数据库结构（仅表结构，不包含数据）
     *
     * @param response HTTP响应
     * @param host 数据库主机
     * @param port 数据库端口
     * @param dbName 数据库名称
     * @param user 用户名
     * @param password 密码
     */
    @SneakyThrows
    @GetMapping("/export-data/postgresql/schema")
    public void exportPostgreSQLSchema(HttpServletResponse response,
                                      String host,
                                      String port,
                                      String dbName,
                                      String user,
                                      String password) {
        dataBaseService.exportPostgreSQLSchema(response, host, port, dbName, user, password);
    }

    /**
     * 导出 PostgreSQL 数据（仅数据，不包含表结构）
     *
     * @param response HTTP响应
     * @param host 数据库主机
     * @param port 数据库端口
     * @param dbName 数据库名称
     * @param user 用户名
     * @param password 密码
     */
    @SneakyThrows
    @GetMapping("/export-data/postgresql/data-only")
    public void exportPostgreSQLDataOnly(HttpServletResponse response,
                                        String host,
                                        String port,
                                        String dbName,
                                        String user,
                                        String password) {
        dataBaseService.exportPostgreSQLDataOnly(response, host, port, dbName, user, password);
    }

    /**
     * 导出 PostgreSQL 指定表
     *
     * @param response HTTP响应
     * @param host 数据库主机
     * @param port 数据库端口
     * @param dbName 数据库名称
     * @param user 用户名
     * @param password 密码
     * @param tables 表名列表，用逗号分隔
     */
    @SneakyThrows
    @GetMapping("/export-data/postgresql/tables")
    public void exportPostgreSQLTables(HttpServletResponse response,
                                      String host,
                                      String port,
                                      String dbName,
                                      String user,
                                      String password,
                                      String tables) {
        if (StrUtil.isBlank(tables)) {
            throw new IllegalArgumentException("表名不能为空");
        }
        String[] tableNames = tables.split(",");
        dataBaseService.exportPostgreSQLTables(response, host, port, dbName, user, password, tableNames);
    }

}

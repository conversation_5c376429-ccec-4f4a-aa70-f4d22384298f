package org.springblade.thingcom.core.dataBase.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.util.List;

/**
 * @ClassName PostgreSQLExportDTO
 * @Description: PostgreSQL导出参数DTO
 * @Author: Augment
 * @Date: 2024/7/25
 * @Version: 1.0
 **/
@Data
public class PostgreSQLExportDTO {

    /**
     * 数据库主机地址
     */
    private String host;

    /**
     * 数据库端口
     */
    @Pattern(regexp = "^[1-9]\\d{0,4}$", message = "端口号格式不正确")
    private String port;

    /**
     * 数据库名称
     */
    @NotBlank(message = "数据库名称不能为空")
    private String dbName;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 导出类型：full-完整导出，schema-仅结构，data-仅数据，tables-指定表
     */
    private ExportType exportType = ExportType.FULL;

    /**
     * 指定表名列表（当exportType为tables时使用）
     */
    private List<String> tableNames;

    /**
     * 是否压缩
     */
    private Boolean compress = false;

    /**
     * 导出格式：plain-纯文本，custom-自定义格式，directory-目录格式，tar-tar格式
     */
    private ExportFormat format = ExportFormat.PLAIN;

    /**
     * 是否包含数据
     */
    private Boolean includeData = true;

    /**
     * 是否包含结构
     */
    private Boolean includeSchema = true;

    /**
     * 是否包含索引
     */
    private Boolean includeIndexes = true;

    /**
     * 是否包含触发器
     */
    private Boolean includeTriggers = true;

    /**
     * 是否包含注释
     */
    private Boolean includeComments = true;

    /**
     * 导出类型枚举
     */
    public enum ExportType {
        FULL("完整导出"),
        SCHEMA("仅结构"),
        DATA("仅数据"),
        TABLES("指定表");

        private final String description;

        ExportType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 导出格式枚举
     */
    public enum ExportFormat {
        PLAIN("p", "纯文本"),
        CUSTOM("c", "自定义格式"),
        DIRECTORY("d", "目录格式"),
        TAR("t", "tar格式");

        private final String code;
        private final String description;

        ExportFormat(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
}

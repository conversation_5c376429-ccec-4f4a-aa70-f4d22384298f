package org.springblade.thingcom.core.dataBase.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.thingcom.core.dataBase.config.PostgreSQLConfig;
import org.springblade.thingcom.core.dataBase.dto.PostgreSQLExportDTO;
import org.springblade.thingcom.core.dataBase.service.PostgreSQLExportService;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.sql.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName PostgreSQLExportServiceImpl
 * @Description: PostgreSQL导出服务实现
 * @Author: Augment
 * @Date: 2024/7/25
 * @Version: 1.0
 **/
@Slf4j
@Service
@AllArgsConstructor
public class PostgreSQLExportServiceImpl implements PostgreSQLExportService {

    private final PostgreSQLConfig postgreSQLConfig;

    @Override
    public void exportDatabase(HttpServletResponse response, PostgreSQLExportDTO exportDTO) {
        // 设置默认值
        setDefaultValues(exportDTO);
        
        log.info("开始导出PostgreSQL数据库: {}:{}/{}, 类型: {}", 
            exportDTO.getHost(), exportDTO.getPort(), exportDTO.getDbName(), exportDTO.getExportType());

        try {
            // 构建pg_dump命令
            String command = buildPgDumpCommand(exportDTO);
            log.debug("执行命令: {}", command);

            ProcessBuilder processBuilder = new ProcessBuilder("cmd.exe", "/c", command);
            processBuilder.environment().put("PGPASSWORD", exportDTO.getPassword());
            processBuilder.redirectErrorStream(true);
            
            Process process = processBuilder.start();

            // 设置响应头
            String fileName = generateFileName(exportDTO);
            
            response.setContentType("application/octet-stream");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));
            response.setHeader("Cache-Control", "no-cache");
            response.setHeader("Pragma", "no-cache");

            // 直接写入响应流
            try (InputStream inputStream = process.getInputStream();
                 OutputStream outputStream = response.getOutputStream()) {

                byte[] buffer = new byte[postgreSQLConfig.getBufferSize()];
                int bytesRead;
                long totalBytes = 0;
                
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                    totalBytes += bytesRead;
                }
                
                outputStream.flush();
                log.info("PostgreSQL数据库导出完成，总大小: {} bytes", totalBytes);
            }

            // 等待进程完成
            boolean finished = process.waitFor(postgreSQLConfig.getTimeoutSeconds(), TimeUnit.SECONDS);
            if (!finished) {
                process.destroyForcibly();
                throw new RuntimeException("导出超时，已强制终止进程");
            }

            int exitCode = process.exitValue();
            if (exitCode != 0) {
                log.error("数据库导出失败，退出码：{}", exitCode);
                throw new RuntimeException("数据库导出失败，退出码：" + exitCode);
            }

        } catch (Exception e) {
            log.error("导出PostgreSQL数据库失败：{}", e.getMessage(), e);
            throw new RuntimeException("导出数据库失败：" + e.getMessage(), e);
        }
    }

    @Override
    public String exportDatabaseToFile(PostgreSQLExportDTO exportDTO, String filePath) {
        setDefaultValues(exportDTO);
        
        log.info("开始导出PostgreSQL数据库到文件: {}", filePath);

        try {
            String command = buildPgDumpCommand(exportDTO) + " > \"" + filePath + "\"";
            
            ProcessBuilder processBuilder = new ProcessBuilder("cmd.exe", "/c", command);
            processBuilder.environment().put("PGPASSWORD", exportDTO.getPassword());
            
            Process process = processBuilder.start();
            
            boolean finished = process.waitFor(postgreSQLConfig.getTimeoutSeconds(), TimeUnit.SECONDS);
            if (!finished) {
                process.destroyForcibly();
                throw new RuntimeException("导出超时");
            }

            int exitCode = process.exitValue();
            if (exitCode != 0) {
                throw new RuntimeException("导出失败，退出码：" + exitCode);
            }

            File file = new File(filePath);
            long fileSize = file.exists() ? file.length() : 0;
            
            log.info("PostgreSQL数据库导出到文件完成，文件大小: {} bytes", fileSize);
            return String.format("导出成功，文件大小: %d bytes", fileSize);

        } catch (Exception e) {
            log.error("导出PostgreSQL数据库到文件失败：{}", e.getMessage(), e);
            throw new RuntimeException("导出失败：" + e.getMessage(), e);
        }
    }

    @Override
    public boolean testConnection(String host, String port, String dbName, String username, String password) {
        String url = String.format("jdbc:postgresql://%s:%s/%s", host, port, dbName);
        
        try (Connection connection = DriverManager.getConnection(url, username, password)) {
            return connection.isValid(5);
        } catch (SQLException e) {
            log.warn("PostgreSQL连接测试失败: {}", e.getMessage());
            return false;
        }
    }

    @Override
    public List<String> getTableList(String host, String port, String dbName, String username, String password) {
        List<String> tables = new ArrayList<>();
        String url = String.format("jdbc:postgresql://%s:%s/%s", host, port, dbName);
        
        String sql = "SELECT table_name FROM information_schema.tables " +
                    "WHERE table_schema = 'public' AND table_type = 'BASE TABLE' " +
                    "ORDER BY table_name";
        
        try (Connection connection = DriverManager.getConnection(url, username, password);
             PreparedStatement statement = connection.prepareStatement(sql);
             ResultSet resultSet = statement.executeQuery()) {
            
            while (resultSet.next()) {
                tables.add(resultSet.getString("table_name"));
            }
            
        } catch (SQLException e) {
            log.error("获取表列表失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取表列表失败：" + e.getMessage(), e);
        }
        
        return tables;
    }

    @Override
    public long getDatabaseSize(String host, String port, String dbName, String username, String password) {
        String url = String.format("jdbc:postgresql://%s:%s/%s", host, port, dbName);
        
        String sql = "SELECT pg_database_size(?) as size";
        
        try (Connection connection = DriverManager.getConnection(url, username, password);
             PreparedStatement statement = connection.prepareStatement(sql)) {
            
            statement.setString(1, dbName);
            
            try (ResultSet resultSet = statement.executeQuery()) {
                if (resultSet.next()) {
                    return resultSet.getLong("size");
                }
            }
            
        } catch (SQLException e) {
            log.error("获取数据库大小失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取数据库大小失败：" + e.getMessage(), e);
        }
        
        return 0;
    }

    /**
     * 设置默认值
     */
    private void setDefaultValues(PostgreSQLExportDTO exportDTO) {
        if (StrUtil.isBlank(exportDTO.getHost())) {
            exportDTO.setHost(postgreSQLConfig.getHost());
        }
        if (StrUtil.isBlank(exportDTO.getPort())) {
            exportDTO.setPort(postgreSQLConfig.getPort());
        }
        if (StrUtil.isBlank(exportDTO.getUsername())) {
            exportDTO.setUsername(postgreSQLConfig.getUsername());
        }
        if (StrUtil.isBlank(exportDTO.getPassword())) {
            exportDTO.setPassword(postgreSQLConfig.getPassword());
        }
        if (exportDTO.getCompress() == null) {
            exportDTO.setCompress(postgreSQLConfig.isCompress());
        }
    }

    /**
     * 构建pg_dump命令
     */
    private String buildPgDumpCommand(PostgreSQLExportDTO exportDTO) {
        StringBuilder command = new StringBuilder();
        
        command.append(postgreSQLConfig.getPgDumpPath())
               .append(" -h ").append(exportDTO.getHost())
               .append(" -p ").append(exportDTO.getPort())
               .append(" -U ").append(exportDTO.getUsername())
               .append(" -d ").append(exportDTO.getDbName())
               .append(" --format=").append(exportDTO.getFormat().getCode())
               .append(" --no-password");

        if (postgreSQLConfig.isVerbose()) {
            command.append(" --verbose");
        }

        // 根据导出类型添加选项
        switch (exportDTO.getExportType()) {
            case SCHEMA:
                command.append(" --schema-only");
                break;
            case DATA:
                command.append(" --data-only");
                break;
            case TABLES:
                if (CollUtil.isNotEmpty(exportDTO.getTableNames())) {
                    for (String tableName : exportDTO.getTableNames()) {
                        command.append(" --table=").append(tableName);
                    }
                }
                break;
            case FULL:
            default:
                // 完整导出，不需要额外参数
                break;
        }

        // 其他选项
        if (!exportDTO.getIncludeIndexes()) {
            command.append(" --no-indexes");
        }
        if (!exportDTO.getIncludeTriggers()) {
            command.append(" --no-triggers");
        }
        if (!exportDTO.getIncludeComments()) {
            command.append(" --no-comments");
        }
        if (exportDTO.getCompress()) {
            command.append(" --compress=").append(postgreSQLConfig.getCompressLevel());
        }

        return command.toString();
    }

    /**
     * 生成文件名
     */
    private String generateFileName(PostgreSQLExportDTO exportDTO) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String suffix = exportDTO.getExportType().name().toLowerCase();
        
        String extension = ".sql";
        if (exportDTO.getFormat() == PostgreSQLExportDTO.ExportFormat.CUSTOM) {
            extension = ".backup";
        } else if (exportDTO.getFormat() == PostgreSQLExportDTO.ExportFormat.TAR) {
            extension = ".tar";
        }
        
        return String.format("%s_%s_%s%s", exportDTO.getDbName(), suffix, timestamp, extension);
    }
}

package org.springblade.thingcom.core.dataBase.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName PostgreSQLConfig
 * @Description: PostgreSQL数据库配置
 * @Author: Augment
 * @Date: 2024/7/25
 * @Version: 1.0
 **/
@Data
@Configuration
@ConfigurationProperties(prefix = "postgresql.export")
public class PostgreSQLConfig {

    /**
     * 数据库主机地址
     */
    private String host = "localhost";

    /**
     * 数据库端口
     */
    private String port = "5432";

    /**
     * 数据库名称
     */
    private String dbName = "loraserver_ns";

    /**
     * 用户名
     */
    private String username = "postgres";

    /**
     * 密码
     */
    private String password = "postgres";

    /**
     * pg_dump可执行文件路径（Windows环境）
     */
    private String pgDumpPath = "pg_dump";

    /**
     * 导出超时时间（秒）
     */
    private int timeoutSeconds = 300;

    /**
     * 缓冲区大小
     */
    private int bufferSize = 8192;

    /**
     * 是否启用详细输出
     */
    private boolean verbose = true;

    /**
     * 默认导出格式
     */
    private String format = "p"; // p=plain text, c=custom, d=directory, t=tar

    /**
     * 是否压缩输出
     */
    private boolean compress = false;

    /**
     * 压缩级别（0-9）
     */
    private int compressLevel = 6;
}

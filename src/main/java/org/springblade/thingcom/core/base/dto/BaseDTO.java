package org.springblade.thingcom.core.base.dto;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @ClassName BaseDTO
 * @Description: 基础入参类
 * @Author: zgq
 * @Date: 2023/7/21 10:27
 * @Version: 1.0
 **/
@Data
public class BaseDTO implements Serializable {
    private static final long serialVersionUID = -974883657898421556L;

    /**
     * 国家语言
     * @see org.springblade.thingcom.translate.enums.LanguageTypeEnum
     */
    private String lang;

    /**
     * 关键字模糊搜素
     */
    private String searchKey;

    /**
     * 日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate date;

    /**
     * 时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime time;

    /**
     * 开始日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate beginDate;

    /**
     * 结束日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    // 兼容 yyyy-MM-dd HH:mm:ss 格式 < endDate，因此需要加一天
    public LocalDate getEndDate() {
        return endDate == null ? null : endDate.plusDays(1);
    }

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime beginTime;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
}

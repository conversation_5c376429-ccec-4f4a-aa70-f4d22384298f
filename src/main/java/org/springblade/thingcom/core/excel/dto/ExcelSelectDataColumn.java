package org.springblade.thingcom.core.excel.dto;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Data;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.thingcom.core.excel.annotation.ExcelSelect;
import org.springblade.thingcom.core.excel.handler.ColumnDynamicSelectDataHandler;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/11/7
 */
@Data
public class ExcelSelectDataColumn<T> {

    private T source;

    private String column;

    private int columnIndex;

    private String parentColumn;

    private int[] parentColumnIndex;

    private int firstRow;

    private int lastRow;

    public T resolveSource(ExcelSelect excelSelect, Object param) {
        if (excelSelect == null) {
            return null;
        }
        // 获取固定下拉框的内容
        final String[] staticData = excelSelect.staticData();
        if (ArrayUtil.isNotEmpty(staticData)) {
            return (T) Arrays.asList(staticData);
        }
        // 获取动态下拉框的内容
        final Class<? extends ColumnDynamicSelectDataHandler> handlerClass = excelSelect.handler();
        if (Objects.nonNull(handlerClass)) {
            final ColumnDynamicSelectDataHandler handler = SpringUtil.getBean(handlerClass);
            return (T) handler.source(param).apply(StrUtil.isNotEmpty(excelSelect.parameter()) ? excelSelect.parameter() : null);
        }
        return null;
    }
}

package org.springblade.thingcom.core.chirpStack.dto;

import lombok.Data;

/**
 * @ClassName ChirpStackDownLinkDTO
 * @Description: ChirpStack 下行数据传输对象
 * @Author: zgq
 * @Date: 2024/3/21 9:49
 * @Version: 1.0
 **/
@Data
public class DownLinkDTO implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 可以填写任意值
     */
    private String referece = "rimelink";

    /**
     * 非确认帧，不需要节点回应 ACK
     */
    private Boolean confirmed = false;

    /**
     * LoRaWAN端口号
     */
    private Integer fPort = 100;

    /**
     * 下行数据的 Base64 编码
     */
    private String data;
}

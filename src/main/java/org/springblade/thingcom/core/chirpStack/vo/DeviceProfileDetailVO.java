package org.springblade.thingcom.core.chirpStack.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName DeviceProfileDetailVO
 * @Description: DeviceProfileDetailVO
 * @Author: zgq
 * @Date: 2024/6/5 20:15
 * @Version: 1.0
 **/
@Data
public class DeviceProfileDetailVO implements Serializable {
    private static final long serialVersionUID = 3369920708028831930L;

    /**
     * createdAt
     */
    private String createdAt;
    /**
     * updatedAt
     */
    private String updatedAt;
    /**
     * deviceProfile
     */
    private DeviceProfile deviceProfile;

    /**
     * DeviceProfile
     */
    @Data
    public static class DeviceProfile {
        /**
         * id
         */
        private String id;
        /**
         * name
         */
        private String name;
        /**
         * organizationID
         */
        private String organizationID;
        /**
         * networkServerID
         */
        private String networkServerID;
        /**
         * supportsClassB
         */
        private Boolean supportsClassB;
        /**
         * classBTimeout
         */
        private Integer classBTimeout;
        /**
         * pingSlotPeriod
         */
        private Integer pingSlotPeriod;
        /**
         * pingSlotDR
         */
        private Integer pingSlotDR;
        /**
         * pingSlotFreq
         */
        private Integer pingSlotFreq;
        /**
         * supportsClassC
         */
        private Boolean supportsClassC;
        /**
         * classCTimeout
         */
        private Integer classCTimeout;
        /**
         * macVersion
         */
        private String macVersion;
        /**
         * regParamsRevision
         */
        private String regParamsRevision;
        /**
         * rxDelay1
         */
        private Integer rxDelay1;
        /**
         * rxDROffset1
         */
        private Integer rxDROffset1;
        /**
         * rxDataRate2
         */
        private Integer rxDataRate2;
        /**
         * rxFreq2
         */
        private Integer rxFreq2;
        /**
         * factoryPresetFreqs
         */
        private List<Integer> factoryPresetFreqs;
        /**
         * maxEIRP
         */
        private Integer maxEIRP;
        /**
         * maxDutyCycle
         */
        private Integer maxDutyCycle;
        /**
         * supportsJoin
         */
        private Boolean supportsJoin;
        /**
         * rfRegion
         */
        private String rfRegion;
        /**
         * supports32BitFCnt
         */
        private Boolean supports32BitFCnt;
        /**
         * payloadCodec
         */
        private String payloadCodec;
        /**
         * payloadEncoderScript
         */
        private String payloadEncoderScript;
        /**
         * payloadDecoderScript
         */
        private String payloadDecoderScript;
        /**
         * geolocBufferTTL
         */
        private Integer geolocBufferTTL;
        /**
         * geolocMinBufferSize
         */
        private Integer geolocMinBufferSize;
        /**
         * uplinkInterval
         */
        private String uplinkInterval;
        /**
         * adrAlgorithmID
         */
        private String adrAlgorithmID;

        /**
         * tags
         */
        //private Tags tags;
        /**
         * Tags
         */
        //@Data
        //public static class Tags {
        //}
    }
}

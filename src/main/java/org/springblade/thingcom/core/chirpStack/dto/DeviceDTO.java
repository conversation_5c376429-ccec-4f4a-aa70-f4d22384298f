package org.springblade.thingcom.core.chirpStack.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @ClassName DeviceDTO
 * @Description: 设备DTO
 * @Author: zgq
 * @Date: 2024/4/15 11:32
 * @Version: 1.0
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class DeviceDTO extends BaseDTO {
    private static final long serialVersionUID = -1873619288218467641L;

    /**
     * device
     */
    private Device device;

    /**
     * Device
     */
    @Data
    @NoArgsConstructor
    public static class Device {
        /**
         * applicationID
         */
        private Long applicationID;
        /**
         * description
         */
        private String description;
        /**
         * devEUI
         */
        private String devEUI;
        /**
         * deviceProfileID
         */
        private String deviceProfileID;
        /**
         * isDisabled
         */
        private Boolean isDisabled = false;
        /**
         * name
         */
        private String name;
        /**
         * referenceAltitude
         */
        private Integer referenceAltitude;
        /**
         * skipFCntCheck
         */
        private Boolean skipFCntCheck = true;
        /**
         * tags
         */
        private Tags tags;
        /**
         * variables
         */
        private Variables variables;

        /**
         * Tags
         */
        @Data
        @NoArgsConstructor
        public static class Tags {
        }

        /**
         * Variables
         */
        @Data
        @NoArgsConstructor
        public static class Variables {
        }
    }
}

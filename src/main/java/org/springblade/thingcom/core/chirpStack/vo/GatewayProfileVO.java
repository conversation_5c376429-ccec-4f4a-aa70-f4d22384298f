package org.springblade.thingcom.core.chirpStack.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024-10-09 16:54
 */
@Data
public class GatewayProfileVO implements Serializable {
    private static final long serialVersionUID = -1L;
    /**
     * id
     */
    private String id;
    /**
     * name
     */
    private String name;
    /**
     * networkServerID
     */
    private String networkServerID;
    /**
     * networkServerName
     */
    private String networkServerName;
    /**
     * createdAt
     */
    private String createdAt;
    /**
     * updatedAt
     */
    private String updatedAt;
}

package org.springblade.thingcom.core.chirpStack.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.thingcom.core.chirpStack.cache.CommonCache;
import org.springblade.thingcom.core.chirpStack.constant.ApiConstant;
import org.springblade.thingcom.core.chirpStack.constant.ParamConstant;
import org.springblade.thingcom.core.chirpStack.constant.ResultConstant;
import org.springblade.thingcom.core.chirpStack.dto.ApplicationDTO;
import org.springblade.thingcom.core.chirpStack.dto.DeviceActivationDTO;
import org.springblade.thingcom.core.chirpStack.dto.DeviceDTO;
import org.springblade.thingcom.core.chirpStack.dto.DeviceProfileDTO;
import org.springblade.thingcom.core.chirpStack.enums.SaveDeviceResultEnum;
import org.springblade.thingcom.core.chirpStack.service.ApplicationService;
import org.springblade.thingcom.core.chirpStack.service.DeviceProfileService;
import org.springblade.thingcom.core.chirpStack.service.DeviceService;
import org.springblade.thingcom.core.chirpStack.utils.ApiUtil;
import org.springblade.thingcom.core.chirpStack.vo.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * @ClassName DeviceServiceImpl
 * @Description: chirpStack Device API
 * @Author: zgq
 * @Date: 2024/4/15 11:30
 * @Version: 1.0
 **/
@Slf4j
@Service
@AllArgsConstructor
public class DeviceServiceImpl implements DeviceService {

    private final ApplicationService applicationService;
    private final DeviceProfileService deviceProfileService;

    @Override
    public boolean saveData(DeviceDTO.Device device) {
        DeviceDTO dto = new DeviceDTO();
        String jwt = dto.getJwt();
        if (StrUtil.isBlank(jwt)) {
            // 获取 token
            InternalLoginVO loginVo = CommonCache.getToken();
            jwt = loginVo.getJwt();
        }
        // 获取应用
        ApplicationDTO applicationDto = new ApplicationDTO();
        applicationDto.setJwt(jwt);
        PageVO<ApplicationVO> applicationPage = applicationService.page(applicationDto);
        if (Objects.nonNull(applicationPage) && CollUtil.isNotEmpty(applicationPage.getResult())) {
            // 设置 applicationID（默认使用第一个）
            Long applicationId = applicationPage.getResult().get(0).getId();
            device.setApplicationID(applicationId);
        } else {
            log.error("获取应用失败：{}", JSONUtil.toJsonStr(applicationPage));
            return false;
        }
        // 获取 deviceProfile
        DeviceProfileDTO deviceProfileDto = new DeviceProfileDTO();
        deviceProfileDto.setJwt(jwt);
        PageVO<DeviceProfileVO> deviceProfilePage = deviceProfileService.page(deviceProfileDto);
        if (Objects.nonNull(deviceProfilePage) && CollUtil.isNotEmpty(deviceProfilePage.getResult())) {
            // 通过 deviceProfileID 查询支持 ClassC 的 deviceProfile
            List<DeviceProfileVO> deviceProfileList = deviceProfilePage.getResult();
            for (DeviceProfileVO deviceProfileVO : deviceProfileList) {
                // 通过 deviceProfileID 查询支持 ClassC 的 deviceProfile
                //deviceProfileDto.setId(deviceProfileVO.getId());
                //DeviceProfileDetailVO deviceProfileDetail = deviceProfileService.getById(deviceProfileDto);
                //if (Objects.nonNull(deviceProfileDetail) && Objects.nonNull(deviceProfileDetail.getDeviceProfile())
                //        && deviceProfileDetail.getDeviceProfile().getSupportsClassC()) {
                //    device.setDeviceProfileID(deviceProfileDetail.getDeviceProfile().getId());
                //    break;
                //}
                //deviceProfileDto.setId(deviceProfileVO.getId());
                //DeviceProfileDetailVO deviceProfileDetail = deviceProfileService.getById(deviceProfileDto);
                // 通过名称匹配
                if ("deviceprofileABP_C".equals(deviceProfileVO.getName())) {
                    device.setDeviceProfileID(deviceProfileVO.getId());
                    break;
                }
            }
        }
        if (StrUtil.isBlank(device.getDeviceProfileID())) {
            log.error("获取 deviceProfile 失败：{}", JSONUtil.toJsonStr(deviceProfilePage));
            return false;
        }
        dto.setDevice(device);
        // 获取 url
        String apiHost = ApiUtil.getApiHost();
        String apiUrl = apiHost + ApiConstant.DEVICE;
        int apiTimeout = CommonConstant.API_TIMEOUT;
        // 创建请求，设置 header，超时时间
        HttpRequest post = HttpUtil.createPost(apiUrl);
        post.header(ParamConstant.HEADER_AUTHORIZATION, jwt);
        post.setConnectionTimeout(apiTimeout);
        post.setReadTimeout(apiTimeout);
        // 设置请求参数，发送请求，获取返回结果
        String ret = post.body(JSONUtil.toJsonStr(dto)).execute().body();
        if (ResultConstant.SUCCESS.equals(ret)) {
            return true;
        } else {
            log.error("保存设备失败：请求参数：{}，返回结果：{}", JSONUtil.toJsonStr(dto), ret);
            JSONObject jsonObject = JSONUtil.parseObj(ret);
            String msg = SaveDeviceResultEnum.translationValue(jsonObject.getInt(ResultConstant.CODE));
            Assert.isTrue(false, msg);
        }
        return false;
    }

    @Override
    public boolean deleteData(String eui) {
        // 获取 token
        InternalLoginVO loginVo = CommonCache.getToken();
        String jwt = loginVo.getJwt();
        // 获取 url
        String apiHost = ApiUtil.getApiHost();
        String apiUrl = apiHost + ApiConstant.DEVICE + StrUtil.SLASH + eui;
        int apiTimeout = CommonConstant.API_TIMEOUT;
        // 创建请求，设置 header，超时时间
        HttpRequest delete = HttpRequest.delete(apiUrl);
        delete.header(ParamConstant.HEADER_AUTHORIZATION, jwt);
        delete.setConnectionTimeout(apiTimeout);
        delete.setReadTimeout(apiTimeout);
        // 发送请求，获取返回结果
        String ret = delete.execute().body();
        if (ResultConstant.SUCCESS.equals(ret)) {
            return true;
        } else {
            JSONObject jsonObject = JSONUtil.parseObj(ret);
            Assert.isTrue(false, jsonObject.getStr(ResultConstant.ERROR));
        }
        return false;
    }

    @Override
    public DeviceActivationVO getActivationData(String eui) {
        // 获取 token
        InternalLoginVO loginVo = CommonCache.getToken();
        String jwt = loginVo.getJwt();
        // 获取 url
        String apiHost = ApiUtil.getApiHost();
        String apiUrl = apiHost + StrUtil.format(ApiConstant.DEVICE_ACTIVATION, eui);
        int apiTimeout = CommonConstant.API_TIMEOUT;
        try {
            // 创建请求，设置 header，超时时间
            HttpRequest get = HttpUtil.createGet(apiUrl);
            get.header(ParamConstant.HEADER_AUTHORIZATION, jwt);
            get.setConnectionTimeout(apiTimeout);
            get.setReadTimeout(apiTimeout);
            // 发送请求，获取返回结果
            String ret = get.execute().body();
            return JSONUtil.toBean(ret, new TypeReference<DeviceActivationVO>() {
            }, false);
        } catch (Exception e) {
            log.error("get device activation error", e);
            return null;
        }
    }

    @Override
    public boolean saveActivationData(DeviceActivationDTO.DeviceActivation deviceActivation) {
        DeviceActivationDTO dto = new DeviceActivationDTO();
        String jwt = dto.getJwt();
        if (StrUtil.isBlank(jwt)) {
            // 获取 token
            InternalLoginVO loginVo = CommonCache.getToken();
            jwt = loginVo.getJwt();
        }
        // 设备EUI
        String devEui = deviceActivation.getDevEUI();
        // devEUI 最小长度
        int euiMinLength = ParamConstant.EUI_MIN_LENGTH;
        // 校验 devEUI
        Assert.notBlank(devEui, "devEUI is null");
        Assert.isTrue(devEui.length() >= euiMinLength, "devEUI length is less than 8");
        // 设置 devAddr，eui 的后8位
        String devAddr = devEui.substring(devEui.length() - euiMinLength);
        deviceActivation.setDevAddr(devAddr);
        // 获取 sessionKey
        String sessionKey = CommonCache.getSessionKey();
        deviceActivation.setAppSKey(sessionKey);
        deviceActivation.setFNwkSIntKey(sessionKey);
        deviceActivation.setNwkSEncKey(sessionKey);
        deviceActivation.setSNwkSIntKey(sessionKey);
        dto.setDeviceActivation(deviceActivation);
        // 获取 url
        String apiHost = ApiUtil.getApiHost();
        String apiUrl = apiHost + StrUtil.format(ApiConstant.DEVICE_ACTIVATE, devEui);
        int apiTimeout = CommonConstant.API_TIMEOUT;
        // 创建请求，设置 header，超时时间
        HttpRequest post = HttpUtil.createPost(apiUrl);
        post.header(ParamConstant.HEADER_AUTHORIZATION, jwt);
        post.setConnectionTimeout(apiTimeout);
        post.setReadTimeout(apiTimeout);
        // 设置请求参数，发送请求，获取返回结果
        String ret = post.body(JSONUtil.toJsonStr(dto)).execute().body();
        if (ResultConstant.SUCCESS.equals(ret)) {
            return true;
        } else {
            JSONObject jsonObject = JSONUtil.parseObj(ret);
            Assert.isTrue(false, jsonObject.getStr(ResultConstant.ERROR));
        }
        return false;
    }

    @Async
    @Override
    public void validateActivationDataAndUpdate(String eui) {
        // 根据 eui 获取激活数据
        DeviceActivationVO vo = getActivationData(eui);
        if (Objects.isNull(vo)) {
            // 激活设备
            DeviceActivationDTO.DeviceActivation entity = new DeviceActivationDTO.DeviceActivation();
            entity.setDevEUI(eui);
            saveActivationData(entity);
        } else {
            // 判断激活信息是否正确
            DeviceActivationVO.DeviceActivation entity = vo.getDeviceActivation();
            if (Objects.isNull(entity)) {
                // 激活设备
                DeviceActivationDTO.DeviceActivation dto = new DeviceActivationDTO.DeviceActivation();
                dto.setDevEUI(eui);
                saveActivationData(dto);
            } else {
                String appSKey = entity.getAppSKey();
                String nwkSEncKey = entity.getNwkSEncKey();
                // 获取 sessionKey
                String sessionKey = CommonCache.getSessionKey();
                if (!appSKey.equals(sessionKey) || !nwkSEncKey.equals(sessionKey)) {
                    // 更新激活信息
                    DeviceActivationDTO.DeviceActivation dto = new DeviceActivationDTO.DeviceActivation();
                    dto.setDevEUI(eui);
                    saveActivationData(dto);
                }
            }
        }
    }

}

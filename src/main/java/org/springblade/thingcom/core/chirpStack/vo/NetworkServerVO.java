package org.springblade.thingcom.core.chirpStack.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName NetworkServerVO
 * @Description: NetworkServerVO
 * @Author: zgq
 * @Date: 2024/4/20 17:26
 * @Version: 1.0
 **/
@Data
public class NetworkServerVO implements Serializable {
    private static final long serialVersionUID = -5202429745238878404L;

    /**
     * id
     */
    private String id;
    /**
     * name
     */
    private String name;
    /**
     * server
     */
    private String server;
    /**
     * createdAt
     */
    private String createdAt;
    /**
     * updatedAt
     */
    private String updatedAt;
}

package org.springblade.thingcom.core.chirpStack.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.thingcom.core.chirpStack.cache.CommonCache;
import org.springblade.thingcom.core.chirpStack.constant.ApiConstant;
import org.springblade.thingcom.core.chirpStack.constant.ParamConstant;
import org.springblade.thingcom.core.chirpStack.dto.DeviceProfileDTO;
import org.springblade.thingcom.core.chirpStack.dto.NetworkServerDTO;
import org.springblade.thingcom.core.chirpStack.dto.OrganizationDTO;
import org.springblade.thingcom.core.chirpStack.service.DeviceProfileService;
import org.springblade.thingcom.core.chirpStack.service.NetworkServerService;
import org.springblade.thingcom.core.chirpStack.service.OrganizationService;
import org.springblade.thingcom.core.chirpStack.utils.ApiUtil;
import org.springblade.thingcom.core.chirpStack.vo.*;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName DeviceProfileServiceImpl
 * @Description: DeviceProfileService
 * @Author: zgq
 * @Date: 2024/4/15 17:24
 * @Version: 1.0
 **/
@Slf4j
@Service
@AllArgsConstructor
public class DeviceProfileServiceImpl implements DeviceProfileService {

    private final NetworkServerService networkServerService;
    private final OrganizationService organizationService;

    private static final Map<String, String> deviceMap = new HashMap<>();

    // 初始化控制码映射关系
    static {
        deviceMap.put("3b0623c1-488a-4b68-a317-55907eeb50d3", "deviceprofileABP_C");
        deviceMap.put("9de8773a-21df-4ea0-849c-fca9f595ab40", "deviceprofileOTAA");
        deviceMap.put("3f63b727-5a0d-462d-9db3-64361d946c58", "deviceprofileOTAA_C");
        deviceMap.put("af1cac59-8eb5-4291-8b94-f87e753c7269", "rimelink_demo");
    }

    @Override
    public PageVO<DeviceProfileVO> page(DeviceProfileDTO dto) {
        String jwt = dto.getJwt();
        if (StrUtil.isBlank(jwt)) {
            // 获取 token
            InternalLoginVO loginVo = CommonCache.getToken();
            jwt = loginVo.getJwt();
        }
        // 获取 url
        String apiHost = ApiUtil.getApiHost();
        String apiUrl = apiHost + ApiConstant.DEVICE_PROFILE;
        int apiTimeout = CommonConstant.API_TIMEOUT;
        try {
            // 创建请求，设置 header，超时时间
            HttpRequest get = HttpUtil.createGet(apiUrl);
            get.header(ParamConstant.HEADER_AUTHORIZATION, jwt);
            get.setConnectionTimeout(apiTimeout);
            get.setReadTimeout(apiTimeout);
            // 设置请求参数
            Map<String, Object> formMap = BeanUtil.beanToMap(dto, false, true);
            // 发送请求，获取返回结果
            String ret = get.form(formMap).execute().body();
            return JSONUtil.toBean(ret, new TypeReference<PageVO<DeviceProfileVO>>() {
            }, false);
        } catch (Exception e) {
            log.error("page deviceProfile error", e);
            return null;
        }
    }

    @Override
    public DeviceProfileDetailVO getById(DeviceProfileDTO dto) {
        String jwt = dto.getJwt();
        if (StrUtil.isBlank(jwt)) {
            // 获取 token
            InternalLoginVO loginVo = CommonCache.getToken();
            jwt = loginVo.getJwt();
        }
        // 获取 url
        String apiHost = ApiUtil.getApiHost();
        String apiUrl = apiHost + ApiConstant.DEVICE_PROFILE + "/" + dto.getDeviceProfile().getId();
        int apiTimeout = CommonConstant.API_TIMEOUT;
        try {
            // 创建请求，设置 header，超时时间
            HttpRequest get = HttpUtil.createGet(apiUrl);
            get.header(ParamConstant.HEADER_AUTHORIZATION, jwt);
            get.setConnectionTimeout(apiTimeout);
            get.setReadTimeout(apiTimeout);
            // 发送请求，获取返回结果
            String ret = get.execute().body();
            return JSONUtil.toBean(ret, DeviceProfileDetailVO.class);
        } catch (Exception e) {
            log.error("getById deviceProfile error", e);
            return null;
        }
    }

    @Override
    public boolean saveData(DeviceProfileDTO.DeviceProfile deviceProfile) {
        DeviceProfileDTO dto = new DeviceProfileDTO();
        // 查询数据
        PageVO<DeviceProfileVO> deviceProfileVOPageVO = page(dto);
        // 如果没有数据，则新增数据
        if (CollUtil.isEmpty(deviceProfileVOPageVO.getResult())) {
            String jwt = dto.getJwt();
            if (StrUtil.isBlank(jwt)) {
                // 获取 token
                InternalLoginVO loginVo = CommonCache.getToken();
                jwt = loginVo.getJwt();
            }
            // 获取网络服务器信息
            NetworkServerDTO networkServer = new NetworkServerDTO();
            networkServer.setJwt(jwt);
            PageVO<NetworkServerVO> networkServerVOPageVO = networkServerService.page(networkServer);
            if (CollUtil.isNotEmpty(networkServerVOPageVO.getResult())) {
                deviceProfile.setNetworkServerID(networkServerVOPageVO.getResult().get(0).getId());
            } else {
                log.error("获取 NetworkServer 失败：{}", JSONUtil.toJsonStr(networkServerVOPageVO));
                return false;
            }
            // 获取组织信息
            OrganizationDTO organization = new OrganizationDTO();
            organization.setJwt(jwt);
            PageVO<OrganizationVO> organizationVOPageVO = organizationService.page(organization);
            if (CollUtil.isNotEmpty(organizationVOPageVO.getResult())) {
                deviceProfile.setOrganizationID(organizationVOPageVO.getResult().get(0).getId());
            } else {
                log.error("获取 Organization 失败：{}", JSONUtil.toJsonStr(organizationVOPageVO));
                return false;
            }
            dto.setDeviceProfile(deviceProfile);
            dto.setLimit(null);
            dto.setOffset(null);
            // 获取 url
            String apiHost = ApiUtil.getApiHost();
            String apiUrl = apiHost + ApiConstant.DEVICE_PROFILE;
            int apiTimeout = CommonConstant.API_TIMEOUT;
            // 创建请求，设置 header，超时时间
            HttpRequest post = HttpUtil.createPost(apiUrl);
            post.header(ParamConstant.HEADER_AUTHORIZATION, jwt);
            post.setConnectionTimeout(apiTimeout);
            post.setReadTimeout(apiTimeout);
            // 新增四台设备
            for (Map.Entry<String, String> entry : deviceMap.entrySet()) {
                dto.getDeviceProfile().setId(entry.getKey());
                dto.getDeviceProfile().setName(entry.getValue());
                // 设置请求参数，发送请求，获取返回结果
                String ret = post.body(JSONUtil.toJsonStr(dto)).execute().body();
                JSONObject json = JSONUtil.parseObj(ret);
                if (StrUtil.isBlank(json.getStr("id"))) {
                    log.error("保存 DeviceProfile 失败：请求参数：{}，返回结果：{}", JSONUtil.toJsonStr(dto), ret);
                    return false;
                }
            }
            return true;
        }
        return false;
    }

}

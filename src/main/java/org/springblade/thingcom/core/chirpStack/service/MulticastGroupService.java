package org.springblade.thingcom.core.chirpStack.service;

import org.springblade.thingcom.core.chirpStack.dto.MulticasGroupDeviceDTO;
import org.springblade.thingcom.core.chirpStack.dto.MulticastGroupDTO;
import org.springblade.thingcom.core.chirpStack.dto.MulticastQueueItemDTO;
import org.springblade.thingcom.core.chirpStack.vo.MulticastGroupDetailVO;
import org.springblade.thingcom.core.chirpStack.vo.MulticastGroupVO;
import org.springblade.thingcom.core.chirpStack.vo.PageVO;

/**
 * 组播服务接口
 */
public interface MulticastGroupService {

    /**
     * 保存组播信息
     *
     * @param multicastGroup
     * @return boolean
     */
    boolean saveData(MulticastGroupDTO.MulticastGroup multicastGroup);

    /**
     * 分页查询 MulticastGroup
     *
     * @param dto
     * @return PageVO<MulticastGroupVO>
     */
    PageVO<MulticastGroupVO> page(MulticastGroupDTO dto);

    /**
     * 根据id查询组播信息
     *
     * @param id
     * @return MulticastGroupDetailVO
     */
    MulticastGroupDetailVO getById(String id);

    /**
     * 添加设备到组播
     *
     * @param dto 组播设备参数
     * @return 组播设备结果
     */
    MulticastGroupDetailVO insertDeviceToMulticastGroup(MulticasGroupDeviceDTO dto);

    /**
     * 删除设备组播
     *
     * @param dto 组播设备参数
     * @return 组播设备结果
     */
    boolean removeDeviceFromMulticastGroup(MulticasGroupDeviceDTO dto);

    /**
     * 发送节点组播请求
     *
     * @param dto 组播队列参数
     * @return 组播队列结果
     */
    boolean itemMulticastGroup(MulticastQueueItemDTO dto);
}

package org.springblade.thingcom.core.chirpStack.service;

import org.springblade.thingcom.core.chirpStack.dto.OrganizationDTO;
import org.springblade.thingcom.core.chirpStack.vo.OrganizationVO;
import org.springblade.thingcom.core.chirpStack.vo.PageVO;

/**
 * 组织服务接口
 */
public interface OrganizationService {

    /**
     * 分页查询组织
     *
     * @param dto
     * @return PageVO<OrganizationVO>
     */
    PageVO<OrganizationVO> page(OrganizationDTO dto);

    /**
     * 新增 organization
     *
     * @param organization
     * @return
     */
    boolean saveData(OrganizationDTO.Organization organization);
}

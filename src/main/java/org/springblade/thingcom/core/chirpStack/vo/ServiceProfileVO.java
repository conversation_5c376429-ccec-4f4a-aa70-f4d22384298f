package org.springblade.thingcom.core.chirpStack.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024-10-09 15:37
 */
@Data
public class ServiceProfileVO implements Serializable {
    private static final long serialVersionUID = -1L;

    /**
     * id
     */
    private String id;
    /**
     * name
     */
    private String name;
    /**
     * organizationID
     */
    private String organizationID;
    /**
     * networkServerID
     */
    private String networkServerID;
    /**
     * networkServerName
     */
    private String networkServerName;
    /**
     * createdAt
     */
    private String createdAt;
    /**
     * updatedAt
     */
    private String updatedAt;
}

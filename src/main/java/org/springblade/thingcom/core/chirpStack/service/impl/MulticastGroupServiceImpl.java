package org.springblade.thingcom.core.chirpStack.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.thingcom.core.chirpStack.cache.CommonCache;
import org.springblade.thingcom.core.chirpStack.constant.ApiConstant;
import org.springblade.thingcom.core.chirpStack.constant.ParamConstant;
import org.springblade.thingcom.core.chirpStack.constant.ResultConstant;
import org.springblade.thingcom.core.chirpStack.dto.MulticasGroupDeviceDTO;
import org.springblade.thingcom.core.chirpStack.dto.MulticastGroupDTO;
import org.springblade.thingcom.core.chirpStack.dto.MulticastQueueItemDTO;
import org.springblade.thingcom.core.chirpStack.dto.ServiceProfileDTO;
import org.springblade.thingcom.core.chirpStack.service.MulticastGroupService;
import org.springblade.thingcom.core.chirpStack.service.ServiceProfileService;
import org.springblade.thingcom.core.chirpStack.utils.ApiUtil;
import org.springblade.thingcom.core.chirpStack.vo.*;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

/**
 * @ClassName MulticastGroupServiceImpl
 * @Description: TODO
 * @Author: zgq
 * @Date: 2024/7/2 10:54
 * @Version: 1.0
 **/
@Slf4j
@Service
@AllArgsConstructor
public class MulticastGroupServiceImpl implements MulticastGroupService {
    private final ServiceProfileService serviceProfileService;

    @Override
    public boolean saveData(MulticastGroupDTO.MulticastGroup multicastGroup) {
        MulticastGroupDTO dto = new MulticastGroupDTO();
        // 查询数据
        PageVO<MulticastGroupVO> multicastGroupVOPageVO = page(dto);
        // 如果存在数据，则更新数据
        if (CollUtil.isEmpty(multicastGroupVOPageVO.getResult())) {
            String jwt = dto.getJwt();
            if (StrUtil.isBlank(jwt)) {
                // 获取 token
                InternalLoginVO loginVo = CommonCache.getToken();
                jwt = loginVo.getJwt();
            }
            // 获取服务配置数据
            ServiceProfileDTO serviceProfileDTO = new ServiceProfileDTO();
            serviceProfileDTO.setJwt(jwt);
            PageVO<ServiceProfileVO> serviceProfileVOPageVO = serviceProfileService.page(serviceProfileDTO);
            if (CollUtil.isNotEmpty(serviceProfileVOPageVO.getResult())) {
                multicastGroup.setServiceProfileID(serviceProfileVOPageVO.getResult().get(0).getId());
            } else {
                log.error("获取 ServiceProfile 失败：{}", JSONUtil.toJsonStr(serviceProfileVOPageVO));
                return false;
            }
            dto.setMulticastGroup(multicastGroup);
            dto.setLimit(null);
            dto.setOffset(null);
            // 获取 url
            String apiHost = ApiUtil.getApiHost();
            String apiUrl = apiHost + ApiConstant.MULTICAST_GROUPS;
            int apiTimeout = CommonConstant.API_TIMEOUT;
            // 创建请求，设置 header，超时时间
            HttpRequest post = HttpUtil.createPost(apiUrl);
            post.header(ParamConstant.HEADER_AUTHORIZATION, jwt);
            post.setConnectionTimeout(apiTimeout);
            post.setReadTimeout(apiTimeout);
            // 设置请求参数，发送请求，获取返回结果
            String ret = post.body(JSONUtil.toJsonStr(dto)).execute().body();
            JSONObject json = JSONUtil.parseObj(ret);
            if (StrUtil.isBlank(json.getStr("id"))) {
                log.error("保存 MulticastGroup 失败：请求参数：{}，返回结果：{}", JSONUtil.toJsonStr(dto), ret);
                return false;
            }
            return true;
        }
        return false;
    }

    @Override
    public PageVO<MulticastGroupVO> page(MulticastGroupDTO dto) {
        // 获取 token
        InternalLoginVO loginVo = CommonCache.getToken();
        String jwt = loginVo.getJwt();
        // 获取 url
        String apiHost = ApiUtil.getApiHost();
        String apiUrl = apiHost + ApiConstant.MULTICAST_GROUPS;
        int apiTimeout = CommonConstant.API_TIMEOUT;
        try {
            // 创建请求，设置 header，超时时间
            HttpRequest get = HttpUtil.createGet(apiUrl);
            get.header(ParamConstant.HEADER_AUTHORIZATION, jwt);
            get.setConnectionTimeout(apiTimeout);
            get.setReadTimeout(apiTimeout);
            // 设置请求参数
            Map<String, Object> formMap = BeanUtil.beanToMap(dto, false, true);
            // 发送请求，获取返回结果
            String ret = get.form(formMap).execute().body();
            return JSONUtil.toBean(ret, new TypeReference<PageVO<MulticastGroupVO>>() {
            }, false);
        } catch (Exception e) {
            log.error("page multicastGroup error", e);
            return null;
        }
    }

    @Override
    public MulticastGroupDetailVO getById(String id) {
        // 获取 token
        InternalLoginVO loginVo = CommonCache.getToken();
        String jwt = loginVo.getJwt();
        // 获取 url
        String apiHost = ApiUtil.getApiHost();
        String apiUrl = apiHost + ApiConstant.MULTICAST_GROUPS + "/" + id;
        int apiTimeout = CommonConstant.API_TIMEOUT;
        try {
            // 创建请求，设置 header，超时时间
            HttpRequest get = HttpUtil.createGet(apiUrl);
            get.header(ParamConstant.HEADER_AUTHORIZATION, jwt);
            get.setConnectionTimeout(apiTimeout);
            get.setReadTimeout(apiTimeout);
            // 发送请求，获取返回结果
            String ret = get.execute().body();
            return JSONUtil.toBean(ret, new TypeReference<MulticastGroupDetailVO>() {
            }, false);
        } catch (Exception e) {
            log.error("get multicastGroup error", e);
            return null;
        }
    }

    @Override
    public MulticastGroupDetailVO insertDeviceToMulticastGroup(MulticasGroupDeviceDTO dto) {
        // 获取 token
        InternalLoginVO loginVo = CommonCache.getToken();
        String jwt = loginVo.getJwt();
        // 查询组播数据
        MulticastGroupDTO multicastGroupDTO = new MulticastGroupDTO();
        multicastGroupDTO.setJwt(jwt);
        PageVO<MulticastGroupVO> page = page(multicastGroupDTO);
        if (Objects.isNull(page) || CollUtil.isEmpty(page.getResult())) {
            return null;
        }
        // 默认取第 1 个
        MulticastGroupVO multicastGroupVO = page.getResult().get(0);
        String multicastGroupID = multicastGroupVO.getId();
        dto.setMulticastGroupID(multicastGroupID);
        // 获取 url
        String apiHost = ApiUtil.getApiHost();
        String apiUrl = apiHost + StrUtil.format(ApiConstant.MULTICAST_GROUPS_DEVICES, multicastGroupID);
        int apiTimeout = CommonConstant.API_TIMEOUT;
        // 创建请求，设置 header，超时时间
        HttpRequest post = HttpUtil.createPost(apiUrl);
        post.header(ParamConstant.HEADER_AUTHORIZATION, jwt);
        post.setConnectionTimeout(apiTimeout);
        post.setReadTimeout(apiTimeout);
        // 设置请求参数，发送请求，获取返回结果
        String ret = post.body(JSONUtil.toJsonStr(dto)).execute().body();
        JSONObject jsonObject = JSONUtil.parseObj(ret);
        String error = jsonObject.getStr(ResultConstant.ERROR);
        if (StrUtil.isBlank(error)) {
            return getById(multicastGroupID);
        } else {
            log.error("添加设备到多播组失败：{}", ret);
        }
        return null;
    }

    @Override
    public boolean removeDeviceFromMulticastGroup(MulticasGroupDeviceDTO dto) {
        // 获取 token
        InternalLoginVO loginVo = CommonCache.getToken();
        String jwt = loginVo.getJwt();
        // 查询组播数据
        MulticastGroupDTO multicastGroupDTO = new MulticastGroupDTO();
        multicastGroupDTO.setJwt(jwt);
        PageVO<MulticastGroupVO> page = page(multicastGroupDTO);
        if (Objects.isNull(page) || CollUtil.isEmpty(page.getResult())) {
            return true;
        }
        // 默认取第 1 个
        MulticastGroupVO multicastGroupVO = page.getResult().get(0);
        String multicastGroupID = multicastGroupVO.getId();
        dto.setMulticastGroupID(multicastGroupID);
        // 获取 url
        String apiHost = ApiUtil.getApiHost();
        String apiUrl = apiHost + StrUtil.format(ApiConstant.MULTICAST_GROUPS_DEVICES, multicastGroupID) + "/" + dto.getDevEUI();
        int apiTimeout = CommonConstant.API_TIMEOUT;
        // 创建请求，设置 header，超时时间
        HttpRequest delete = HttpUtil.createRequest(Method.DELETE, apiUrl);
        delete.header(ParamConstant.HEADER_AUTHORIZATION, jwt);
        delete.setConnectionTimeout(apiTimeout);
        delete.setReadTimeout(apiTimeout);
        // 设置请求参数，发送请求，获取返回结果
        delete.execute().body();
        return true;
    }

    @Override
    public boolean itemMulticastGroup(MulticastQueueItemDTO dto) {
        // 获取 token
        InternalLoginVO loginVo = CommonCache.getToken();
        String jwt = loginVo.getJwt();
        // 查询组播数据
        MulticastGroupDTO multicastGroupDTO = new MulticastGroupDTO();
        multicastGroupDTO.setJwt(jwt);
        PageVO<MulticastGroupVO> page = page(multicastGroupDTO);
        if (Objects.isNull(page) || CollUtil.isEmpty(page.getResult())) {
            return false;
        }
        // 默认取第 1 个
        String multicastGroupID = page.getResult().get(0).getId();
        dto.getMulticastQueueItem().setMulticastGroupID(multicastGroupID);
        // 获取 url
        String apiHost = ApiUtil.getApiHost();
        String apiUrl = apiHost + StrUtil.format(ApiConstant.MULTICAST_GROUPS_ITEM_QUEUE, multicastGroupID);
        int apiTimeout = CommonConstant.API_TIMEOUT;
        // 创建请求，设置 header，超时时间
        HttpRequest post = HttpUtil.createPost(apiUrl);
        post.header(ParamConstant.HEADER_AUTHORIZATION, jwt);
        post.setConnectionTimeout(apiTimeout);
        post.setReadTimeout(apiTimeout);
        // 设置请求参数，发送请求，获取返回结果
        String ret = post.body(JSONUtil.toJsonStr(dto)).execute().body();
        JSONObject jsonObject = JSONUtil.parseObj(ret);
        String error = jsonObject.getStr(ResultConstant.ERROR);
        if (StrUtil.isBlank(error)) {
            return true;
        } else {
            log.error("发送节点组播请求失败：{}", ret);
        }
        return false;
    }
}

package org.springblade.thingcom.core.chirpStack.dto;

import lombok.Data;

/**
 * @ClassName InternalLoginDTO
 * @Description: 登录，dto 参数
 * @Author: zgq
 * @Date: 2024/4/15 10:52
 * @Version: 1.0
 **/
@Data
public class InternalLoginDTO implements java.io.Serializable {
    private static final long serialVersionUID = -20478945643298718L;

    /**
     * email，默认 admin
     */
    private String email = "admin";
    /**
     * password，默认 admin
     */
    private String password = "admin";
}

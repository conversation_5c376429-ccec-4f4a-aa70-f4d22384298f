package org.springblade.thingcom.core.chirpStack.service;

import org.springblade.thingcom.core.chirpStack.dto.ServiceProfileDTO;
import org.springblade.thingcom.core.chirpStack.vo.PageVO;
import org.springblade.thingcom.core.chirpStack.vo.ServiceProfileVO;

/**
 * <AUTHOR>
 * @since 2024-10-09 15:27
 */
public interface ServiceProfileService {
    /**
     * 分页查询组织
     *
     * @param dto
     * @return PageVO<ServiceProfileVO>
     */
    PageVO<ServiceProfileVO> page(ServiceProfileDTO dto);

    /**
     * 新增serviceProfile
     *
     * @param serviceProfile
     * @return
     */
    boolean saveData(ServiceProfileDTO.ServiceProfile serviceProfile);
}

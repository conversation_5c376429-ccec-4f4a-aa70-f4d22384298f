package org.springblade.thingcom.core.chirpStack.constant;

/**
 * chirpStack API 常量
 **/
public interface ApiConstant {

    /**
     * 登录，获取 token
     */
    String LOGIN = "/api/internal/login";

    /**
     * 组织
     */
    String ORGANIZATION = "/api/organizations";

    /**
     * NetworkServer
     */
    String NETWORK_SERVER = "/api/network-servers";

    /**
     * 应用
     */
    String APPLICATION = "/api/applications";

    /**
     * DeviceProfile
     */
    String DEVICE_PROFILE = "/api/device-profiles";

    /**
     * DeviceQueue
     */
    String DEVICE_QUEUE = "/api/devices/{}/queue";

    /**
     * 设备
     */
    String DEVICE = "/api/devices";

    /**
     * 获取设备激活信息，{devEui}
     */
    String DEVICE_ACTIVATION = "/api/devices/{}/activation";

    /**
     * 保存设备激活信息，{devEui}
     */
    String DEVICE_ACTIVATE = "/api/devices/{}/activate";

    /**
     * 网关
     */
    String GATEWAYS = "/api/gateways";

    /**
     * 网关配置
     */
    String GATEWAY_PROFILES = "/api/gateway-profiles";

    /**
     * 服务配置
     */
    String SERVICE_PROFILES = "/api/service-profiles";

    /**
     * 组播配置
     */
    String MULTICAST_GROUPS = "/api/multicast-groups";

    /**
     * 设备-组播，{groupID}
     */
    String MULTICAST_GROUPS_DEVICES = "/api/multicast-groups/{}/devices";

    /**
     * 发送节点组播，{groupID}
     */
    String MULTICAST_GROUPS_ITEM_QUEUE = "/api/multicast-groups/{}/queue";

    /**
     * 消息
     */
    String EVENTS = "/events";

    /**
     * 事件
     */
    String FRAMES = "/frames";
}

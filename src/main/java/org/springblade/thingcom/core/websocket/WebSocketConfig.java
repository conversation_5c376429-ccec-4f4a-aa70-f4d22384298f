package org.springblade.thingcom.core.websocket;

import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.thingcom.warning.service.AlarmService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.yeauty.standard.ServerEndpointExporter;

/**
 * @ClassName WebSocketConfig
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/8/8 7:48
 * @Version 1.0
 **/
@Configuration
public class WebSocketConfig {

    @Bean
    public ServerEndpointExporter serverEndpointExporter() {
        return new ServerEndpointExporter();
    }

    @Autowired
    public void setService(AlarmService alarmService, BladeRedis bladeRedis) {
        WebSocketService.alarmService = alarmService;
        WebSocketService.bladeRedis = bladeRedis;
    }
}

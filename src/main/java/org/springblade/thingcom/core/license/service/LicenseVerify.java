package org.springblade.thingcom.core.license.service;

import cn.hutool.core.lang.Assert;
import de.schlichtherle.license.*;
import lombok.extern.slf4j.Slf4j;
import org.springblade.thingcom.core.license.constant.LicenseConstant;
import org.springblade.thingcom.core.license.entity.LicenseVerifyParam;

import java.io.File;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.prefs.Preferences;

/**
 * License校验类
 */
@Slf4j
public class LicenseVerify {
    /**
     * 安装License证书
     */
    public synchronized LicenseContent install(LicenseVerifyParam param) {
        LicenseContent result = null;
        DateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //1. 安装证书
        try {
            LicenseManager licenseManager = LicenseManagerHolder.getInstance(initLicenseParam(param));
            Assert.notNull(licenseManager, "您的证书无效，请核查服务器是否取得授权或重新申请证书！");
            licenseManager.uninstall();

            result = licenseManager.install(new File(param.getLicensePath()));
            //log.info(MessageFormat.format("证书安装成功，证书有效期：{0} - {1}"
            //        , format.format(result.getNotBefore()), format.format(result.getNotAfter())));
        } catch (Exception e) {
            String message = e.getMessage();
            // 还未生效的证书，无需重新安装
            if (LicenseConstant.NOT_YET_VALID.equals(message)) {
                result = new LicenseContent();
                result.setInfo(LicenseConstant.NOT_YET_VALID);
                return result;
            }
            return null;
            //Assert.isFalse(true, "您的证书无效，请核查服务器是否取得授权或重新申请证书！");
            //log.error("证书安装失败！", e);
        }

        return result;
    }

    /**
     * 校验License证书
     *
     * @return boolean
     */
    public boolean verify() {
        LicenseManager licenseManager = LicenseManagerHolder.getInstance(null);
        Assert.notNull(licenseManager, "您的证书无效，请核查服务器是否取得授权或重新申请证书！");
        DateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //2. 校验证书
        try {
            LicenseContent licenseContent = licenseManager.verify();
            //System.out.println(licenseContent.getSubject());
            //log.info(MessageFormat.format("证书校验通过，证书有效期：{0} - {1}"
            //        , format.format(licenseContent.getNotBefore()), format.format(licenseContent.getNotAfter())));
            return true;
        } catch (Exception e) {
            Assert.isFalse(true, "您的证书无效，请核查服务器是否取得授权或重新申请证书！");
            //log.error("证书校验失败！", e);
            return false;
        }
    }

    /**
     * 初始化证书生成参数
     *
     * @param param License校验类需要的参数
     * @return de.schlichtherle.license.LicenseParam
     */
    private LicenseParam initLicenseParam(LicenseVerifyParam param) {
        Preferences preferences = Preferences.userNodeForPackage(LicenseVerify.class);

        CipherParam cipherParam = new DefaultCipherParam(param.getStorePass());

        KeyStoreParam publicStoreParam = new CustomKeyStoreParam(LicenseVerify.class
                , param.getPublicKeysStorePath()
                , param.getPublicAlias()
                , param.getStorePass()
                , null);

        return new DefaultLicenseParam(param.getSubject()
                , preferences
                , publicStoreParam
                , cipherParam);
    }

}

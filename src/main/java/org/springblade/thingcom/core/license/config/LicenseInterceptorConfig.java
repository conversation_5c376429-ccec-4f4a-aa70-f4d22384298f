//package org.springblade.thingcom.core.license.config;
//
//import org.springblade.thingcom.core.license.service.LicenseCheckInterceptor;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
//import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
//
///**
// * <AUTHOR>
// * @version 1.0.0
// * @ProjectName LicenseInterceptorConfig
// * @Description 注册拦截器
// * @createTime 2022/4/30 0030 21:11
// */
//@Configuration
//public class LicenseInterceptorConfig implements WebMvcConfigurer {
//
//    @Bean
//    public LicenseCheckInterceptor sysInterceptor() {
//        return new LicenseCheckInterceptor();
//    }
//
//    /**
//     * 添加拦截器
//     */
//    @Override
//    public void addInterceptors(InterceptorRegistry registry) {
//        registry.addInterceptor(sysInterceptor());
//        //.addPathPatterns("/**");
//    }
//}

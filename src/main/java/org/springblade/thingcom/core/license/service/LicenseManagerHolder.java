package org.springblade.thingcom.core.license.service;

import de.schlichtherle.license.LicenseManager;
import de.schlichtherle.license.LicenseParam;
import org.springblade.common.cache.CacheNames;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.thingcom.license.enums.LicenseStatusEnum;

import java.util.Objects;

/**
 * de.schlichtherle.license.LicenseManager的单例
 */
public class LicenseManagerHolder {

    private static volatile LicenseManager LICENSE_MANAGER;

    private static final BladeRedis bladeRedis;

    static {
        bladeRedis = SpringUtil.getBean(BladeRedis.class);
    }

    public static LicenseManager getInstance(LicenseParam param) {
        if (LICENSE_MANAGER == null) {
            synchronized (LicenseManagerHolder.class) {
                if (LICENSE_MANAGER == null) {
                    if (Objects.isNull(param)) {
                        bladeRedis.set(CacheNames.LICENSE_STATUS, LicenseStatusEnum.NO_EFFECTIVE.getValue());
                        throw new IllegalArgumentException("您的证书无效，请核查服务器是否取得授权或重新申请证书！");
                    }
                    LICENSE_MANAGER = new CustomLicenseManager(param);
                }
            }
        }

        return LICENSE_MANAGER;
    }

}

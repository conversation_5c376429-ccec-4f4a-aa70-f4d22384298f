package org.springblade.thingcom.core.license.service;

import lombok.extern.slf4j.Slf4j;
import org.springblade.common.cache.CacheNames;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.thingcom.license.enums.LicenseStatusEnum;
import org.springblade.thingcom.license.utils.LicenseUtil;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 使用拦截器拦截请求，验证证书的可用性
 */
@Slf4j
@Component
public class LicenseCheckInterceptor implements HandlerInterceptor {

    @Resource
    private BladeRedis bladeRedis;

    /**
     * 进入controller层之前拦截请求
     *
     * @param request
     * @param response
     * @param handler
     * @return
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // 拦截所有的 post 请求、导出（ApiPath 包含 export）、排除：登录（oauth/token）、上传证书的请求（license/upload）
        String requestURI = request.getRequestURI();
        if ("POST".equals(request.getMethod()) || requestURI.contains("export")) {
            if (requestURI.contains("oauth/token") || requestURI.contains("license/upload")
                    || requestURI.contains("/robot/import-data") || requestURI.contains("/thirdPartyMessage")) {
                return true;
            }
            // 校验证书是否有效
            verifyLicenseStatus();
        } else {
            // 电池、电机历史数据：robotBatteryLog、robotMotorLog
            // 历史告警：robotAlarmLog、gatewayAlarmLog、weatherStationAlarmLog、trackerAlarmLog、serverAlarmLog、thirdPartyAlarmLog
            // 操作日志：logApi、robotOperationLog、gatewayOperationLog
            if (requestURI.contains("/blade-system/user")
                    || requestURI.contains("robotBatteryLog")
                    || requestURI.contains("robotMotorLog")
                    || requestURI.contains("AlarmLog")
                    || requestURI.contains("logApi")
                    || requestURI.contains("OperationLog")) {
                // 校验证书是否有效
                verifyLicenseStatus();
            }
        }
        return true;
    }

    /**
     * 校验证书是否有效
     */
    private void verifyLicenseStatus() {
        // 检查系统时间是否被修改
        boolean modified = LicenseUtil.checkSystemTimeIsModified();
        if (modified) {
            throw new IllegalArgumentException("系统时间被人为修改，导致系统License异常，请恢复系统时间！");
        }
        // 校验证书是否有效
        LicenseVerify licenseVerify = new LicenseVerify();
        boolean verifyResult = licenseVerify.verify();
        if (verifyResult) {
            bladeRedis.set(CacheNames.LICENSE_STATUS, LicenseStatusEnum.EFFECTIVE.getValue());
        } else {
            bladeRedis.set(CacheNames.LICENSE_STATUS, LicenseStatusEnum.NO_EFFECTIVE.getValue());
            throw new IllegalArgumentException("您的证书无效，请核查服务器是否取得授权或重新申请证书！");
        }
    }

    /**
     * 处理请求完成后视图渲染之前的处理操作
     *
     * @param request
     * @param response
     * @param handler
     * @param modelAndView
     */
    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, @Nullable ModelAndView modelAndView) {
        //log.info("处理请求完成后视图渲染之前的处理操作");
    }

    /**
     * 视图渲染之后的操作
     *
     * @param request
     * @param response
     * @param handler
     * @param ex
     */
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, @Nullable Exception ex) {
        //log.info("视图渲染之后的操作");
    }

}

package org.springblade.thingcom.license.utils;

import org.springblade.common.cache.CacheNames;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.thingcom.license.enums.LicenseStatusEnum;

import java.util.Objects;

/**
 * @ClassName LicenseUtil
 * @Description license工具类
 * <AUTHOR>
 * @Date 2024/12/27 11:18
 * @Version 1.0
 **/
public class LicenseUtil {

    private static final BladeRedis bladeRedis;

    static {
        bladeRedis = SpringUtil.getBean(BladeRedis.class);
    }

    /**
     * 获取当前 license 状态
     *
     * @return license 状态
     */
    public static Integer getLicenseStatus() {
        // 先检查系统时间是否被修改
        boolean modified = checkSystemTimeIsModified();
        if (modified) {
            return LicenseStatusEnum.NO_EFFECTIVE.getValue();
        }
        // 检查license状态
        Object licenseStatusObj = bladeRedis.get(CacheNames.LICENSE_STATUS);
        // 默认无效
        Integer expired = LicenseStatusEnum.EXPIRED.getValue();
        return Objects.isNull(licenseStatusObj) ? expired : Func.toInt(licenseStatusObj, expired);
    }

    /**
     * 更新 license 系统时间戳（针对：采集器）
     */
    public static void updateLicenseSystemTimestamp() {
        // 更新 license 系统时间戳
        bladeRedis.set(CacheNames.LICENSE_SYSTEM_TIME, System.currentTimeMillis());
    }

    /**
     * 定时更新 license 系统时间戳（针对：license 定时任务校验系统时间是否被修改）
     */
    public static void timingUpdateLicenseSystemTimestamp() {
        // 获取当前时间戳
        long currentTimeMillis = System.currentTimeMillis();
        // 查询时间戳记录
        Long timestampRecord = bladeRedis.get(CacheNames.LICENSE_SYSTEM_TIME);
        // 判断是否把系统时间修改为历史时间了
        if (Objects.isNull(timestampRecord)) {
            // 更新时间戳
            bladeRedis.set(CacheNames.LICENSE_SYSTEM_TIME, currentTimeMillis);
        } else {
            if (currentTimeMillis - timestampRecord > 0) {
                // 更新时间戳
                bladeRedis.set(CacheNames.LICENSE_SYSTEM_TIME, currentTimeMillis);
            }
        }
    }

    /**
     * 校验系统时间是否被人为修改
     *
     * @return true：系统时间被修改，false：系统时间没有被修改
     */
    public static boolean checkSystemTimeIsModified() {
        // 获取当前时间戳
        long currentTimeMillis = System.currentTimeMillis();
        // 查询时间戳记录
        Long timestampRecord = bladeRedis.get(CacheNames.LICENSE_SYSTEM_TIME);
        // 判断是否把系统时间修改为历史时间了
        return Objects.nonNull(timestampRecord) && currentTimeMillis - timestampRecord < 0;
    }

}

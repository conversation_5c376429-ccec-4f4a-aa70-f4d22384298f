package org.springblade.thingcom.license.service;

import de.schlichtherle.license.LicenseContent;
import org.springblade.thingcom.core.mybatisPlus.IBaseService;
import org.springblade.thingcom.license.entity.License;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * license服务接口
 *
 * <AUTHOR>
 * @since 2024-11-19 17:55:00
 */
public interface LicenseService extends IBaseService<License> {

    /**
     * mac 地址下拉列表
     *
     * @return List<String>
     */
    List<String> selectList();

    /**
     * 上传license文件
     *
     * @param file
     * @return
     */
    boolean upload(MultipartFile file);

    /**
     * 安装并验证license文件
     *
     * @param filePath
     * @return
     */
    LicenseContent installAndVerify(String filePath);
}

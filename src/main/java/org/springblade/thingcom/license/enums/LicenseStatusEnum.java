package org.springblade.thingcom.license.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @ClassName LicenseStatusEnum
 * @Description license状态枚举
 * <AUTHOR>
 * @Date 2024/11/21 11:08
 * @Version 1.0
 **/
@Getter
@AllArgsConstructor
public enum LicenseStatusEnum {

    /**
     * 未生效
     */
    NO_EFFECTIVE(0, "未生效"),

    /**
     * 有效中
     */
    EFFECTIVE(1, "有效中"),

    /**
     * 已过期
     */
    EXPIRED(2, "已过期");

    private final Integer value;
    private final String label;

    public static String translation(Integer value) {
        return Arrays.stream(LicenseStatusEnum.values()).filter(e -> e.getValue().equals(value))
                .findFirst().orElse(EXPIRED).getLabel();
    }
}

package org.springblade.thingcom.license.wrapper;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springblade.common.utils.CommonUtil;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.thingcom.license.dto.LicenseDTO;
import org.springblade.thingcom.license.entity.License;
import org.springblade.thingcom.license.enums.LicenseStatusEnum;
import org.springblade.thingcom.license.enums.LicenseTypeEnum;
import org.springblade.thingcom.license.vo.LicenseVO;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * license包装类
 *
 * <AUTHOR>
 * @since 2024-11-19 17:55:01
 */
public class LicenseWrapper extends BaseEntityWrapper<License, LicenseVO> {

    public static LicenseWrapper build() {
        return new LicenseWrapper();
    }

    /**
     * 返回视图层所需的字段
     *
     * @param entity
     * @return LicenseVO
     */
    @Override
    public LicenseVO entityVO(License entity) {
        if (Objects.isNull(entity)) {
            return null;
        }
        return BeanUtil.copy(entity, LicenseVO.class);
    }

    /**
     * 返回视图层所需的字段
     *
     * @param entity
     * @return LicenseVO
     */
    public LicenseVO entityVoExt(License entity, LocalDateTime now) {
        if (Objects.isNull(entity)) {
            return null;
        }
        LicenseVO vo = BeanUtil.copy(entity, LicenseVO.class);
        // 永久授权不显示有效期（因为涉及校验，所以保存数据有初始化日期）
        if (LicenseTypeEnum.PERMANENT.getValue().equals(entity.getType())) {
            vo.setBeginTime(null);
            vo.setEndTime(null);
            vo.setStatus(LicenseStatusEnum.EFFECTIVE.getValue());
        } else {
            if (Objects.nonNull(entity.getBeginTime()) && Objects.nonNull(entity.getEndTime())) {
                if (entity.getBeginTime().isAfter(now)) {
                    // 开始时间 大于 当前时间：未生效
                    vo.setStatus(LicenseStatusEnum.NO_EFFECTIVE.getValue());
                } else if ((entity.getBeginTime().isBefore(now) || entity.getBeginTime().isEqual(now))
                        && (entity.getEndTime().isAfter(now) || entity.getEndTime().isEqual(now))) {
                    // 开始时间 小于等于 当前时间 && 结束时间 大于等于 当前时间：有效中
                    vo.setStatus(LicenseStatusEnum.EFFECTIVE.getValue());
                } else if (entity.getEndTime().isBefore(now)) {
                    // 结束时间 小于 当前时间：已过期
                    vo.setStatus(LicenseStatusEnum.EXPIRED.getValue());
                }
            }
        }
        // 类型
        vo.setTypeName(LicenseTypeEnum.translation(entity.getType()));
        vo.setStatusName(LicenseStatusEnum.translation(vo.getStatus()));
        return vo;
    }

    public List<LicenseVO> listVoExt(List<License> list, LocalDateTime now) {
        return list.stream().map(m -> entityVoExt(m, now)).collect(Collectors.toList());
    }

    public IPage<LicenseVO> pageVoExt(IPage<License> pages, LocalDateTime now) {
        List<LicenseVO> records = this.listVoExt(pages.getRecords(), now);
        IPage<LicenseVO> pageVo = new Page(pages.getCurrent(), pages.getSize(), pages.getTotal());
        pageVo.setRecords(records);
        return pageVo;
    }

    public LambdaQueryWrapper<License> setQueryParam(LicenseDTO dto) {
        LambdaQueryWrapper<License> lqw = new LambdaQueryWrapper<>();
        // 名称
        lqw.like(StrUtil.isNotBlank(dto.getName()), License::getName, dto.getName());
        // 类型
        lqw.eq(Objects.nonNull(dto.getType()), License::getType, dto.getType());
        // 状态
        LocalDateTime now = CommonUtil.getZoneTime();
        if (LicenseStatusEnum.NO_EFFECTIVE.getValue().equals(dto.getStatus())) {
            // 开始时间 大于 当前时间：未生效
            lqw.gt(License::getBeginTime, now);
        } else if (LicenseStatusEnum.EFFECTIVE.getValue().equals(dto.getStatus())) {
            // 开始时间 小于等于 当前时间 && 结束时间 大于等于 当前时间：有效中
            lqw.le(License::getBeginTime, now).ge(License::getEndTime, now);
        } else if (LicenseStatusEnum.EXPIRED.getValue().equals(dto.getStatus())) {
            // 结束时间 小于 当前时间：已过期
            lqw.lt(License::getEndTime, now);
        }
        // mac地址
        if (StrUtil.isNotBlank(dto.getMac())) {
            lqw.and(wrapper -> wrapper.like(License::getMac1, dto.getMac())
                    .or().like(License::getMac2, dto.getMac()));
        }
        // 倒序
        lqw.orderByDesc(License::getId);
        return lqw;
    }

}

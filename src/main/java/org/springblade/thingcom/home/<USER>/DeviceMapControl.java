package org.springblade.thingcom.home.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.thingcom.base.cache.SystemCache;
import org.springblade.thingcom.base.entity.SystemSet;
import org.springblade.thingcom.data.utils.RobotDataUtil;
import org.springblade.thingcom.device.entity.Robot;
import org.springblade.thingcom.device.entity.Tracker;
import org.springblade.thingcom.device.enums.DeviceStatusEnum;
import org.springblade.thingcom.device.service.RobotService;
import org.springblade.thingcom.device.service.TrackerService;
import org.springblade.thingcom.device.vo.RobotStatusVO;
import org.springblade.thingcom.device.vo.TrackerVO;
import org.springblade.thingcom.device.wrapper.RobotWrapper;
import org.springblade.thingcom.device.wrapper.TrackerWrapper;
import org.springblade.thingcom.home.service.DeviceMapService;
import org.springblade.thingcom.home.vo.RobotClearingPathVO;
import org.springblade.thingcom.home.vo.RobotMapVO;
import org.springblade.thingcom.operationMaintenance.dto.RobotClearingDTO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.springblade.core.tool.api.R.data;

/**
 * 设备地图控制层
 *
 * <AUTHOR>
 * @since 2024-03-08
 */

@AllArgsConstructor
@RestController
@RequestMapping("/deviceMap")
public class DeviceMapControl {

    private final RobotService robotService;
    private final TrackerService trackerService;
    private final DeviceMapService deviceMapService;

    /**
     * 设备状态分类统计
     *
     * @param robotRegionalId
     * @return
     */
    @GetMapping("/DeviceStatusCount")
    public R<Map<String, Object>> DeviceCountByStatus(Long robotRegionalId) {
        Map<String, Object> result = new HashMap<>();
        RobotStatusVO robotStatusVo = RobotDataUtil.getRobotStatusStatis(robotRegionalId);
        // 总数
        long totalCount = robotStatusVo.getTotalCount();
        // 查询今日清扫的机器人数量
        RobotClearingDTO robotClearingDto = new RobotClearingDTO();
        LocalDate now = LocalDate.now();
        robotClearingDto.setBeginDate(now);
        robotClearingDto.setEndDate(now);
        long todayClearingRobotCount = robotService.countClearingRobot(robotClearingDto);
        // 运行率（清扫率）
        double cleaningRate = totalCount > 0 ? (double) todayClearingRobotCount / totalCount * 100 : 0D;
        // 将清扫率保留两位小数
        cleaningRate = Double.parseDouble(String.format("%.2f", cleaningRate));
        result.put("allCount", totalCount);
        result.put("cleaningRate", cleaningRate);
        List<Map<String, Object>> resultList = new ArrayList<>();
        DeviceStatusEnum[] deviceStatusArray = DeviceStatusEnum.values();
        for (DeviceStatusEnum deviceStatusEnum : deviceStatusArray) {
            Map<String, Object> statusInfo = new HashMap<>();
            statusInfo.put("status", deviceStatusEnum.getValue());
            statusInfo.put("statusName", deviceStatusEnum.getLabel());
            // 根据状态获取对应数量
            Long countByStatus = RobotDataUtil.getCountByStatus(deviceStatusEnum.getValue(), robotStatusVo);
            // 今日清扫数量
            if (DeviceStatusEnum.TODAY_CLEAN_FINISH.getValue().equals(deviceStatusEnum.getValue())) {
                countByStatus = todayClearingRobotCount;
            }
            statusInfo.put("value", countByStatus);
            resultList.add(statusInfo);
        }
        result.put("statusList", resultList);
        return data(result);
    }

    /**
     * 查看机器人详情
     *
     * @param id
     * @return
     */
    @GetMapping("/getRobotDetail")
    public R<RobotMapVO> getRobotDetail(Long id) {
        Robot entity = robotService.getById(id);
        // 系统配置
        SystemSet systemSet = SystemCache.getById(CommonConstant.SYSTEM_CONFIG_ID);
        return data(RobotWrapper.build().entityVoMap(entity, systemSet));
    }

    /**
     * 查看跟踪支架
     *
     * @param id
     * @return
     */
    @GetMapping("/getTrackerDetail")
    public R<TrackerVO> getTraDetail(Long id) {
        Tracker entity = trackerService.getById(id);
        return data(TrackerWrapper.build().entityVOMap(entity));
    }

    /**
     * 根据区域编号分页获取机器人
     *
     * @param query
     * @param robotRegionalId
     * @return 包含机器人列表的分页响应
     */
    @GetMapping("/getRobotsByRegionalNumber")
    public R<IPage<RobotClearingPathVO>> pageRobotPath(Query query, Long robotRegionalId) {
        IPage<RobotClearingPathVO> robotPage = deviceMapService.pageRobotPath(Condition.getPage(query), robotRegionalId);
        return R.data(robotPage);
    }

}
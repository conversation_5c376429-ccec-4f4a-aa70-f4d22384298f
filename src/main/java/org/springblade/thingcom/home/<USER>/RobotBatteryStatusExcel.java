package org.springblade.thingcom.home.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;

@Data
@ColumnWidth(25)
@HeadRowHeight(52)
@ContentRowHeight(18)
public class RobotBatteryStatusExcel implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 分区编号
     */
    @ExcelProperty(value = "分区编号", index = 0)
    private String regionalNumber;

    /**
     * 机器人编号
     */
    @ExcelProperty(value = "机器人编号", index = 1)
    private String robotName;

    /**
     * 电池状态
     */
    @ColumnWidth(90)
    @ExcelProperty(value = "电池状态", index = 2)
    private String batteryStatus;
}

package org.springblade.thingcom.home.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;

@Data
@ColumnWidth(16)
@HeadRowHeight(52)
@ContentRowHeight(18)
public class TrackerStatusExcel implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 区域名称
     */
    @ExcelProperty(value = "区域名称")
    private String regionalName;

    /**
     * 正常
     */
    @ExcelProperty(value = "正常")
    private String normalNumber;

    /**
     * 离线
     */
    @ExcelProperty(value = "离线")
    private String criticalNumber;

    /**
     * 告警
     */
    @ExcelProperty(value = "告警")
    private String overNumber;
}

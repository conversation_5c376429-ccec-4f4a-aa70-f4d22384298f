package org.springblade.thingcom.home.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.thingcom.base.cache.SystemCache;
import org.springblade.thingcom.base.entity.SystemSet;
import org.springblade.thingcom.communication.enums.RobotLocationEnum;
import org.springblade.thingcom.communication.enums.RobotWalkDirectionEnum;
import org.springblade.thingcom.data.utils.RobotDataUtil;
import org.springblade.thingcom.data.vo.RobotTrackerLocationVO;
import org.springblade.thingcom.device.entity.Robot;
import org.springblade.thingcom.device.entity.Tracker;
import org.springblade.thingcom.device.enums.ClearingStatusEnum;
import org.springblade.thingcom.device.enums.DeviceStatusEnum;
import org.springblade.thingcom.device.enums.TrackerStatusEnum;
import org.springblade.thingcom.device.enums.UpgradeStatusEnum;
import org.springblade.thingcom.device.service.RobotService;
import org.springblade.thingcom.device.service.TrackerService;
import org.springblade.thingcom.device.vo.RobotRealDataVO;
import org.springblade.thingcom.home.service.DeviceMapService;
import org.springblade.thingcom.home.vo.RobotClearingPathVO;
import org.springblade.thingcom.operationMaintenance.entity.RobotClearing;
import org.springblade.thingcom.operationMaintenance.service.RobotClearingService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 设备地图服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@Service
@AllArgsConstructor
public class DeviceMapServiceImpl implements DeviceMapService {

    private final RobotService robotService;
    private final TrackerService trackerService;
    private final RobotClearingService robotClearingService;

    @Override
    public IPage<RobotClearingPathVO> pageRobotPath(IPage<Robot> page, Long robotRegionalId) {
        // 系统配置
        SystemSet systemSet = SystemCache.getById(CommonConstant.SYSTEM_CONFIG_ID);
        // 分页查询机器人
        IPage<Robot> robotPage = robotService.page(page, Wrappers.<Robot>lambdaQuery()
                .eq(Objects.nonNull(robotRegionalId), Robot::getRobotRegionalId, robotRegionalId)
                .orderByAsc(Robot::getRobotNumber));
        List<Robot> robotList = robotPage.getRecords();
        if (CollUtil.isEmpty(robotList)) {
            return null;
        }
        // 根据机器人id查询清扫记录
        List<Long> robotIdList = robotList.stream().map(Robot::getId).collect(Collectors.toList());
        List<RobotClearing> robotClearingList = robotClearingService.list(Wrappers.<RobotClearing>lambdaQuery()
                .in(RobotClearing::getRobotId, robotIdList));
        // 构建返回结果
        IPage<RobotClearingPathVO> robotPathPage = new Page<>();
        robotPathPage.setTotal(robotPage.getTotal());
        robotPathPage.setPages(robotPage.getPages());
        robotPathPage.setCurrent(robotPage.getCurrent());
        robotPathPage.setSize(robotPage.getSize());
        List<RobotClearingPathVO> robotClearingPathList = new ArrayList<>((int) robotPage.getSize());
        RobotClearingPathVO robotClearingPathVO;
        // 赋值返回结果信息
        for (Robot robot : robotList) {
            robotClearingPathVO = new RobotClearingPathVO();
            robotClearingPathVO.setClearPathId(robot.getClearingPathId());
            robotClearingPathVO.setRobotId(robot.getId());
            robotClearingPathVO.setStatus(robot.getStatus());
            robotClearingPathVO.setStatusName(DeviceStatusEnum.translationValue(robot.getStatus()));
            robotClearingPathVO.setUpgradeStatus(robot.getUpgradeStatus());
            robotClearingPathVO.setUpgradeStatusName(UpgradeStatusEnum.translationValue(robot.getUpgradeStatus()));
            // 今日清扫状态
            RobotClearing robotClearing = robotClearingList.stream()
                    .filter(r -> r.getRobotId().equals(robot.getId())).findFirst().orElse(null);
            Integer todayClearingStatus = RobotDataUtil.getTodayClearingStatus(robotClearing);
            String todayClearingStatusName = ClearingStatusEnum.translationValue(todayClearingStatus);
            robotClearingPathVO.setTodayClearingStatus(todayClearingStatus);
            robotClearingPathVO.setTodayClearingStatusName(todayClearingStatusName);
            // 根据清扫路径，查询清扫路径下的跟踪支架信息
            List<Tracker> trackerOrderList = trackerService.listTrackerBindByClearingPathId(robot.getClearingPathId());
            // 返回桥架，在计算位置时，会颠倒顺序，因此使用一个新列表
            List<Tracker> trackerList = new ArrayList<>(trackerOrderList);
            // 机器人处于跟踪器连接处的位置
            Long trackerConnectId = null;
            // 机器人位置，计算当前处于哪个跟踪器
            RobotRealDataVO robotLocationAndDirection = RobotDataUtil.getRobotRealDataByEui(robot.getEui());
            Integer location = robotLocationAndDirection.getLocation();
            Integer direction = robotLocationAndDirection.getDirection();
            if (Objects.nonNull(location) && Objects.nonNull(direction)) {
                RobotTrackerLocationVO robotTrackerLocation
                        = RobotDataUtil.getRobotTrackerLocation(trackerList, systemSet, location, direction);
                // 起点位置
                String locationInfo = robotTrackerLocation.getLocationInfo();
                if (locationInfo.contains(RobotLocationEnum.START.getLabel())) {
                    robotClearingPathVO.setIsStart(true);
                }
                // 终点位置
                if (locationInfo.contains(RobotLocationEnum.END.getLabel())) {
                    robotClearingPathVO.setIsEnd(true);
                }
                // 连接处
                List<Tracker> trackerConnectList = robotTrackerLocation.getTrackerConnectList();
                if (CollUtil.isNotEmpty(trackerConnectList) && trackerConnectList.size() > 1) {
                    int trackerIndex = 0;
                    // 反方向取第二个跟踪器
                    if (RobotWalkDirectionEnum.BACKWARD.getValue().equals(direction)) {
                        trackerIndex = 1;
                    }
                    trackerConnectId = trackerConnectList.get(trackerIndex).getId();
                }
            } else {
                // 默认起始桥架
                robotClearingPathVO.setIsStart(true);
            }
            // 机器人存在位置信息
            boolean robotHasLocation = robotClearingPathVO.getIsStart() || robotClearingPathVO.getIsEnd()
                    || Objects.nonNull(trackerConnectId);
            boolean isCenter = false;
            // 构建跟踪架信息
            List<RobotClearingPathVO.TrackersInfo> trackersInfoList = new ArrayList<>(trackerList.size());
            if (CollUtil.isEmpty(trackerList)) {
                robotClearingPathVO.setIsStart(true);
            } else {
                RobotClearingPathVO.TrackersInfo trackersInfo;
                // 查询跟踪器对应的机器人信息
                for (Tracker tracker : trackerOrderList) {
                    trackersInfo = new RobotClearingPathVO.TrackersInfo();
                    trackersInfo.setTrackerId(tracker.getId());
                    trackersInfo.setTrackerStatus(tracker.getStatus());
                    trackersInfo.setTrackerStatusName(TrackerStatusEnum.translationValue(tracker.getStatus()));
                    // 查询跟踪架上面对应的机器人信息（机器人处于哪个跟踪架上面）
                    Robot robotInTracker = robotList.stream()
                            .filter(f -> tracker.getId().equals(f.getTrackerId())).findFirst().orElse(null);
                    if (Objects.nonNull(robotInTracker) && !robotHasLocation) {
                        isCenter = true;
                        trackersInfo.setRobotId(robotInTracker.getId());
                        trackersInfo.setIsRobot(true);
                    }
                    // 机器人处于跟踪架连接处的位置
                    if (Objects.nonNull(trackerConnectId) && tracker.getId().equals(trackerConnectId)) {
                        isCenter = true;
                        trackersInfo.setRobotId(robot.getId());
                        trackersInfo.setIsRobot(true);
                    }
                    trackersInfoList.add(trackersInfo);
                }
            }
            // 默认起始桥架
            if (!robotClearingPathVO.getIsStart() && !robotClearingPathVO.getIsEnd() && !isCenter) {
                robotClearingPathVO.setIsStart(true);
            }
            robotClearingPathVO.setTrackersInfo(trackersInfoList);
            robotClearingPathList.add(robotClearingPathVO);
        }
        robotPathPage.setRecords(robotClearingPathList);
        return robotPathPage;
    }

}


package org.springblade.thingcom.device.excel.english.handler;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springblade.thingcom.command.entity.ControlType;
import org.springblade.thingcom.command.enums.ControlTypeCodeEnum;
import org.springblade.thingcom.command.service.ControlTypeService;
import org.springblade.thingcom.core.excel.handler.ColumnDynamicSelectDataHandler;
import org.springblade.thingcom.translate.utils.TranslateUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName StopBitSelectHandler
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/13 9:47
 * @Version 1.0
 **/
@Component
public class StopBitEnglishSelectHandler implements ColumnDynamicSelectDataHandler<String, List<String>> {

    @Resource
    private ControlTypeService controlTypeService;

    @Override
    public Function<String, List<String>> source(Object object) {

        // 这里就做数据的查询，最后返回一个List<String>即可
        return param -> controlTypeService.list(Wrappers.<ControlType>lambdaQuery()
                        .eq(ControlType::getCode, ControlTypeCodeEnum.STOP_BIT.getLabel()))
                .stream()
                .map(m -> TranslateUtil.chineseToEnglish(m.getValue()))
                .collect(Collectors.toList());
    }
}

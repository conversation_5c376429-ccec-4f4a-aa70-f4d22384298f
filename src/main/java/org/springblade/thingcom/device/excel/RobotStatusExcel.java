package org.springblade.thingcom.device.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024-07-30 14:25
 */
@Data
@ColumnWidth(22)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class RobotStatusExcel implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 序号
     */
    @ExcelProperty(value = "No.")
    private Integer number;

    /**
     * 数据类型
     */
    @ExcelProperty(value = "Signal Type")
    private String signalType = "Short Int";

    /**
     * 设备名称
     */
    @ExcelProperty(value = "IOB Name")
    private String name;

    /**
     * 功能码
     */
    @ExcelProperty(value = "Modbus Funtion")
    private Integer modbusFuntion = 03;

    /**
     * 寄存器地址
     */
    @ExcelProperty(value = "Register")
    private Integer register;
}

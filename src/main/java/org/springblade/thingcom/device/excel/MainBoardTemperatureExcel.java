package org.springblade.thingcom.device.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName MainBoardTemperatureExcel
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/4/8 14:51
 * @Version 1.0
 */
@Data
@ColumnWidth(20)
@HeadRowHeight(52)
@ContentRowHeight(18)
public class MainBoardTemperatureExcel implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 类型
     *
     * @see org.springblade.thingcom.device.enums.RobotConfigParamEnum
     */
    @ExcelIgnore
    private Integer type;

    /**
     * 保护温度，单位 ℃
     */
    @ExcelProperty(value = "保护温度(℃)")
    private String protectionTemperature;

    /**
     * 恢复温度，单位 ℃
     */
    @ExcelProperty(value = "恢复温度(℃)")
    private String recoveryTemperature;
}
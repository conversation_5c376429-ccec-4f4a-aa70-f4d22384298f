package org.springblade.thingcom.device.excel.handler;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import org.springblade.common.cache.DictCache;
import org.springblade.common.enums.DictEnum;
import org.springblade.modules.system.entity.Dict;
import org.springblade.thingcom.core.excel.converter.MultiSelectConverter;

import java.util.List;

/**
 * @ClassName LockStatusConverter
 * @Description: 锁定状态转换器
 * @Author: zgq
 * @Date: 2024/1/5 20:27
 * @Version: 1.0
 **/
public class LockStatusConverter extends MultiSelectConverter<String> {

    @Override
    public Class supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public String convertToJavaData(ReadCellData cellData, List<Object> parentValues, ExcelContentProperty contentProperty,
                                    GlobalConfiguration globalConfiguration) {
        String label = cellData.getStringValue();
        List<Dict> list = DictCache.getList(DictEnum.LOCK_STATUS.name());
        Assert.notEmpty(list, "锁定状态不存在。");
        String value = list.stream().filter(o -> o.getDictValue().equals(label)).findFirst().orElse(new Dict()).getDictKey();
        Assert.notBlank(value, "锁定状态不存在。");
        return value;
    }

    @Override
    public WriteCellData convertToExcelData(String value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        String label = DictCache.getValue(DictEnum.LOCK_STATUS.name(), value);
        if (StrUtil.isBlank(label)) {
            label = "";
        }
        return new WriteCellData(label);
    }
}

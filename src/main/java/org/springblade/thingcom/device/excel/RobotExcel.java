package org.springblade.thingcom.device.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.annotation.write.style.OnceAbsoluteMerge;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;
import org.springblade.common.constant.RegexpConstant;
import org.springblade.thingcom.core.excel.annotation.ExcelNotBlank;
import org.springblade.thingcom.core.excel.annotation.ExcelSelect;
import org.springblade.thingcom.device.excel.handler.ClearingPathConverter;
import org.springblade.thingcom.device.excel.handler.ClearingPathSelectHandler;
import org.springblade.thingcom.device.excel.handler.RobotRegionalConverter;
import org.springblade.thingcom.device.excel.handler.RobotRegionalSelectHandler;

import javax.validation.constraints.Max;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.List;

/**
 * 机器人excel类
 *
 * <AUTHOR>
 * @since 2024-01-03 19:25:50
 */
@Data
public class RobotExcel implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 模板类
     */
    @Data
    @EqualsAndHashCode(callSuper = true)
    @ColumnWidth(25)
    @HeadRowHeight(52)
    @ContentRowHeight(18)
    public static class DataTemplateV1 extends RobotExcel {
        private static final long serialVersionUID = -1861556650005721123L;

        /**
         * 机器人分区
         */
        @ExcelNotBlank(message = "机器人分区不能为空")
        @ExcelSelect(handler = RobotRegionalSelectHandler.class)
        @ExcelProperty(value = "*机器人分区", index = 0, converter = RobotRegionalConverter.class)
        private String robotRegional;

        /**
         * 名称前缀
         */
        @ExcelNotBlank(message = "名称前缀不能为空")
        @Length(max = 50, message = "名称前缀长度不能超过50")
        @ExcelProperty(value = "*名称前缀", index = 1)
        private String namePrefix;

        /**
         * 机器人编号
         */
        @ExcelNotBlank(message = "机器人编号不能为空")
        @Pattern(regexp = RegexpConstant.NUMBER, message = "机器人编号必须为数字")
        @Max(value = 65534, message = "机器人编号不能超过65534")
        @ExcelProperty(value = "*机器人编号", index = 2)
        private String robotNumber;

        /**
         * EUI
         */
        @ExcelNotBlank(message = "EUI不能为空")
        @Length(max = 50, message = "EUI长度不能超过50")
        @ExcelProperty(value = "*EUI", index = 3)
        private String eui;

        /**
         * 清扫路径编号
         */
        @ExcelNotBlank(message = "清扫路径编号不能为空")
        @ExcelSelect(handler = ClearingPathSelectHandler.class)
        @ExcelProperty(value = "*清扫路径编号", index = 4, converter = ClearingPathConverter.class)
        private String clearingPath;

        /**
         * 备注
         */
        @Length(max = 200, message = "备注长度不能超过200")
        @ExcelProperty(value = "备注", index = 5)
        private String remark;
    }

    /**
     * 模板类
     */
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class DataTemplate extends RobotExcel {
        private static final long serialVersionUID = -1861556650005721123L;
        /**
         * 机器人分区
         */
        private String robotRegional;
        /**
         * EUI
         */
        private String eui;
        /**
         * 机器人编号
         */
        private String robotNumber;
        /**
         * 清扫路径编号
         */
        private String clearingPath;
    }

    /**
     * 模板类
     */
    @Data
    @EqualsAndHashCode(callSuper = true)
    @ColumnWidth(30)
    // 将第 1 行的 4-8 列合并成一个单元格
    @OnceAbsoluteMerge(firstRowIndex = 0, lastRowIndex = 0, firstColumnIndex = 3, lastColumnIndex = 7)
    public static class DataTemplateV2 extends RobotExcel {
        private static final long serialVersionUID = -1861556650005721123L;

        /**
         * 机器人分区
         */
        @ExcelNotBlank(message = "机器人分区不能为空")
        @ExcelProperty(value = "*机器人分区")
        private String robotRegionalNumber;

        /**
         * EUI
         */
        @ExcelNotBlank(message = "EUI不能为空")
        @Length(max = 50, message = "EUI长度不能超过50")
        @ExcelProperty(value = "*EUI(机器人唯一码)")
        private String eui;

        /**
         * 机器人编号
         */
        @ExcelNotBlank(message = "机器人编号不能为空")
        //@Pattern(regexp = RegexpConstant.NUMBER, message = "机器人编号必须为数字")
        //@Max(value = 65534, message = "机器人编号不能超过65534")
        @ExcelProperty(value = "*机器人编号")
        private String robotNumber;

        /**
         * 清扫路径
         */
        @ExcelNotBlank(message = "清扫路径不能为空")
        @ExcelProperty(value = "*清扫路径···")
        private List<String> trackerList;
    }

    /**
     * 数据类
     */
    @Data
    @EqualsAndHashCode(callSuper = true)
    @ColumnWidth(25)
    @HeadRowHeight(52)
    @ContentRowHeight(18)
    public static class DataExport extends RobotExcel {
        private static final long serialVersionUID = 3575677371302433699L;

        /**
         * 机器人分区
         */
        @ExcelNotBlank(message = "机器人分区不能为空")
        @ExcelProperty(value = "*机器人分区", index = 0, converter = RobotRegionalConverter.class)
        private String robotRegional;

        /**
         * 机器人名称
         */
        @ExcelProperty(value = "机器人名称", index = 1)
        private String name;

        /**
         * 机器人状态，详见字典表
         */
        @ExcelProperty(value = "机器人状态", index = 2)
        private String status;

        /**
         * EUI
         */
        @ExcelNotBlank(message = "EUI不能为空")
        @Length(max = 50, message = "EUI长度不能超过50")
        @ExcelProperty(value = "*EUI", index = 3)
        private String eui;

        /**
         * 清扫路径
         */
        @ExcelProperty(value = "清扫路径", index = 4)
        private String clearingPath;

        /**
         * 备注
         */
        @Length(max = 200, message = "备注长度不能超过200")
        @ExcelProperty(value = "备注", index = 5)
        private String remark;

        /**
         * 跟踪器名称
         */
        @ExcelProperty(value = "当前所在跟踪器位置", index = 6)
        private String trackerName;

        /**
         * 跟踪器-告警状态，详见字典表
         */
        @ExcelProperty(value = "跟踪器-告警状态", index = 7)
        private String trackerStatus;

        /**
         * 锁定状态，1-未锁定；2-已锁定
         */
        @ExcelProperty(value = "锁定状态", index = 8)
        private String lockStatus;

        /**
         * 运行模式，1-手动；2-自动
         */
        @ExcelProperty(value = "运行模式", index = 9)
        private String runModel;

        /**
         * 光线传感器状态，1-正常；2-异常
         */
        @ExcelProperty(value = "光线传感器状态", index = 10)
        private String lightSensorStatus;

        /**
         * 光伏板输出电压，单位V
         */
        @ExcelProperty(value = "光伏板输出电压-V", index = 11)
        private String pvPanelOutputVoltage;

        /**
         * 光伏板输出电流，单位A
         */
        @ExcelProperty(value = "光伏板输出电流-A", index = 12)
        private String pvPanelOutputElectricity;
    }

}

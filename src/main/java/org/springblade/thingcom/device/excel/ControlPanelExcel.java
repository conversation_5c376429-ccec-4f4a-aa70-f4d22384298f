package org.springblade.thingcom.device.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;
import org.springblade.thingcom.core.excel.annotation.ExcelSelect;
import org.springblade.thingcom.device.excel.handler.RunRateSelectHandler;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024-04-08 17:14
 */
@Data
@ColumnWidth(35)
@HeadRowHeight(52)
@ContentRowHeight(18)
public class ControlPanelExcel implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 类型
     *
     * @see org.springblade.thingcom.device.enums.RobotConfigParamEnum
     */
    @ExcelIgnore
    private Integer type;
    /**
     * 反向等待时间(s)
     */
    @ExcelProperty(value = "反向等待时间(s)")
    private String reverseWaitTime;
    ///**
    // * 保护角度(°)
    // */
    //@ExcelProperty(value = "保护角度(°)")
    //private String protectAngle;
    /**
     * 行走电机速率
     */
    @ExcelSelect(handler = RunRateSelectHandler.class)
    @ExcelProperty(value = "行走电机速率")
    private String walkMotorRunRate;
    /**
     * 行走电机-电流上限停机值(mA)
     */
    @ExcelProperty(value = "行走电机-电流上限停机值(mA)")
    private String walkMotorElectricityMaxAstrictStop;
    /**
     * 行走电机-电流上限预警值(mA)
     */
    @ExcelProperty(value = "行走电机-电流上限预警值(mA)")
    private String walkMotorElectricityMaxAstrictWarn;
    /**
     * 行走电机-运行超时时间(s)
     */
    @ExcelProperty(value = "总里程(m)")
    private String walkMotorOverstepRunTime;

    ///**
    // * 毛刷电机-运行速率(%)
    // */
    //@ExcelProperty(value = "毛刷电机-运行速率(%)")
    //private String brushMotorRunRate;
    ///**
    // * 毛刷电机-电流上限停机值(mA)
    // */
    //@ExcelProperty(value = "毛刷电机-电流上限停机值(mA)")
    //private String brushMotorElectricityMaxAstrictStop;
    ///**
    // * 毛刷电机-电流上限预警值(mA)
    // */
    //@ExcelProperty(value = "毛刷电机-电流上限预警值(mA)")
    //private String brushMotorElectricityMaxAstrictWarn;
    ///**
    // * 毛刷电机-运行超时时间(s)
    // */
    //@ExcelProperty(value = "毛刷电机-运行超时时间(s)")
    //private String brushMotorOverstepRunTime;
    ///**
    // * 防风电机-运行速率(%)
    // */
    //@ExcelProperty(value = "防风电机-运行速率(%)")
    //private String windProtectionMotorRunRate;
    ///**
    // * 防风电机-电流上限停机值(mA)
    // */
    //@ExcelProperty(value = "防风电机-电流上限停机值(mA)")
    //private String windProtectionMotorElectricityMaxAstrictStop;
    ///**
    // * 防风电机-电流上限预警值(mA)
    // */
    //@ExcelProperty(value = "防风电机-电流上限预警值(mA)")
    //private String windProtectionMotorElectricityMaxAstrictWarn;
    ///**
    // * 防风电机-运行超时时间(s)
    // */
    //@ExcelProperty(value = "防风电机-运行超时时间(s)")
    //private String windProtectionMotorOverstepRunTime;
}

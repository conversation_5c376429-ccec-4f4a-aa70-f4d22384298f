package org.springblade.thingcom.device.excel.english.handler;

import org.springblade.thingcom.communication.enums.WeekEnum;
import org.springblade.thingcom.core.excel.handler.ColumnDynamicSelectDataHandler;
import org.springblade.thingcom.translate.utils.TranslateUtil;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName Te
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/10/12 16:51
 * @Version 1.0
 **/
@Component
public class WeekSelectEnglishHandler implements ColumnDynamicSelectDataHandler<String, List<String>> {

    @Override
    public Function<String, List<String>> source(Object object) {
        List<String> list = new ArrayList<>(8);
        // 每天放在第一行
        list.add(TranslateUtil.chineseToEnglish(WeekEnum.EVERYDAY.getZh()));
        // 添加周一到周日
        List<String> weekList = Arrays.stream(WeekEnum.values())
                .filter(week -> week != WeekEnum.EVERYDAY)
                .map(m -> TranslateUtil.chineseToEnglish(m.getZh()))
                .collect(Collectors.toList());
        list.addAll(weekList);
        // 这里就做数据的查询，最后返回一个List<String>即可
        return param -> list;
    }
}

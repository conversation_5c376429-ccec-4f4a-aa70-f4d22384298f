package org.springblade.thingcom.device.excel.handler;

import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.tool.support.Kv;
import org.springblade.thingcom.core.excel.handler.ColumnDynamicSelectDataHandler;
import org.springblade.thingcom.device.enums.BindStatusEnum;
import org.springblade.thingcom.device.service.ClearingPathService;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName ClearingPathSelectHandler
 * @Description: 清扫路径下拉框数据源
 * @Author: zgq
 * @Date: 2024/1/5 19:52
 * @Version: 1.0
 **/
@Component
@AllArgsConstructor
public class ClearingPathSelectHandler implements ColumnDynamicSelectDataHandler<String, List<String>> {

    private final ClearingPathService clearingPathService;

    @Override
    public Function<String, List<String>> source(Object object) {
        // 这里就做数据的查询，最后返回一个List<String>即可， 对于该方法的入参param没设计好，暂时影响不大
        List<Kv> list = clearingPathService.selectList(BindStatusEnum.UNBIND.getValue());
        if (list == null) {
            return null;
        }
        return param -> list.stream().map(m -> m.getStr(CommonConstant.LABEL)).collect(Collectors.toList());
    }
}

package org.springblade.thingcom.device.excel.handler;

import cn.hutool.core.lang.Assert;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.tool.support.Kv;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.thingcom.core.excel.converter.MultiSelectConverter;
import org.springblade.thingcom.device.entity.ClearingPath;
import org.springblade.thingcom.device.enums.BindStatusEnum;
import org.springblade.thingcom.device.service.ClearingPathService;

import java.util.List;
import java.util.Objects;

/**
 * @ClassName ClearingPathConverter
 * @Description: 清扫路径转换器
 * @Author: zgq
 * @Date: 2024/1/5 18:53
 * @Version: 1.0
 **/
public class ClearingPathConverter extends MultiSelectConverter<String> {

    private static final ClearingPathService clearingPathService;

    static {
        clearingPathService = SpringUtil.getBean(ClearingPathService.class);
    }

    @Override
    public Class supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public String convertToJavaData(ReadCellData cellData, List<Object> parentValues, ExcelContentProperty contentProperty,
                                    GlobalConfiguration globalConfiguration) {
        String label = cellData.getStringValue();
        List<Kv> list = clearingPathService.selectList(BindStatusEnum.UNBIND.getValue());
        Assert.notEmpty(list, "清扫路径不存在。");
        String value = list.stream().filter(o -> o.getStr(CommonConstant.LABEL).equals(label)).findFirst()
                .orElse(Kv.create()).getStr(CommonConstant.VALUE);
        Assert.notBlank(value, "清扫路径已经被其它机器人使用。");
        return value;
    }

    @Override
    public WriteCellData convertToExcelData(String value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        String label = "";
        ClearingPath entity = clearingPathService.getById(value);
        if (Objects.nonNull(entity)) {
            label = entity.getPathNumber();
        }
        return new WriteCellData(label);
    }
}

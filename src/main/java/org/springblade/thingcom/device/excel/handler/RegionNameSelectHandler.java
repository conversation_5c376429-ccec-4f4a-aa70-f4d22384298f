package org.springblade.thingcom.device.excel.handler;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springblade.thingcom.command.entity.ControlType;
import org.springblade.thingcom.command.service.ControlTypeService;
import org.springblade.thingcom.core.excel.handler.ColumnDynamicSelectDataHandler;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024-04-12 15:23
 */
@Component
public class RegionNameSelectHandler implements ColumnDynamicSelectDataHandler<String, List<String>> {
    @Resource
    private ControlTypeService controlTypeService;

    @Override
    public Function<String, List<String>> source(Object object) {

        // 这里就做数据的查询，最后返回一个List<String>即可
        return param -> controlTypeService.list(Wrappers.<ControlType>lambdaQuery().eq(ControlType::getCode, "region"))
                .stream()
                .map(ControlType::getValue)
                .collect(Collectors.toList());
    }
}

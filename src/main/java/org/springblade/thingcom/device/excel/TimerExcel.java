package org.springblade.thingcom.device.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;
import org.springblade.thingcom.core.excel.annotation.ExcelSelect;
import org.springblade.thingcom.device.excel.handler.WeekSelectHandler;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024-04-10 10:21
 */
@Data
@ColumnWidth(20)
@HeadRowHeight(52)
@ContentRowHeight(18)
public class TimerExcel implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 类型
     *
     * @see org.springblade.thingcom.device.enums.RobotConfigParamEnum
     */
    @ExcelIgnore
    private Integer type;
    /**
     * 星期
     */
    @ExcelSelect(handler = WeekSelectHandler.class)
    @ExcelProperty(value = "星期")
    private String week;
    /**
     * 时间，格式：HH:mm，默认00:00
     */
    @ExcelProperty(value = "时间(示例 01:00)")
    private String time;
    /**
     * 运行次数
     */
    @ExcelProperty(value = "运行次数")
    private String runCount;
}

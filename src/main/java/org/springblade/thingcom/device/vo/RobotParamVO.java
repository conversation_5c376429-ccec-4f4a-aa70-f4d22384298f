package org.springblade.thingcom.device.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import org.springblade.thingcom.communication.entity.ControlBoard;
import org.springblade.thingcom.communication.entity.MainBoardTemperature;
import org.springblade.thingcom.communication.entity.Wind;
import org.springblade.thingcom.communication.vo.BatteryVO;
import org.springblade.thingcom.communication.vo.LoraVO;
import org.springblade.thingcom.communication.vo.TimerVO;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName RobotParamVO
 * @Description: 机器人参数出参类
 * @Author: zgq
 * @Date: 2024/1/8 14:31
 * @Version: 1.0
 **/
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RobotParamVO implements Serializable {
    private static final long serialVersionUID = 5930738397213415489L;

    /**
     * 软件版本
     */
    private String softwareVersion;

    /**
     * 光伏板输出电压，单位V
     */
    private String pvPanelOutputVoltage;

    /**
     * 光伏板输出电流，单位A
     */
    private String pvPanelOutputElectricity;

    /**
     * 机器人本地时间（格式：yyyy-MM-dd HH:mm:ss）
     */
    private String time;

    /**
     * 机器人本地星期（格式：星期一 ~ 星期日）
     */
    private String week;

    /**
     * 停机位-值
     *
     * @see org.springblade.thingcom.communication.enums.StopBitEnum
     */
    private String stopBit;

    /**
     * 锁定状态，1-未锁定；2-已锁定
     *
     * @see org.springblade.thingcom.device.enums.LockStatusEnum
     */
    private String lockStatus;

    /**
     * 白天防误扫，0-关；1-开
     */
    private String daytimePreventClean;

    /**
     * Lora
     */
    private LoraVO lora;

    /**
     * 电池
     */
    private BatteryVO battery;

    /**
     * 控制板
     */
    private ControlBoard controlBoard;

    /**
     * 风
     */
    private Wind wind;

    /**
     * 定时器列表
     */
    private List<TimerVO> timerList;

    /**
     * 主板温度
     */
    private MainBoardTemperature mainBoardTemperature;
}

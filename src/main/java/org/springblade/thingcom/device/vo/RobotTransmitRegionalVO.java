package org.springblade.thingcom.device.vo;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.EqualsAndHashCode;
import org.springblade.thingcom.device.entity.RobotTransmitRegional;

/**
 * 机器人转发通道和机器人分区关系出参类
 *
 * <AUTHOR>
 * @since 2024-07-29 14:34:51
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties({"deleted", "updateTime", "updateId"})
public class RobotTransmitRegionalVO extends RobotTransmitRegional {
    private static final long serialVersionUID = 1L;

}

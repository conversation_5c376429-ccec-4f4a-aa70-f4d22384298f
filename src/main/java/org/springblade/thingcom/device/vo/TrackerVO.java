package org.springblade.thingcom.device.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.thingcom.device.entity.Tracker;
import org.springblade.thingcom.translate.annotation.TranslateCrtl;

/**
 * 跟踪器出参类
 *
 * <AUTHOR>
 * @since 2024-01-03 15:19:53
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties({"deleted", "updateId"})
public class TrackerVO extends Tracker {
    private static final long serialVersionUID = 1L;

    /**
     * 清扫路线ID
     */
    private Long clearingPathId;

    /**
     * 状态名称
     */
    private String statusName;

    /**
     * 机器人名称
     */
    private String robotName;
    /**
     * 机器人分区编号
     */
    private String robotRegionalNumber;
    /**
     * 跟踪器区域编号
     */
    private String trackerRegionalNumber;
    /**
     * 跟踪器区域名称
     */
    private String trackerRegionalName;
    /**
     * 跟踪器型号名称
     */
    @TranslateCrtl(translateMode = 0)
    private String trackerModelName;

    /**
     * 总数
     */
    private Long totalCount = 0L;
    /**
     * 正常数量
     */
    private Long normalCount = 0L;
    /**
     * 预警数量
     */
    private Long warnCount = 0L;
    /**
     * 告警数量
     */
    private Long alarmCount = 0L;
}
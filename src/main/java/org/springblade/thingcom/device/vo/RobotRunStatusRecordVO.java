package org.springblade.thingcom.device.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName RobotRunStatusRecordVO
 * @Description: 机器人运行状态记录VO
 * @Author: zgq
 * @Date: 2024/4/23 9:27
 * @Version: 1.0
 **/
@Data
public class RobotRunStatusRecordVO implements Serializable {
    private static final long serialVersionUID = 2943289603012947027L;

    /**
     * 机器人上一次运行状态，初始化为空
     */
    private Integer lastRunStatus;

    /**
     * 机器人上一次运行状态时间，初始化为空
     */
    private LocalDateTime lastRunStatusTime;

    /**
     * 机器人当前运行状态
     */
    private Integer currentRunStatus;
}

package org.springblade.thingcom.device.vo;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.EqualsAndHashCode;
import org.springblade.thingcom.device.entity.TransmitRobotData;

/**
 * 转发通道机器人数据出参类
 *
 * <AUTHOR>
 * @since 2024-08-01 23:56:51
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties({"deleted", "updateTime", "updateId"})
public class TransmitRobotDataVO extends TransmitRobotData {
    private static final long serialVersionUID = 1L;

}

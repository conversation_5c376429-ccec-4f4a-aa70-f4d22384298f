package org.springblade.thingcom.device.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;
import org.springblade.thingcom.core.mybatisPlus.entity.BaseEntity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 跟踪器实体类
 *
 * <AUTHOR>
 * @since 2024-01-03 15:19:54
 */
@Data
@TableName("tb_tracker")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Tracker extends BaseEntity<Tracker> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 分区id
     */
    private Long regionalId;

    /**
     * 第三方id
     */
    private Long thirdPartyId;

    /**
     * 第三方告警id
     */
    private Long thirdPartyAlarmId;

    /**
     * ip
     */
    private String ip;

    /**
     * 名称前缀
     */
    @Length(max = 50, message = "名称前缀长度不能超过50")
    private String namePrefix;

    /**
     * 跟踪器编号
     */
    @Length(max = 50, message = "跟踪器编号长度不能超过50")
    private String trackerNumber;

    /**
     * 名称
     */
    @Length(max = 100, message = "名称长度不能超过100")
    private String name;

    /**
     * 角度
     */
    private BigDecimal angle;

    /**
     * 状态，详见字典表 tracker_status
     */
    private Integer status;

    /**
     * 角度告警状态
     */
    private Integer angleStatus;

    /**
     * 支架告警状态
     */
    private Integer alarmStatus;

    /**
     * 跟踪器型号id
     */
    private Long trackerModelId;

    /**
     * 时间
     */
    private LocalDateTime time;

    /**
     * 0-未删除1-已删除
     * 其中：is_deleted=1，代码需要判断是不是删除的数据，如果是删除的数据，则无法下发指令
     */
    private Boolean isDeleted;
}

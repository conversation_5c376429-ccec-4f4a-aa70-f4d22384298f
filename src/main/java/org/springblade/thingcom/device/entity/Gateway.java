package org.springblade.thingcom.device.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.thingcom.core.mybatisPlus.entity.BaseEntity;
import org.springblade.thingcom.device.enums.GatewayStatusEnum;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 通讯网关
 * @TableName tb_gateway
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "tb_gateway")
public class Gateway extends BaseEntity<Gateway> implements Serializable {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 网关编号
     */
    private String gatewayNumber;

    /**
     * EUI
     */
    private String eui;

    /**
     * IP地址
     */
    private String ip;

    /**
     * 频段
     */
    private String frequencyBand;

    /**
     * 发射功率
     */
    private String transmissionPower;

    /**
     * 运行时长，单位 s
     */
    private Long runDuration = 0L;

    /**
     * 通信延时时长，单位 s
     */
    private Long communicationLatencyDuration = 0L;

    /**
     * 时间
     */
    private LocalDateTime time;
    /**
     * 状态
     */
    private Integer status = GatewayStatusEnum.NORMAL.getValue();
}
package org.springblade.thingcom.device.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springblade.thingcom.core.mybatisPlus.entity.BaseEntity;

import java.io.Serializable;

/**
 * 转发通道机器人数据实体类
 *
 * <AUTHOR>
 * @since 2024-08-01 23:56:51
 */
@Data
@TableName("tb_transmit_robot_data")
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class TransmitRobotData extends BaseEntity<TransmitRobotData> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 端口
     */
    private Integer port;

    /**
     * 机器人id
     */
    private Long robotId;

    /**
     * 机器人编号
     */
    private String robotNumber;

}

package org.springblade.thingcom.device.service;

import org.springblade.thingcom.core.mybatisPlus.IBaseService;
import org.springblade.thingcom.device.entity.TransmitRobotData;

/**
 * 转发通道机器人数据服务接口
 *
 * <AUTHOR>
 * @since 2024-08-01 23:56:51
 */
public interface TransmitRobotDataService extends IBaseService<TransmitRobotData> {

    /**
     * 检查数据合法性
     *
     * @param entity 实体对象
     * @return
     */
    void validateData(TransmitRobotData entity);

    /**
     * 新增转发通道机器人数据
     *
     * @param entity
     * @return
     */
    boolean saveData(TransmitRobotData entity);

    /**
     * 修改转发通道机器人数据
     *
     * @param entity
     * @return
     */
    boolean updateData(TransmitRobotData entity);

    /**
     * 根据端口删除转发通道机器人数据
     *
     * @param port
     */
    void deleteByPort(Integer port);
}

package org.springblade.thingcom.device.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.tool.support.Kv;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.thingcom.base.entity.SystemSet;
import org.springblade.thingcom.base.mapper.SystemSetMapper;
import org.springblade.thingcom.core.comparator.StringComparator;
import org.springblade.thingcom.core.mybatisPlus.impl.BaseServiceImpl;
import org.springblade.thingcom.device.dto.RobotRegionalDTO;
import org.springblade.thingcom.device.entity.Robot;
import org.springblade.thingcom.device.entity.RobotRegional;
import org.springblade.thingcom.device.excel.RobotRegionalExcel;
import org.springblade.thingcom.device.mapper.RobotMapper;
import org.springblade.thingcom.device.mapper.RobotRegionalMapper;
import org.springblade.thingcom.device.service.RobotRegionalService;
import org.springblade.thingcom.device.vo.RobotRegionalVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 机器人分区服务实现类
 *
 * <AUTHOR>
 * @since 2024-01-04 10:10:46
 */
@Service
@AllArgsConstructor
public class RobotRegionalServiceImpl extends BaseServiceImpl<RobotRegionalMapper, RobotRegional> implements RobotRegionalService {

    private final RobotMapper robotMapper;
    private final SystemSetMapper systemSetMapper;

    @Override
    public void validateData(RobotRegional entity) {
        // 检查名称是否存在
        Long count = baseMapper.selectCount(Wrappers.<RobotRegional>lambdaQuery()
                .eq(RobotRegional::getRegionalNumber, entity.getRegionalNumber())
                // 编辑时，排除自己
                .ne(entity.getId() != null, RobotRegional::getId, entity.getId()));
        Assert.isTrue(count == 0, StrUtil.format("编号[{}]已经存在", entity.getRegionalNumber()));
    }

    @Override
    public IPage<RobotRegionalVO> page(IPage<RobotRegional> page, RobotRegionalDTO dto) {
        List<Long> idList = Func.toLongList(dto.getIds());
        // 系统设置机器人参数
        SystemSet systemSet = systemSetMapper.selectOne(Wrappers.<SystemSet>lambdaQuery()
                .orderByAsc(SystemSet::getId).last("limit 1"));
        return baseMapper.page(page, dto, idList, systemSet);
    }

    @Override
    public List<RobotRegionalVO> list(RobotRegionalDTO dto) {
        List<Long> idList = Func.toLongList(dto.getIds());
        List<Long> excludeIdList = Func.toLongList(dto.getExcludeIds());
        // 系统设置机器人参数
        SystemSet systemSet = systemSetMapper.selectOne(Wrappers.<SystemSet>lambdaQuery()
                .orderByAsc(SystemSet::getId).last("limit 1"));
        return baseMapper.list(dto, idList, excludeIdList, systemSet);
    }

    @Override
    public RobotRegionalVO getById(Long id) {
        // 系统设置机器人参数
        SystemSet systemSet = systemSetMapper.selectOne(Wrappers.<SystemSet>lambdaQuery()
                .orderByAsc(SystemSet::getId).last("limit 1"));
        BigDecimal clearingAngleMin = null, clearingAngleMax = null;
        if (Objects.nonNull(systemSet)) {
            clearingAngleMin = systemSet.getClearingAngleMin();
            clearingAngleMax = systemSet.getClearingAngleMax();
        }
        RobotRegionalDTO dto = new RobotRegionalDTO();
        dto.setId(id);
        dto.setClearingAngleMin(clearingAngleMin);
        dto.setClearingAngleMax(clearingAngleMax);
        return baseMapper.getById(dto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveData(RobotRegional entity) {
        // 新增机器人分区
        return save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateData(RobotRegional entity) {
        // 修改机器人分区
        return updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteData(List<Long> idList) {
        Long count = robotMapper.selectCount(Wrappers.<Robot>lambdaQuery().in(Robot::getRobotRegionalId, idList));
        Assert.isTrue(count == 0, "分区下存在机器人，无法删除");
        return removeBatchByIds(idList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importData(List<RobotRegionalExcel.DataTemplate> datas) {
        List<String> repeatList = new ArrayList<>(0);
        List<RobotRegional> saveList = new ArrayList<>(datas.size());
        for (RobotRegionalExcel.DataTemplate excel : datas) {
            RobotRegional entity = BeanUtil.copyProperties(excel, RobotRegional.class);
            // 判断文件中是否有重复数据
            String regionalNumber = entity.getRegionalNumber();
            Assert.isTrue(!repeatList.contains(regionalNumber), StrUtil.format("编号[{}]重复", regionalNumber));
            repeatList.add(regionalNumber);
            // 判断数据库中是否有重复数据
            validateData(entity);
            saveList.add(entity);
        }
        // 批量新增机器人分区
        if (CollUtil.isNotEmpty(saveList)) {
            saveBatch(saveList);
        }
    }

    @Override
    public List<RobotRegionalExcel.DataExport> exportData(RobotRegionalDTO dto) {
        List<Long> idList = Func.toLongList(dto.getIds());
        // 系统设置机器人参数
        SystemSet systemSet = systemSetMapper.selectOne(Wrappers.<SystemSet>lambdaQuery()
                .orderByAsc(SystemSet::getId).last("limit 1"));
        return baseMapper.exportData(dto, idList, systemSet);
    }

    @Override
    public List<Kv> selectList() {
	    return list(Wrappers.<RobotRegional>lambdaQuery()
			    .select(RobotRegional::getId, RobotRegional::getRegionalNumber))
                .stream()
			    .sorted(Comparator.comparing(RobotRegional::getRegionalNumber, new StringComparator()))
                .map(obj -> Kv.create()
                        .set(CommonConstant.LABEL, obj.getRegionalNumber())
                        .set(CommonConstant.VALUE, obj.getId().toString()))
                .collect(Collectors.toList());
    }

}
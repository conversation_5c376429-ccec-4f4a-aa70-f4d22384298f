package org.springblade.thingcom.device.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.springblade.common.cache.ParamCache;
import org.springblade.common.constant.ParamConstant;
import org.springblade.common.utils.CommonUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.thingcom.core.mybatisPlus.impl.BaseServiceImpl;
import org.springblade.thingcom.device.dto.RobotTransmitDTO;
import org.springblade.thingcom.device.entity.Robot;
import org.springblade.thingcom.device.entity.RobotTransmit;
import org.springblade.thingcom.device.entity.RobotTransmitRegional;
import org.springblade.thingcom.device.entity.TransmitRobotData;
import org.springblade.thingcom.device.enums.DeviceStatusEnum;
import org.springblade.thingcom.device.mapper.RobotTransmitMapper;
import org.springblade.thingcom.device.service.RobotService;
import org.springblade.thingcom.device.service.RobotTransmitRegionalService;
import org.springblade.thingcom.device.service.RobotTransmitService;
import org.springblade.thingcom.device.service.TransmitRobotDataService;
import org.springblade.thingcom.device.vo.RobotTransmitVO;
import org.springblade.thingcom.operationMaintenance.entity.DcsRunStatis;
import org.springblade.thingcom.operationMaintenance.service.DcsRunStatisService;
import org.springblade.thingcom.socket.enums.RobotChannelTypeEnum;
import org.springblade.thingcom.socket.service.TcpSlaveService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 机器人转发通道服务实现类
 *
 * <AUTHOR>
 * @since 2024-07-29 14:34:50
 */
@Service
@AllArgsConstructor
public class RobotTransmitServiceImpl extends BaseServiceImpl<RobotTransmitMapper, RobotTransmit> implements RobotTransmitService {

    private final RobotTransmitRegionalService robotTransmitRegionalService;
    private final TransmitRobotDataService transmitRobotDataService;
    private final RobotService robotService;
    private final TcpSlaveService tcpSlaveService;
    private final DcsRunStatisService dcsRunStatisService;

    @Override
    public IPage<RobotTransmitVO> page(IPage<RobotTransmit> page, RobotTransmitDTO dto) {
        return baseMapper.page(page, dto);
    }

    @Override
    public void validateData(RobotTransmitDTO dto) {
        // 机器人分区不能超过10个
        List<Long> robotRegionalIdList = Func.toLongList(dto.getRobotRegionalIds());
        Assert.isTrue(robotRegionalIdList.size() <= 10, "机器人分区不能超过10个");
        String systemUsePort = ParamCache.getValue(ParamConstant.SYSTEM_USE_PORT);
        if (StrUtil.isNotBlank(systemUsePort)) {
            List<Integer> systemUsePortList = Func.toIntList(systemUsePort);
            Assert.isTrue(!systemUsePortList.contains(dto.getPort()), StrUtil.format("端口号[{}]已经被占用", dto.getPort()));
        }
        // 检查端口号是否存在
        Long count = baseMapper.selectCount(Wrappers.<RobotTransmit>lambdaQuery()
                .eq(RobotTransmit::getPort, dto.getPort())
                // 编辑时，排除自己
                .ne(dto.getId() != null, RobotTransmit::getId, dto.getId()));
        Assert.isTrue(count == 0, StrUtil.format("端口号[{}]已经存在", dto.getPort()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveData(RobotTransmitDTO dto) {
        // 新增机器人转发通道
        boolean ret = save(dto);
        // 新增机器人转发通道和机器人分区关系数据
        List<Long> robotRegionalIdList = Func.toLongList(dto.getRobotRegionalIds());
        List<RobotTransmitRegional> robotTransmitRegionalList = new ArrayList<>(10);
        for (Long robotRegionalId : robotRegionalIdList) {
            RobotTransmitRegional robotTransmitRegional = new RobotTransmitRegional();
            robotTransmitRegional.setRobotTransmitId(dto.getId());
            robotTransmitRegional.setRobotRegionalId(robotRegionalId);
            robotTransmitRegionalList.add(robotTransmitRegional);
        }
        robotTransmitRegionalService.saveBatch(robotTransmitRegionalList);
        // 根据类型生成点表数据
        if (RobotChannelTypeEnum.ROBOT_STATUS.getValue().equals(dto.getChannelType())) {
            listLatestRobotByPort(dto.getPort());
        } else {
            listTodayClearingLatestRobotByPort(dto.getPort());
        }
        // 建立 socket 连接
        tcpSlaveService.socketConnect(dto);
        return ret;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateData(RobotTransmitDTO dto) {
        RobotTransmit robotTransmit = getById(dto.getId());
        // 修改机器人转发通道
        boolean ret = updateById(dto);
        // 删除机器人转发通道和机器人分区关系数据
        robotTransmitRegionalService.remove(Wrappers.<RobotTransmitRegional>lambdaQuery()
                .eq(RobotTransmitRegional::getRobotTransmitId, dto.getId()));
        // 新增机器人转发通道和机器人分区关系数据
        List<Long> robotRegionalIdList = Func.toLongList(dto.getRobotRegionalIds());
        List<RobotTransmitRegional> robotTransmitRegionalList = new ArrayList<>(10);
        for (Long robotRegionalId : robotRegionalIdList) {
            RobotTransmitRegional robotTransmitRegional = new RobotTransmitRegional();
            robotTransmitRegional.setRobotTransmitId(dto.getId());
            robotTransmitRegional.setRobotRegionalId(robotRegionalId);
            robotTransmitRegionalList.add(robotTransmitRegional);
        }
        robotTransmitRegionalService.saveBatch(robotTransmitRegionalList);
        // 端口号发生变更，关闭旧端口的连接，建立新端口的连接
        if (!Objects.equals(robotTransmit.getPort(), dto.getPort())) {
            tcpSlaveService.socketClose(robotTransmit.getPort());
            tcpSlaveService.socketConnect(dto);
        }
        return ret;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteData(List<Long> idList) {
        List<RobotTransmit> robotTransmitList = listByIds(idList);
        // 删除机器人转发通道
        removeBatchByIds(idList);
        List<String> robotNameList = robotTransmitList.stream().map(RobotTransmit::getName).collect(Collectors.toList());
        // dcs运行统计数据
        dcsRunStatisService.remove(Wrappers.<DcsRunStatis>lambdaQuery()
                .in(DcsRunStatis::getTransmitName, robotNameList));
        // 删除机器人转发通道和机器人分区关系数据
        robotTransmitRegionalService.remove(Wrappers.<RobotTransmitRegional>lambdaQuery()
                .in(RobotTransmitRegional::getRobotTransmitId, idList));
        // 关闭 socket 连接
        for (RobotTransmit robotTransmit : robotTransmitList) {
            tcpSlaveService.socketClose(robotTransmit.getPort());
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Robot> listLatestRobotByPort(Integer port) {
        // 根据端口号查询机器人分区ID列表
        List<Long> robotRegionalIdList = baseMapper.listRobotRegionalIdByPort(port);
        if (CollUtil.isEmpty(robotRegionalIdList)) {
            return new ArrayList<>(0);
        }
        // 根据机器人分区ID列表查询机器人列表（按照分区编号、机器人编号升序排序）
        List<Robot> robotList = robotService.listTransmitRobot(robotRegionalIdList);
        // 更新转发通道机器人数据
        if (CollUtil.isNotEmpty(robotList)) {
            // 删除转发通道机器人数据
            transmitRobotDataService.deleteByPort(port);
            // 新增转发通道机器人数据
            List<TransmitRobotData> transmitRobotDataList = robotList.stream().map(robot -> {
                TransmitRobotData transmitRobotData = new TransmitRobotData();
                transmitRobotData.setPort(port);
                transmitRobotData.setRobotId(robot.getId());
                transmitRobotData.setRobotNumber(robot.getRobotNumber());
                return transmitRobotData;
            }).collect(Collectors.toList());
            transmitRobotDataService.saveBatch(transmitRobotDataList);
        }
        return robotList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Robot> listTodayClearingLatestRobotByPort(Integer port) {
        // 根据端口号查询机器人分区ID列表
        List<Long> robotRegionalIdList = baseMapper.listRobotRegionalIdByPort(port);
        if (CollUtil.isEmpty(robotRegionalIdList)) {
            return new ArrayList<>(0);
        }
        // 当天的起始时间
        LocalDateTime now = CommonUtil.getZoneTime();
        LocalDateTime beginTime = now.withHour(0).withMinute(0).withSecond(0);
        LocalDateTime endTime = now.withHour(23).withMinute(59).withSecond(59);
        // 查询今日清扫的机器人列表
        List<Robot> todayClearingRobotList = robotService.listTransmitClearingRobot(robotRegionalIdList, beginTime, endTime);
        // 查询所有的机器人列表
        List<Robot> robotList = robotService.listTransmitRobot(robotRegionalIdList);
        // 对今日清扫的机器人 status 赋值为 运行
        for (Robot robot : robotList) {
            Robot todayClearingRobot = todayClearingRobotList.stream()
                    .filter(r -> r.getId().equals(robot.getId())).findFirst().orElse(null);
            if (Objects.nonNull(todayClearingRobot)) {
                robot.setStatus(DeviceStatusEnum.RUN.getValue());
            } else {
                robot.setStatus(DeviceStatusEnum.NORMAL.getValue());
            }
        }
        // 更新转发通道机器人数据
        if (CollUtil.isNotEmpty(robotList)) {
            // 删除转发通道机器人数据
            transmitRobotDataService.deleteByPort(port);
            // 新增转发通道机器人数据
            List<TransmitRobotData> transmitRobotDataList = robotList.stream().map(robot -> {
                TransmitRobotData transmitRobotData = new TransmitRobotData();
                transmitRobotData.setPort(port);
                transmitRobotData.setRobotId(robot.getId());
                transmitRobotData.setRobotNumber(robot.getRobotNumber());
                return transmitRobotData;
            }).collect(Collectors.toList());
            transmitRobotDataService.saveBatch(transmitRobotDataList);
        }
        return robotList;
    }

    @Override
    public List<Robot> listTransmitRobotByPort(Integer port) {
        // 最新机器人列表（最新的数据）
        List<Robot> latestRobotList = new ArrayList<>(0);
        // 根据端口号查询机器人分区ID列表
        List<Long> robotRegionalIdList = baseMapper.listRobotRegionalIdByPort(port);
        if (CollUtil.isNotEmpty(robotRegionalIdList)) {
            // 根据机器人分区ID列表查询机器人列表（按照分区编号、机器人编号升序排序）
            latestRobotList = robotService.listTransmitRobot(robotRegionalIdList);
        }
        // 根据端口查询转发通道机器人数据（上一次的数据）
        List<TransmitRobotData> transmitRobotDataList = transmitRobotDataService.list(Wrappers.<TransmitRobotData>lambdaQuery()
                .eq(TransmitRobotData::getPort, port));
        // 列表为空，返回最新的数据
        if (CollUtil.isEmpty(transmitRobotDataList)) {
            return latestRobotList;
        }
        // 匹配状态：在线、离线、删除
        List<Robot> robotList = new ArrayList<>(transmitRobotDataList.size());
        Robot robot;
        for (TransmitRobotData transmitRobotData : transmitRobotDataList) {
            robot = new Robot();
            robot.setId(transmitRobotData.getId());
            robot.setRobotNumber(transmitRobotData.getRobotNumber());
            Robot robotRecord = latestRobotList.stream()
                    .filter(r -> r.getId().equals(transmitRobotData.getRobotId())).findFirst().orElse(null);
            if (Objects.nonNull(robotRecord)) {
                robot.setStatus(robotRecord.getStatus());
            } else {
                robot.setStatus(DeviceStatusEnum.REMOVE.getValue());
            }
            robotList.add(robot);
        }
        return robotList;
    }

    @Override
    public List<Robot> listTodayClearingTransmitRobotByPort(Integer port) {
        // 最新机器人列表（最新的数据）
        List<Robot> latestRobotList = new ArrayList<>(0);
        // 根据端口号查询机器人分区ID列表
        List<Long> robotRegionalIdList = baseMapper.listRobotRegionalIdByPort(port);
        if (CollUtil.isNotEmpty(robotRegionalIdList)) {
            // 当天的起始时间
            LocalDateTime now = CommonUtil.getZoneTime();
            LocalDateTime beginTime = now.withHour(0).withMinute(0).withSecond(0);
            LocalDateTime endTime = now.withHour(23).withMinute(59).withSecond(59);
            // 查询今日清扫的机器人列表
            List<Robot> todayClearingRobotList = robotService.listTransmitClearingRobot(robotRegionalIdList, beginTime, endTime);
            // 查询所有的机器人列表
            latestRobotList = robotService.listTransmitRobot(robotRegionalIdList);
            // 对今日清扫的机器人 status 赋值为 运行
            for (Robot robot : latestRobotList) {
                Robot todayClearingRobot = todayClearingRobotList.stream()
                        .filter(r -> r.getId().equals(robot.getId())).findFirst().orElse(null);
                if (Objects.nonNull(todayClearingRobot)) {
                    robot.setStatus(DeviceStatusEnum.RUN.getValue());
                } else {
                    robot.setStatus(DeviceStatusEnum.NORMAL.getValue());
                }
            }
        }
        // 根据端口查询转发通道机器人数据（上一次的数据）
        List<TransmitRobotData> transmitRobotDataList = transmitRobotDataService.list(Wrappers.<TransmitRobotData>lambdaQuery()
                .eq(TransmitRobotData::getPort, port));
        // 列表为空，返回最新的数据
        if (CollUtil.isEmpty(transmitRobotDataList)) {
            return latestRobotList;
        }
        // 匹配今日清扫率
        List<Robot> robotList = new ArrayList<>(transmitRobotDataList.size());
        Robot robot;
        for (TransmitRobotData transmitRobotData : transmitRobotDataList) {
            robot = new Robot();
            robot.setId(transmitRobotData.getId());
            robot.setRobotNumber(transmitRobotData.getRobotNumber());
            Robot robotRecord = latestRobotList.stream()
                    .filter(r -> r.getId().equals(transmitRobotData.getRobotId())).findFirst().orElse(null);
            if (Objects.nonNull(robotRecord)) {
                robot.setStatus(robotRecord.getStatus());
            } else {
                robot.setStatus(DeviceStatusEnum.REMOVE.getValue());
            }
            robotList.add(robot);
        }
        return robotList;
    }

}

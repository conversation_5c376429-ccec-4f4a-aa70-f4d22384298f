package org.springblade.thingcom.device.service.impl;

import org.springblade.thingcom.core.mybatisPlus.impl.BaseServiceImpl;
import org.springblade.thingcom.device.mapper.RobotTransmitRegionalMapper;
import org.springblade.thingcom.device.entity.RobotTransmitRegional;
import org.springblade.thingcom.device.service.RobotTransmitRegionalService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 机器人转发通道和机器人分区关系服务实现类
 *
 * <AUTHOR>
 * @since 2024-07-29 14:34:51
 */
@Service
public class RobotTransmitRegionalServiceImpl extends BaseServiceImpl<RobotTransmitRegionalMapper, RobotTransmitRegional> implements RobotTransmitRegionalService {

    @Override
    public void validateData(RobotTransmitRegional entity) {
        // 检查名称是否存在
        //Long count = baseMapper.selectCount(Wrappers.<TrRobotTransmitRegional>lambdaQuery()
        //        .eq(TrRobotTransmitRegional::getName, entity.getName())
        //        // 编辑时，排除自己
        //        .ne(entity.getId() != null, TrRobotTransmitRegional::getId, entity.getId()));
        //Assert.isTrue(count == 0, StrUtil.format("名称[{}]已经存在", entity.getName()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveData(RobotTransmitRegional entity) {
        // 新增机器人转发通道和机器人分区关系
        boolean ret = save(entity);
        return ret;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateData(RobotTransmitRegional entity) {
        // 修改机器人转发通道和机器人分区关系
        boolean ret = updateById(entity);
        return ret;
    }

}

package org.springblade.thingcom.device.service;

import org.springblade.thingcom.core.mybatisPlus.IBaseService;
import org.springblade.thingcom.device.entity.TrackerModel;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 跟踪器型号服务接口
 *
 * <AUTHOR>
 * @since 2025-02-14 16:07:00
 */
public interface TrackerModelService extends IBaseService<TrackerModel> {

    /**
     * 检查数据合法性
     *
     * @param entity 实体对象
     * @return
     */
    void validateData(TrackerModel entity);

    /**
     * 删除跟踪器型号
     *
     * @param idList
     * @return
     */
    boolean removeData(List<Long> idList);

    /**
     * 导入跟踪器型号
     *
     * @param file
     * @return
     */
    String importData(MultipartFile file);
}

package org.springblade.thingcom.device.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springblade.common.constant.CommonConstant;
import org.springblade.thingcom.base.entity.SystemSet;
import org.springblade.thingcom.base.service.SystemSetService;
import org.springblade.thingcom.core.excel.utils.ThingcomExcelUtil;
import org.springblade.thingcom.core.mybatisPlus.impl.BaseServiceImpl;
import org.springblade.thingcom.device.entity.Tracker;
import org.springblade.thingcom.device.entity.TrackerModel;
import org.springblade.thingcom.device.mapper.TrackerModelMapper;
import org.springblade.thingcom.device.service.TrackerModelService;
import org.springblade.thingcom.device.service.TrackerService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;

/**
 * 跟踪器型号服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-14 16:07:00
 */
@Service
@AllArgsConstructor
public class TrackerModelServiceImpl extends BaseServiceImpl<TrackerModelMapper, TrackerModel> implements TrackerModelService {

    private final TrackerService trackerService;
    private final SystemSetService systemSetService;

    @Override
    public void validateData(TrackerModel entity) {
        // 检查名称是否存在
        Long count = baseMapper.selectCount(Wrappers.<TrackerModel>lambdaQuery()
                .eq(TrackerModel::getModelName, entity.getModelName())
                // 编辑时，排除自己
                .ne(entity.getId() != null, TrackerModel::getId, entity.getId()));
        Assert.isTrue(count == 0, StrUtil.format("型号名称[{}]已经存在", entity.getModelName()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeData(List<Long> idList) {
        // 检查是否有跟踪器使用了该型号
        long count = trackerService.count(Wrappers.<Tracker>lambdaQuery()
                .eq(Tracker::getIsDeleted, 0)
                .in(Tracker::getTrackerModelId, idList));
        Assert.isTrue(count == 0, "型号已被跟踪器使用，不能删除");
        // 删除型号
        return removeBatchByIds(idList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importData(MultipartFile file) {
        if (null == file) {
            throw new IllegalArgumentException("请先选择导入文件");
        }
        if (file.getSize() > (1024 * 1024 * CommonConstant.FILE_SIZE)) {
            throw new IllegalArgumentException(StrUtil.format("文件大小不能超过{}M!", CommonConstant.FILE_SIZE));
        }
        // 导入结果
        StringBuilder errorReasonStringBuilder = new StringBuilder();
        int successCount = 0;
        int failCount = 0;
        // 重复的支架型号列表
        Set<String> modelNameRepeatSet = new HashSet<>();
        // 系统设置
        SystemSet systemSet = null;
        // 数据列表
        List<TrackerModel> dataList = new ArrayList<>();
        try (InputStream inputStream = file.getInputStream(); Workbook workbook = new XSSFWorkbook(inputStream)) {
            // 获取第一个工作表
            Sheet sheet = workbook.getSheetAt(0);
            // 行号（第一行是桥架长度数据，第二行是标题，数据从第三行开始）
            int rowNum = 3;
            // 遍历每一行
            for (Row row : sheet) {
                // 第一行数据：起始桥架长度、桥架长度、返回桥架长度
                if (row.getRowNum() == 0) {
                    // 获取系统设置
                    systemSet = getSystemSet(row);
                    continue;
                }
                // 第二行数据：支架型号数据的标题，需跳过
                if (row.getRowNum() == 1) {
                    continue;
                }
                // 获取支架型号数据
                TrackerModel trackerModel = getTrackerModel(row, rowNum, errorReasonStringBuilder, modelNameRepeatSet);
                if (Objects.isNull(trackerModel)) {
                    failCount++;
                } else {
                    dataList.add(trackerModel);
                    successCount++;
                }
                rowNum++;
            }
        } catch (IOException e) {
            throw new IllegalArgumentException("文件解析失败{}", e);
        }
        if (Objects.nonNull(systemSet)) {
            systemSetService.updateData(systemSet);
        }
        if (CollUtil.isNotEmpty(dataList)) {
            successCount = dataList.size();
            // 批量插入数据
            saveBatch(dataList);
        }
        if (StrUtil.isBlank(errorReasonStringBuilder)) {
            return "";
        }
        String errorReason = errorReasonStringBuilder.deleteCharAt(errorReasonStringBuilder.length() - 1).toString();
        return StrUtil.format("成功导入{}条数据，失败{}条数据，失败原因：{}", successCount, failCount, errorReason);
    }

    /**
     * 获取系统设置
     *
     * @param row 行对象
     * @return 系统设置
     */
    private static SystemSet getSystemSet(Row row) {
        // 起始桥架长度
        Cell startingBridgeLengthCell = row.getCell(1);
        Assert.isTrue(Objects.nonNull(startingBridgeLengthCell)
                && startingBridgeLengthCell.getCellType() != CellType.BLANK, "起始桥架长度不能为空");
        String startingBridgeLengthStr = ThingcomExcelUtil.getCellValueAsString(startingBridgeLengthCell);
        Assert.isTrue(NumberUtil.isNumber(startingBridgeLengthStr), "起始桥架长度必须为数字");
        BigDecimal startingBridgeLength = NumberUtil.toBigDecimal(startingBridgeLengthStr);
        Assert.isTrue(startingBridgeLength.compareTo(BigDecimal.ZERO) >= 0
                && startingBridgeLength.compareTo(new BigDecimal(99999)) <= 0, "起始桥架长度必须在0~99999之间");
        // 桥架长度
        Cell bridgeLengthCell = row.getCell(3);
        Assert.isTrue(Objects.nonNull(bridgeLengthCell)
                && bridgeLengthCell.getCellType() != CellType.BLANK, "桥架长度不能为空");
        String bridgeLengthStr = ThingcomExcelUtil.getCellValueAsString(bridgeLengthCell);
        Assert.isTrue(NumberUtil.isNumber(bridgeLengthStr), "桥架长度必须为数字");
        BigDecimal bridgeLength = NumberUtil.toBigDecimal(bridgeLengthStr);
        Assert.isTrue(bridgeLength.compareTo(BigDecimal.ZERO) >= 0
                && bridgeLength.compareTo(new BigDecimal(99999)) <= 0, "桥架长度必须在0~99999之间");
        // 返回桥架长度
        Cell returnBridgeLengthCell = row.getCell(5);
        Assert.isTrue(Objects.nonNull(returnBridgeLengthCell)
                && returnBridgeLengthCell.getCellType() != CellType.BLANK, "返回桥架长度不能为空");
        String returnBridgeLengthStr = ThingcomExcelUtil.getCellValueAsString(returnBridgeLengthCell);
        Assert.isTrue(NumberUtil.isNumber(returnBridgeLengthStr), "返回桥架长度必须为数字");
        BigDecimal returnBridgeLength = NumberUtil.toBigDecimal(returnBridgeLengthStr);
        Assert.isTrue(returnBridgeLength.compareTo(BigDecimal.ZERO) >= 0
                && returnBridgeLength.compareTo(new BigDecimal(99999)) <= 0, "返回桥架长度必须在0~99999之间");
        // 更新系统设置
        SystemSet systemSet = new SystemSet();
        systemSet.setStartBridgeLength(startingBridgeLength);
        systemSet.setBridgeLength(bridgeLength);
        systemSet.setReturnBridgeLength(returnBridgeLength);
        return systemSet;
    }

    /**
     * 获取支架型号数据
     *
     * @param row                      行对象
     * @param rowNum                   行号
     * @param errorReasonStringBuilder 错误原因
     * @param modelNameRepeatSet       重复的支架型号列表
     * @return 支架型号数据
     */
    private TrackerModel getTrackerModel(Row row, int rowNum, StringBuilder errorReasonStringBuilder, Set<String> modelNameRepeatSet) {
        // 支架型号
        Cell modelNameCell = row.getCell(0);
        if (Objects.isNull(modelNameCell) || modelNameCell.getCellType() == CellType.BLANK) {
            errorReasonStringBuilder.append(String.format("A%d：支架型号不能为空；", rowNum));
            return null;
        }
        String modelName = ThingcomExcelUtil.getCellValueAsString(modelNameCell);
        if (modelName.length() > 50) {
            errorReasonStringBuilder.append(String.format("A%d：支架型号长度不能超过50个字符；", rowNum));
            return null;
        }
        if (modelNameRepeatSet.contains(modelName)) {
            errorReasonStringBuilder.append(String.format("A%d：支架型号在文件中重复；", rowNum));
            return null;
        }
        // 添加型号，防止重复
        modelNameRepeatSet.add(modelName);
        // 检查是否已存在
        TrackerModel one = getOne(Wrappers.<TrackerModel>lambdaQuery().eq(TrackerModel::getModelName, modelName));
        if (Objects.nonNull(one)) {
            errorReasonStringBuilder.append(String.format("A%d：支架型号已存在，请核对数据；", rowNum));
            return null;
        }
        // 支架型号长度
        Cell modelLengthCell = row.getCell(1);
        if (Objects.isNull(modelLengthCell) || modelLengthCell.getCellType() == CellType.BLANK) {
            errorReasonStringBuilder.append(String.format("B%d：长度不能为空；", rowNum));
            return null;
        }
        String modelLengthStr = ThingcomExcelUtil.getCellValueAsString(modelLengthCell);
        if (!NumberUtil.isNumber(modelLengthStr)) {
            errorReasonStringBuilder.append(String.format("B%d：长度必须为数字；", rowNum));
            return null;
        }
        BigDecimal modelLength = NumberUtil.toBigDecimal(modelLengthStr);
        if (modelLength.compareTo(BigDecimal.ZERO) < 0 || modelLength.compareTo(new BigDecimal(99999)) > 0) {
            errorReasonStringBuilder.append(String.format("B%d：长度必须在0~99999之间；", rowNum));
            return null;
        }
        // 备注
        Cell remarkCell = row.getCell(2);
        String remark = null;
        if (Objects.nonNull(remarkCell)) {
            remark = ThingcomExcelUtil.getCellValueAsString(remarkCell);
            if (remark.length() > 200) {
                errorReasonStringBuilder.append(String.format("C%d：备注长度不能超过200个字符；", rowNum));
                return null;
            }
        }
        // 设置属性值
        TrackerModel trackerModel = new TrackerModel();
        trackerModel.setModelName(modelName);
        trackerModel.setModelLength(modelLength);
        trackerModel.setRemark(remark);
        return trackerModel;
    }

}

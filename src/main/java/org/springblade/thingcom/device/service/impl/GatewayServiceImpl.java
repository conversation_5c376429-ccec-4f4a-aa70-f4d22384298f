package org.springblade.thingcom.device.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.CommonUtil;
import org.springblade.common.utils.NumberUtil;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.tool.utils.Func;
import org.springblade.thingcom.communication.utils.HttpClientUtil;
import org.springblade.thingcom.core.chirpStack.cache.CommonCache;
import org.springblade.thingcom.core.chirpStack.constant.ApiConstant;
import org.springblade.thingcom.core.chirpStack.constant.ParamConstant;
import org.springblade.thingcom.core.chirpStack.dto.NetworkServerDTO;
import org.springblade.thingcom.core.chirpStack.service.NetworkServerService;
import org.springblade.thingcom.core.chirpStack.utils.ApiUtil;
import org.springblade.thingcom.core.chirpStack.vo.InternalLoginVO;
import org.springblade.thingcom.core.chirpStack.vo.NetworkServerVO;
import org.springblade.thingcom.core.chirpStack.vo.PageVO;
import org.springblade.thingcom.device.async.AsyncBusinessDataHandler;
import org.springblade.thingcom.device.dto.GatewayDTO;
import org.springblade.thingcom.device.entity.Gateway;
import org.springblade.thingcom.device.enums.GatewayStatusEnum;
import org.springblade.thingcom.device.excel.GatewayImportExcel;
import org.springblade.thingcom.device.mapper.GatewayMapper;
import org.springblade.thingcom.device.service.GatewayService;
import org.springblade.thingcom.device.utils.GatewayUtil;
import org.springblade.thingcom.device.vo.GatewayConfigCommonVO;
import org.springblade.thingcom.device.wrapper.GatewayWrapper;
import org.springblade.thingcom.operationMaintenance.vo.GatewayRunStatisVO;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【tb_gateway(通讯网关)】的数据库操作Service实现
 * @createDate 2024-04-16 11:28:49
 */
@Slf4j
@Service
@AllArgsConstructor
public class GatewayServiceImpl extends ServiceImpl<GatewayMapper, Gateway> implements GatewayService {

    private final AsyncBusinessDataHandler asyncBusinessDataHandler;
    private final NetworkServerService networkServerService;

    @Override
    public String validateData(Gateway entity) {
        // 检查网关编号是否唯一
        Long count = baseMapper.selectCount(Wrappers.<Gateway>lambdaQuery()
                .eq(Gateway::getGatewayNumber, entity.getGatewayNumber())
                // 编辑时排除自己
                .ne(entity.getId() != null, Gateway::getId, entity.getId()));
        if (count > 0L) {
            return entity.getGatewayNumber() + "网关编号已经存在。";
        }
        // 检查IP是否唯一
        Long count1 = baseMapper.selectCount(Wrappers.<Gateway>lambdaQuery()
                .eq(Gateway::getEui, entity.getEui())
                // 编辑时排除自己
                .ne(entity.getId() != null, Gateway::getId, entity.getId()));
        if (count1 > 0L) {
            return entity.getEui() + "IP已经存在。";
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveCommunicationGateway(Gateway gateway) {
        // 向网关发送请求获取EUI
        String url = GatewayUtil.getGatewayApiUrl(gateway.getIp());
        String params = "type=loraserver";
        String result;
        try {
            result = HttpClientUtil.doPost(url, params, null);
            // 去除注释
            result = result.replaceAll("/\\*.*?\\*/", "");
        } catch (Exception e) {
            log.error("向网关发送请求获取EUI失败", e);
            throw new ServiceException("向网关发送请求获取EUI失败");
        }
        // 获取网关公共配置
        GatewayConfigCommonVO gatewayConfigCommonVO = GatewayUtil.getGatewayConfigCommonVO(result);
        BigDecimal frequencyBandStart = gatewayConfigCommonVO.getFrequencyBandStart();
        BigDecimal frequencyBandEnd = gatewayConfigCommonVO.getFrequencyBandEnd();
        String transmissionPower = gatewayConfigCommonVO.getTransmissionPower();
        String eui = gatewayConfigCommonVO.getEui();
        // 保存网关信息
        gateway.setEui(eui);
        gateway.setTransmissionPower(transmissionPower);
        // 检查数据合法性
        String errMsg = validateData(gateway);
        if (errMsg != null) {
            throw new ServiceException(errMsg);
        }
        frequencyBandStart = frequencyBandStart.subtract(new BigDecimal("300000")).divide(new BigDecimal("1000000"));
        frequencyBandEnd = frequencyBandEnd.add(new BigDecimal("1100000")).divide(new BigDecimal("1000000"));
        gateway.setFrequencyBand(frequencyBandStart + "-" + frequencyBandEnd);
        gateway.setTime(CommonUtil.getZoneTime());
        boolean save = save(gateway);
        // 获取 token
        InternalLoginVO loginVo = CommonCache.getToken();
        Assert.notNull(loginVo, "登录失败");
        // 获取请求地址
        String apiUrl = ApiUtil.getApiHost() + ApiConstant.GATEWAYS;

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add(ParamConstant.HEADER_AUTHORIZATION, loginVo.getJwt());

        // 设置请求实体，包含 JSON 数据和请求头
        HttpEntity<String> requestEntity = new HttpEntity<>(createGateway(loginVo, gateway).toString(), headers);

        // 发送 POST 请求
        RestTemplate restTemplate = new RestTemplate();
        String response = restTemplate.postForObject("http://" + apiUrl, requestEntity, String.class);
        log.info("创建网关返回结果：{}", response);
        return save;
    }

    @Override
    //@Transactional(rollbackFor = Exception.class)
    public void importData(List<GatewayImportExcel> list) throws IOException {
        for (GatewayImportExcel gatewayImportExcel : list) {
            Gateway gateway = new Gateway();
            gateway.setGatewayNumber(gatewayImportExcel.getGatewayNumber());
            gateway.setIp(gatewayImportExcel.getIp());
            saveCommunicationGateway(gateway);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(Long id) {
        // 获取网关信息
        Gateway gateway = getById(id);
        Assert.notNull(gateway, "未找到网关信息");
        // 删除网关
        removeById(id);
        // 获取 token
        InternalLoginVO loginVo = CommonCache.getToken();
        Assert.notNull(loginVo, "登录失败");
        // 获取请求地址
        String apiUrl = ApiUtil.getApiHost() + ApiConstant.GATEWAYS;
        HttpClientUtil.doDelete("http://" + apiUrl + "/" + gateway.getEui(), loginVo.getJwt());
        // 同时删除和机器人相关的数据
        asyncBusinessDataHandler.deleteGatewayRelationData(Func.toLongList(id.toString()));
        return true;
    }

    @Override
    public String selectMessageLog(String eui) {
        InternalLoginVO loginVo = CommonCache.getToken();
        Assert.notNull(loginVo, "登录失败");
        String url = ApiUtil.getApiHost() + ApiConstant.GATEWAYS + "/" + eui + "/frames";
        String result = HttpClientUtil.doGet("http://" + url, loginVo.getJwt());
        return NumberUtil.base64ToHexStr(result);
    }

    public JsonObject createGateway(InternalLoginVO loginVo, Gateway communicationGateway) {
        JsonObject location = new JsonObject();
        location.addProperty("accuracy", 0);
        location.addProperty("altitude", 0);
        location.addProperty("latitude", 0);
        location.addProperty("longitude", 0);
        location.addProperty("source", "UNKNOWN");
        // 获取网关配置id
        String gatewayProfileUrl = ApiUtil.getApiHost() + ApiConstant.GATEWAY_PROFILES;
        JsonObject gatewayProfileID = new JsonObject();
        gatewayProfileID.addProperty("limit", 1);
        String requestParam = HttpClientUtil.convertJsonObjectToString(gatewayProfileID);
        String gatewayProfile = HttpClientUtil.doGet("http://" + gatewayProfileUrl + "?" + requestParam, loginVo.getJwt());
        String gatewayId = JsonParser.parseString(gatewayProfile).getAsJsonObject()
                .getAsJsonArray("result").get(0).getAsJsonObject().get("id").getAsString();

        // 获取服务配置id
        String serviceProfileUrl = ApiUtil.getApiHost() + ApiConstant.SERVICE_PROFILES;
        JsonObject serviceProfileID = new JsonObject();
        serviceProfileID.addProperty("limit", 1);
        requestParam = HttpClientUtil.convertJsonObjectToString(serviceProfileID);
        String serviceProfile = HttpClientUtil.doGet("http://" + serviceProfileUrl + "?" + requestParam, loginVo.getJwt());
        String serviceId = JsonParser.parseString(serviceProfile).getAsJsonObject()
                .getAsJsonArray("result").get(0).getAsJsonObject().get("id").getAsString();

        // 获取应用
        NetworkServerDTO networkServerDTO = new NetworkServerDTO();
        networkServerDTO.setJwt(loginVo.getJwt());
        PageVO<NetworkServerVO> networkServerPage = networkServerService.page(networkServerDTO);
        String networkServerID = "1";
        if (Objects.nonNull(networkServerPage) && CollUtil.isNotEmpty(networkServerPage.getResult())) {
            // 设置 applicationID（默认使用第一个）
            networkServerID = networkServerPage.getResult().get(0).getId();
        }

        // 封装返回数据
        JsonObject gateway = new JsonObject();
        gateway.addProperty("description", communicationGateway.getEui());
        gateway.addProperty("discoveryEnabled", true);
        gateway.addProperty("gatewayProfileID", gatewayId);
        gateway.addProperty("id", communicationGateway.getEui());
        gateway.add("location", location);
        gateway.add("metadata", new JsonObject());
        gateway.addProperty("name", communicationGateway.getEui());
        gateway.addProperty("networkServerID", networkServerID);
        gateway.addProperty("organizationID", "1");
        gateway.addProperty("serviceProfileID", serviceId);
        gateway.add("tags", new JsonObject());

        JsonObject json = new JsonObject();
        json.add("gateway", gateway);

        return json;
    }

    public static void main(String[] args) throws IOException {
        String url = "http://*************:8080/api/gateways";
        // 封装返回数据
        JsonObject gateway = new JsonObject();
        JsonObject location = new JsonObject();
        location.addProperty("accuracy", 0);
        location.addProperty("altitude", 0);
        location.addProperty("latitude", 0);
        location.addProperty("longitude", 0);
        location.addProperty("source", "UNKNOWN");
        gateway.addProperty("description", "082716FFFE6A703A");
        gateway.addProperty("discoveryEnabled", true);
        gateway.addProperty("gatewayProfileID", "98f93727-29fe-49de-a549-b6363e56f1ea");
        gateway.addProperty("id", "082716FFFE6A703A");
        gateway.add("location", location);
        gateway.add("metadata", new JsonObject());
        gateway.addProperty("name", "082716FFFE6A703A");
        gateway.addProperty("networkServerID", "2");
        gateway.addProperty("organizationID", "1");
        gateway.addProperty("serviceProfileID", "e5bc242a-2866-48ad-a3d3-2c7bfe8ea8df");
        gateway.add("tags", new JsonObject());

        JsonObject json = new JsonObject();
        json.add("gateway", gateway);
        //String s = HttpClientUtils.doPost(url, json.toString(), "token");
        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add(ParamConstant.HEADER_AUTHORIZATION, "token");

        // 设置请求实体，包含 JSON 数据和请求头
        HttpEntity<String> requestEntity = new HttpEntity<>(json.toString(), headers);

        // 发送 POST 请求
        RestTemplate restTemplate = new RestTemplate();
        String response = restTemplate.postForObject(url, requestEntity, String.class);
        System.out.println(response);
    }

    @Override
    public GatewayRunStatisVO statistics(GatewayDTO dto) {
        GatewayRunStatisVO vo = new GatewayRunStatisVO();
        // 查询网关
        LambdaQueryWrapper<Gateway> wrapper = GatewayWrapper.build().setQueryParam(dto);
        List<Gateway> list = list(wrapper);
        if (CollUtil.isNotEmpty(list)) {
            vo.setTotalCount((long) list.size());
            Map<Integer, Long> statusCountMap = list.stream()
                    .collect(Collectors.groupingBy(Gateway::getStatus, Collectors.counting()));
            vo.setNormalCount(statusCountMap.getOrDefault(GatewayStatusEnum.NORMAL.getValue(), 0L));
            vo.setOfflineCount(statusCountMap.getOrDefault(GatewayStatusEnum.OFFLINE.getValue(), 0L));
            vo.setAlarmCount(statusCountMap.getOrDefault(GatewayStatusEnum.ALARM.getValue(), 0L));
        }
        return vo;
    }
}

package org.springblade.thingcom.device.service;

import org.springblade.thingcom.core.mybatisPlus.IBaseService;
import org.springblade.thingcom.device.entity.ClearingPathTracker;

import java.util.List;

/**
 * 清扫路径跟踪器关系服务接口
 *
 * <AUTHOR>
 * @since 2024-01-09 14:40:37
 */
public interface ClearingPathTrackerService extends IBaseService<ClearingPathTracker> {

    /**
     * 根据清扫路径ID删除清扫路径跟踪器关系
     *
     * @param clearingPathIdList 清扫路径ID列表
     */
    void deleteByClearingPathId(List<Long> clearingPathIdList);
}

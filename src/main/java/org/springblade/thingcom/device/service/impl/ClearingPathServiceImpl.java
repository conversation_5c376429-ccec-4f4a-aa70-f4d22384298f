package org.springblade.thingcom.device.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.PasswordUtil;
import org.springblade.core.tool.support.Kv;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.thingcom.base.entity.SystemSet;
import org.springblade.thingcom.base.mapper.SystemSetMapper;
import org.springblade.thingcom.core.mybatisPlus.impl.BaseServiceImpl;
import org.springblade.thingcom.device.dto.ClearingPathDTO;
import org.springblade.thingcom.device.dto.ClearingPathDeleteDTO;
import org.springblade.thingcom.device.dto.ClearingPathSaveDTO;
import org.springblade.thingcom.device.entity.ClearingPath;
import org.springblade.thingcom.device.entity.ClearingPathTracker;
import org.springblade.thingcom.device.enums.BindStatusEnum;
import org.springblade.thingcom.device.excel.ClearingPathExcel;
import org.springblade.thingcom.device.mapper.ClearingPathMapper;
import org.springblade.thingcom.device.mapper.ClearingPathTrackerMapper;
import org.springblade.thingcom.device.mapper.TrackerMapper;
import org.springblade.thingcom.device.service.ClearingPathService;
import org.springblade.thingcom.device.vo.ClearingPathVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 清扫路径服务实现类
 *
 * <AUTHOR>
 * @since 2023-12-14 09:37:22
 */
@Service
@AllArgsConstructor
public class ClearingPathServiceImpl extends BaseServiceImpl<ClearingPathMapper, ClearingPath> implements ClearingPathService {

    private final TrackerMapper trackerMapper;
    private final ClearingPathTrackerMapper clearingPathTrackerMapper;
    private final SystemSetMapper systemSetMapper;

    @Override
    public String validateData(ClearingPath entity) {
        // 检查路径编码是否存在
        Long count = baseMapper.selectCount(Wrappers.<ClearingPath>lambdaQuery()
                .eq(ClearingPath::getPathNumber, entity.getPathNumber())
                // 编辑时排除自己
                .ne(entity.getId() != null, ClearingPath::getId, entity.getId()));
        if (count > 0L) {
            return entity.getPathNumber() + "路径编码已经存在。";
        }
        return null;
    }

    @Override
    public IPage<ClearingPathVO> page(IPage<ClearingPath> page, ClearingPathDTO dto) {
        return baseMapper.page(page, dto);
    }

    @Override
    public IPage<ClearingPathVO> pageTrackerBind(IPage<ClearingPath> page, ClearingPathDTO dto) {
        // 查看对应清扫路径下绑定的跟踪器
        if (Objects.nonNull(dto.getId()) && BindStatusEnum.BIND.getValue().equals(dto.getStatus())) {
            return trackerMapper.pageTrackerParam(page, dto);
        } else {
            // 默认查看未绑定的跟踪器
            if (Objects.isNull(dto.getStatus())) {
                dto.setStatus(BindStatusEnum.UNBIND.getValue());
            }
            return trackerMapper.pageTrackerBind(page, dto);
        }
    }

    @Override
    public List<ClearingPathVO> listTrackerBind(ClearingPathDTO dto) {
        // 查看对应清扫路径下绑定的跟踪器
        if (Objects.nonNull(dto.getId()) && BindStatusEnum.BIND.getValue().equals(dto.getStatus())) {
            return trackerMapper.listTrackerParam(dto);
        } else {
            // 默认查看未绑定的跟踪器
            if (Objects.isNull(dto.getStatus())) {
                dto.setStatus(BindStatusEnum.UNBIND.getValue());
            }
            return trackerMapper.listTrackerBind(dto);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveData(ClearingPathSaveDTO dto) {
        ClearingPath entity = BeanUtil.copyProperties(dto, ClearingPath.class);
        // 检查数据合法性
        String errMsg = validateData(entity);
        Assert.isNull(errMsg, errMsg);
        return save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateData(ClearingPathSaveDTO dto) {
        ClearingPath entity = BeanUtil.copyProperties(dto, ClearingPath.class);
        // 检查数据合法性
        String errMsg = validateData(entity);
        Assert.isNull(errMsg, errMsg);
        return updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteData(ClearingPathDeleteDTO dto) {
        ClearingPath record = getById(dto.getId());
        Assert.notNull(record, "清扫路径不存在");
        // 查看绑定状态
        if (BindStatusEnum.BIND.getValue().equals(record.getStatus())) {
            // 密码校验
            SystemSet systemSet = systemSetMapper.selectOne(Wrappers.<SystemSet>lambdaQuery()
                    .orderByDesc(SystemSet::getId).last("limit 1"));

            //Assert.isTrue(Objects.nonNull(systemSet) && StrUtil.isNotBlank(systemSet.getClearingPathDeletePassword())
            //        , "系统密码未设定，请联系管理员");
            //String passwordRecord = PasswordUtil.base64Decode(systemSet.getClearingPathDeletePassword());
            //Assert.isTrue(passwordRecord.equals(dto.getPassword()), "密码不正确");

            Assert.isTrue(Objects.nonNull(systemSet) && StrUtil.isNotBlank(systemSet.getControlPassword())
                    , "系统密码未设定，请联系管理员");
            String controlPassword = PasswordUtil.base64Decode(systemSet.getControlPassword());
            Assert.isTrue(controlPassword.equals(dto.getPassword()), "密码不正确");
        }
        // 删除清扫路径跟踪器关系数据
        clearingPathTrackerMapper.delete(Wrappers.<ClearingPathTracker>lambdaQuery()
                .eq(ClearingPathTracker::getClearingPathId, dto.getId()));
        // 删除清扫路径
        return removeById(dto.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importData(List<ClearingPathExcel.DataTemplate> list) {
        List<String> nameList = new ArrayList<>();
        List<ClearingPath> saveList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            ClearingPathExcel.DataTemplate excel = list.get(i);
            ClearingPath entity = new ClearingPath();
            // 检验路径编号在表格内是否重复
            Assert.isTrue(!nameList.contains(excel.getPathNumber())
                    , String.format("文件第%d行，清扫路径编号[%s]重复！", i + 2, excel.getPathNumber()));
            nameList.add(excel.getPathNumber());
            entity.setPathNumber(excel.getPathNumber());
            entity.setRemark(excel.getRemark());
            // 检验路径编号在数据库中是否重复
            String msg = validateData(entity);
            Assert.isNull(msg, String.format("文件第%d行，清扫路径编号[%s]已存在！", i + 2, excel.getPathNumber()));
            saveList.add(entity);
        }
        if (CollUtil.isNotEmpty(saveList)) {
            saveBatch(saveList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean bindTracker(ClearingPathSaveDTO dto) {
        // 绑定状态，需要覆盖添加，未绑定状态，直接添加
        if (BindStatusEnum.BIND.getValue().equals(dto.getStatus())) {
            // 覆盖添加
            clearingPathTrackerMapper.delete(Wrappers.<ClearingPathTracker>lambdaQuery()
                    .eq(ClearingPathTracker::getClearingPathId, dto.getId()));
        }
        // 如果跟踪器列表为空，则直接返回
        if (StrUtil.isBlank(dto.getTrackerIds())) {
            return true;
        }
        // 跟踪器列表
        List<Long> trackerIdList = Func.toLongList(dto.getTrackerIds());
        List<ClearingPathTracker> saveList = trackerIdList.stream().map(m -> {
            ClearingPathTracker clearingPathTracker = new ClearingPathTracker();
            clearingPathTracker.setClearingPathId(dto.getId());
            clearingPathTracker.setTrackerId(m);
            return clearingPathTracker;
        }).collect(Collectors.toList());
        // 新增清扫路径跟踪器关系数据
        clearingPathTrackerMapper.insertBatchSomeColumn(saveList);
        return true;
    }

    @Override
    public List<Kv> selectList(Integer status) {
        // 查询清扫路径
        return list(Wrappers.<ClearingPath>lambdaQuery().select(ClearingPath::getId, ClearingPath::getPathNumber)
                .eq(Objects.nonNull(status), ClearingPath::getStatus, status)
                .orderByAsc(ClearingPath::getPathNumber))
                .stream()
                .map(obj -> Kv.create()
                        .set(CommonConstant.LABEL, obj.getPathNumber())
                        .set(CommonConstant.VALUE, obj.getId().toString()))
                .collect(Collectors.toList());
    }

    @Override
    public ClearingPath getClearingPathByTrackerId(Long trackerId) {
        ClearingPathTracker clearingPathTracker = clearingPathTrackerMapper.selectOne(Wrappers.<ClearingPathTracker>lambdaQuery()
                .eq(ClearingPathTracker::getTrackerId, trackerId).last("limit 1"));
        if (Objects.nonNull(clearingPathTracker)) {
            return getById(clearingPathTracker.getClearingPathId());
        }
        return null;
    }

}

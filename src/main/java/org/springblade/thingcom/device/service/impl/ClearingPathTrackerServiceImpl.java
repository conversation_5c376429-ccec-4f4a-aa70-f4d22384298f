package org.springblade.thingcom.device.service.impl;

import org.springblade.thingcom.core.mybatisPlus.impl.BaseServiceImpl;
import org.springblade.thingcom.device.entity.ClearingPathTracker;
import org.springblade.thingcom.device.mapper.ClearingPathTrackerMapper;
import org.springblade.thingcom.device.service.ClearingPathTrackerService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 清扫路径跟踪器关系服务实现类
 *
 * <AUTHOR>
 * @since 2024-01-09 14:40:37
 */
@Service
public class ClearingPathTrackerServiceImpl extends BaseServiceImpl<ClearingPathTrackerMapper, ClearingPathTracker> implements ClearingPathTrackerService {

    @Override
    public void deleteByClearingPathId(List<Long> clearingPathIdList) {
        baseMapper.deleteByClearingPathId(clearingPathIdList);
    }
}

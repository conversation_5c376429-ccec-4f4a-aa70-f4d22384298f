package org.springblade.thingcom.device.controller;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import io.jsonwebtoken.lang.Collections;
import lombok.AllArgsConstructor;
import org.springblade.common.utils.PasswordUtil;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.thingcom.base.entity.SystemSet;
import org.springblade.thingcom.base.service.SystemSetService;
import org.springblade.thingcom.communication.utils.HttpClientUtil;
import org.springblade.thingcom.core.excel.handler.AutoColumnWidthHandler;
import org.springblade.thingcom.core.excel.handler.ImportHeaderCellHandler;
import org.springblade.thingcom.core.excel.handler.ThingcomSheetHandler;
import org.springblade.thingcom.core.excel.utils.ThingcomExcelUtil;
import org.springblade.thingcom.device.dto.GatewayDTO;
import org.springblade.thingcom.device.dto.GatewayFrequencyBandDTO;
import org.springblade.thingcom.device.dto.RestartGatewayDTO;
import org.springblade.thingcom.device.entity.Gateway;
import org.springblade.thingcom.device.excel.GatewayDetailExport;
import org.springblade.thingcom.device.excel.GatewayExcel;
import org.springblade.thingcom.device.excel.GatewayExportExcel;
import org.springblade.thingcom.device.excel.GatewayImportExcel;
import org.springblade.thingcom.device.service.GatewayService;
import org.springblade.thingcom.device.utils.GatewayUtil;
import org.springblade.thingcom.device.vo.*;
import org.springblade.thingcom.device.wrapper.GatewayWrapper;
import org.springblade.thingcom.statistic.entity.GatewayOperationLog;
import org.springblade.thingcom.statistic.service.GatewayOperationLogService;
import org.springblade.thingcom.translate.utils.TranslateUtil;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2024/4/16 9:39
 */
@RestController
@AllArgsConstructor
@RequestMapping("/communication")
public class GatewayController extends BladeController {

    private final SystemSetService systemSetService;
    private final GatewayService gatewayService;
    private final GatewayOperationLogService gatewayOperationLogService;

    /**
     * 通讯网关分页查询
     *
     * @param query
     * @param dto
     * @return
     */
    @GetMapping("/page")
    public R<IPage<GatewayVO>> page(Query query, GatewayDTO dto) {
        LambdaQueryWrapper<Gateway> wrapper = GatewayWrapper.build().setQueryParam(dto);
        IPage<Gateway> iPage = gatewayService.page(Condition.getPage(query), wrapper);
        return data(GatewayWrapper.build().pageVO(iPage));
    }

    /**
     * 通讯网关编号下拉
     *
     * @return
     */
    @GetMapping("/gatewayNumberList")
    public R<List<Gateway>> gatewayNumberList(String gatewayNumber) {
        List<Gateway> list = gatewayService.list(
                Wrappers.<Gateway>lambdaQuery()
                        .eq(ObjectUtil.isNotEmpty(gatewayNumber), Gateway::getGatewayNumber, gatewayNumber)
                        .select(Gateway::getId, Gateway::getGatewayNumber, Gateway::getEui));
        return data(list);
    }

    /**
     * 通讯网关新增
     *
     * @param gateway
     * @return
     */
    @PostMapping("/save")
    public R save(@RequestBody Gateway gateway) throws IOException {
        return R.status(gatewayService.saveCommunicationGateway(gateway));
    }

    /**
     * 导出模板
     *
     * @param response
     * @return void
     */
    @GetMapping("/export-template")
    public void exportTemplate(HttpServletResponse response, String lang) throws Exception {
        String sheetName = "设备管理通讯网关";
        String fileName = "设备管理通讯网关导入模板";
        // 判断是否需要翻译
        boolean translate = TranslateUtil.isTranslate(lang);
        // 表头翻译
        List<List<String>> headerList = ThingcomExcelUtil.getHeadList(GatewayExportExcel.class, translate);
        // translate为真，翻译
        if (translate) {
            // 表名的翻译
            sheetName = TranslateUtil.chineseToEnglish(sheetName);
            // 文件名的翻译
            fileName = TranslateUtil.chineseToEnglish(fileName);
        }
        ThingcomExcelUtil.setExportTemplateFile(response, fileName);
        EasyExcel.write(response.getOutputStream(), GatewayExportExcel.class)
                .inMemory(true)
                .head(headerList)
                .registerWriteHandler(new AutoColumnWidthHandler())
                .registerWriteHandler(new ThingcomSheetHandler(GatewayExportExcel.class))
                .registerWriteHandler(new ImportHeaderCellHandler(GatewayExportExcel.class))
                .sheet(sheetName)
                .doWrite(new ArrayList<>(0));
    }

    /**
     * 通讯网关导入
     *
     * @param file
     * @return
     */
    @PostMapping("/importData")
    public R importData(MultipartFile file) throws IOException {
        List<GatewayImportExcel> list = ThingcomExcelUtil.readFile(file, GatewayImportExcel.class);
        if (!Collections.isEmpty(list)) {
            gatewayService.importData(list);
        }
        return R.success("操作成功");
    }

    /**
     * 通讯网关批量导出
     *
     * @param dto
     * @param response
     * @throws Exception
     */
    @GetMapping("/exportData")
    public void communicationGatewayExport(GatewayDTO dto, HttpServletResponse response) throws Exception {
        List<Gateway> list;
        // 若不勾选导出全部数据
        if (StrUtil.isBlank(dto.getIds())) {
            LambdaQueryWrapper<Gateway> wrapper = GatewayWrapper.build().setQueryParam(dto);
            list = gatewayService.list(wrapper);
        } else {
            // 导出勾选数据
            List<Long> longList = Func.toLongList(dto.getIds());
            list = gatewayService.listByIds(longList);
        }
        String fileName = "设备管理通讯网关";
        String sheetName = "设备管理通讯网关";
        // 判断是否需要翻译
        boolean translate = TranslateUtil.isTranslate(dto.getLang());
        // 表头翻译
        List<List<String>> headerList = ThingcomExcelUtil.getHeadList(GatewayExcel.class, translate);
        // 表内数据翻译
        List<GatewayExcel> gatewayExcels = GatewayWrapper.build().listToExcel(list, translate);
        // translate为真，翻译
        if (translate) {
            // 表名的翻译
            sheetName = TranslateUtil.chineseToEnglish(sheetName);
            // 文件名的翻译
            fileName = TranslateUtil.chineseToEnglish(fileName);
        }
        fileName = fileName + "_" + DateUtil.time();
        Class<GatewayExcel> clazz = GatewayExcel.class;
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        fileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.name());
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        response.addHeader("Access-Control-Expose-Headers", "Content-Disposition");
        EasyExcel.write(response.getOutputStream(), clazz)
                .inMemory(true)
                .registerWriteHandler(new AutoColumnWidthHandler())
                .head(headerList)
                .sheet(sheetName).doWrite(gatewayExcels);
    }

    /**
     * 通讯网关批量导出
     *
     * @param id
     * @param response
     * @throws Exception
     */
    @GetMapping("/exportDetailData")
    public void communicationGatewayDetailExport(Long id, HttpServletResponse response, String lang) throws Exception {
        Gateway gateway = gatewayService.getById(id);

        List<GatewayDetailExport> list = new ArrayList<>();
        GatewayDetailExport gatewayDetailExport = new GatewayDetailExport();
        gatewayDetailExport.setGatewayNumber(gateway.getGatewayNumber());
        gatewayDetailExport.setTransmissionPower(gateway.getTransmissionPower());

        // 查询id配置
        GatewayIpVO selectIp = selectIp(gateway.getIp()).getData();
        gatewayDetailExport.setIpaddr(selectIp.getIpaddr());
        gatewayDetailExport.setGateway(selectIp.getGateway());
        gatewayDetailExport.setNetmask(selectIp.getNetmask());
        gatewayDetailExport.setDns1(selectIp.getDns1());
        gatewayDetailExport.setDns2(selectIp.getDns2());

        // 查询服务器配置
        GatewayServerVO selectServer = selectServer(gateway.getIp()).getData();
        gatewayDetailExport.setServerAddress(selectServer.getServerAddress());
        gatewayDetailExport.setServPortUp(selectServer.getServPortUp());
        gatewayDetailExport.setServPortDown(selectServer.getServPortDown());

        // 查询频段配置
        GatewayFrequencyBandVO selectFrequencyBand = selectFrequencyBand(gateway.getIp()).getData();
        gatewayDetailExport.setIndex(selectFrequencyBand.getIndex());
        gatewayDetailExport.setFrequencyRange(selectFrequencyBand.getFrequencyRange());
        gatewayDetailExport.setRx2Sf(selectFrequencyBand.getRx2Sf());
        gatewayDetailExport.setRx2Freq(selectFrequencyBand.getRx2Freq());
        gatewayDetailExport.setRx2Count(selectFrequencyBand.getRx2Count());
        list.add(gatewayDetailExport);

        String fileName = "设备管理通讯网关详情_" + DateUtil.time();
        String sheetName = "设备管理通讯网关详情";
        // 判断是否需要翻译
        boolean translate = TranslateUtil.isTranslate(lang);
        if (translate) {
            // 表名的翻译
            sheetName = TranslateUtil.chineseToEnglish(sheetName);
            // 文件名的翻译
            fileName = TranslateUtil.chineseToEnglish(fileName);
        }
        Class<GatewayDetailExport> clazz = GatewayDetailExport.class;

        // 表头翻译
        List<List<String>> headerList = ThingcomExcelUtil.getHeadList(GatewayDetailExport.class, translate);

        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        fileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.name());
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        response.addHeader("Access-Control-Expose-Headers", "Content-Disposition");
        EasyExcel.write(response.getOutputStream(), clazz)
                .inMemory(true)
                .registerWriteHandler(new AutoColumnWidthHandler())
                .head(headerList)
                .sheet(sheetName).doWrite(list);
    }

    /**
     * 通讯网关删除
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete")
    public R delete(Long id) {
        return R.status(gatewayService.delete(id));
    }

    /**
     * 修改通讯网关编号
     *
     * @param gateway
     * @return
     */
    @PutMapping("/updateGatewayNumber")
    public R updateGatewayNumber(@RequestBody Gateway gateway) {
        // 检查数据合法性
        String errMsg = gatewayService.validateData(gateway);
        if (errMsg != null) {
            throw new ServiceException(errMsg);
        }
        return R.status(gatewayService.updateById(gateway));
    }

    /**
     * 修改通讯网关发射功率
     *
     * @param gateway
     * @return
     */
    @PutMapping("/updateTransmissionPower")
    public R updateTransmissionPower(@RequestBody Gateway gateway) {
        String url = GatewayUtil.getGatewayApiUrl(gateway.getIp());
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("type", "tx_pwr");
        jsonObject.addProperty("value", gateway.getTransmissionPower());

        // 记录操作日志
        Gateway one = gatewayService.getOne(Wrappers.<Gateway>lambdaQuery()
                .eq(Gateway::getIp, gateway.getIp()));
        GatewayOperationLog gatewayOperationLog = new GatewayOperationLog();
        gatewayOperationLog.setGatewayId(one.getId());
        gatewayOperationLog.setGatewayNumber(one.getGatewayNumber());
        gatewayOperationLog.setStatus(1);
        gatewayOperationLog.setTitle("网关参数配置");
        gatewayOperationLog.setContent("发射功率：" + gateway.getTransmissionPower());
        BladeUser user = AuthUtil.getUser();
        String createBy = user.getAccount();
        gatewayOperationLog.setCreateBy(createBy);

        try {
            String requestParam = HttpClientUtil.convertJsonObjectToString(jsonObject);
            HttpClientUtil.doPost(url, requestParam, null);
        } catch (Exception e) {
            gatewayOperationLog.setStatus(2);
        }
        gatewayOperationLogService.save(gatewayOperationLog);
        // 待确认校验结果
        return R.status(gatewayService.updateById(gateway));
    }

    /**
     * 通讯网关ip设置查询
     *
     * @param ip
     * @return
     */
    @GetMapping("/selectIp")
    public R<GatewayIpVO> selectIp(String ip) throws IOException {
        String url = GatewayUtil.getGatewayApiUrl(ip);
        String params = "type=ip_get";
        String result = HttpClientUtil.doPost(url, params, null);
        JsonObject jsonObject = JsonParser.parseString(result).getAsJsonObject().getAsJsonObject("eth0_conf");
        GatewayIpVO gatewayIpVO = new GatewayIpVO();
        gatewayIpVO.setIpaddr(jsonObject.get("ipaddr").getAsString());
        gatewayIpVO.setGateway(jsonObject.get("gateway").getAsString());
        gatewayIpVO.setNetmask(jsonObject.get("netmask").getAsString());
        gatewayIpVO.setDns1(jsonObject.get("dns1").getAsString());
        gatewayIpVO.setDns2(jsonObject.get("dns2").getAsString());
        return data(gatewayIpVO);
    }

    /**
     * 通讯网关ip设置修改
     *
     * @param gatewayIpVO
     * @return
     */
    @PutMapping("/updateIp")
    public R updateIp(@RequestBody GatewayIpVO gatewayIpVO) {
        String url = GatewayUtil.getGatewayApiUrl(gatewayIpVO.getIp());
        LambdaUpdateWrapper<Gateway> updateWrapper = Wrappers.<Gateway>lambdaUpdate()
                .eq(Gateway::getIp, gatewayIpVO.getIp())
                .set(Gateway::getIp, gatewayIpVO.getIpaddr());
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("ipaddr", gatewayIpVO.getIpaddr());
        jsonObject.addProperty("gateway", gatewayIpVO.getGateway());
        jsonObject.addProperty("netmask", gatewayIpVO.getNetmask());
        jsonObject.addProperty("dns1", gatewayIpVO.getDns1());
        jsonObject.addProperty("dns2", gatewayIpVO.getDns2());
        jsonObject.addProperty("dhcp", 0 + "");
        jsonObject.addProperty("type", "ip_set");
        // 记录操作日志
        Gateway one = gatewayService.getOne(Wrappers.<Gateway>lambdaQuery()
                .eq(Gateway::getIp, gatewayIpVO.getIp()));
        GatewayOperationLog gatewayOperationLog = new GatewayOperationLog();
        gatewayOperationLog.setGatewayId(one.getId());
        gatewayOperationLog.setGatewayNumber(one.getGatewayNumber());
        gatewayOperationLog.setStatus(1);
        gatewayOperationLog.setTitle("IP地址设置");
        gatewayOperationLog.setContent("ip地址：" + gatewayIpVO.getIpaddr() + ",子网掩码：" + gatewayIpVO.getNetmask()
                + ",网关地址：" + gatewayIpVO.getGateway() + ",DNS1：" + gatewayIpVO.getDns1() + ",DNS2：" + gatewayIpVO.getDns2());
        BladeUser user = AuthUtil.getUser();
        String createBy = user.getAccount();
        gatewayOperationLog.setCreateBy(createBy);
        gatewayService.update(updateWrapper);
        try {
            String requestParam = HttpClientUtil.convertJsonObjectToString(jsonObject);
            HttpClientUtil.doPost(url, requestParam, null);
        } catch (Exception e) {
            gatewayOperationLog.setStatus(2);
        }
        gatewayOperationLogService.save(gatewayOperationLog);

        return R.status(true);
    }

    /**
     * 通讯网关服务器设置查询
     *
     * @param ip
     * @return
     */
    @GetMapping("/selectServer")
    public R<GatewayServerVO> selectServer(String ip) throws IOException {
        String url = GatewayUtil.getGatewayApiUrl(ip);
        String params = "type=loraserver";
        String result = HttpClientUtil.doPost(url, params, null);
        Assert.notBlank(result, "获取网关配置失败");
        // 去除注释
        result = result.replaceAll("/\\*.*?\\*/", "");
        // 获取网关公共配置
        GatewayConfigCommonVO gatewayConfigCommonVO = GatewayUtil.getGatewayConfigCommonVO(result);

        GatewayServerVO gatewayServerVO = new GatewayServerVO();
        gatewayServerVO.setServerAddress(gatewayConfigCommonVO.getServerAddress());
        gatewayServerVO.setServPortUp(Func.toStr(gatewayConfigCommonVO.getServPortUp()));
        gatewayServerVO.setServPortDown(Func.toStr(gatewayConfigCommonVO.getServPortDown()));
        gatewayServerVO.setEui(gatewayConfigCommonVO.getEui());
        gatewayServerVO.setTransmissionPower(gatewayConfigCommonVO.getTransmissionPower());

        String paramsGps = "type=gps_get";
        String gps = HttpClientUtil.doPost(url, paramsGps, null);
        String[] split = gps.trim().split(",");
        gatewayServerVO.setLongitude(split[0]);
        gatewayServerVO.setLatitude(split[1]);
        gatewayServerVO.setHeight(split[2]);
        return data(gatewayServerVO);
    }

    /**
     * 通讯网关服务器设置修改
     *
     * @param gatewayServerVO
     * @return
     */
    @PutMapping("/updateServer")
    public R updateServer(@RequestBody GatewayServerVO gatewayServerVO) {
        String url = GatewayUtil.getGatewayApiUrl(gatewayServerVO.getIp());
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("addr", gatewayServerVO.getServerAddress());
        jsonObject.addProperty("port_up", gatewayServerVO.getServPortUp());
        jsonObject.addProperty("port_down", gatewayServerVO.getServPortDown());
        jsonObject.addProperty("type", "loraserver_set");
        // 记录操作日志
        Gateway one = gatewayService.getOne(Wrappers.<Gateway>lambdaQuery()
                .eq(Gateway::getIp, gatewayServerVO.getIp()));
        GatewayOperationLog gatewayOperationLog = new GatewayOperationLog();
        gatewayOperationLog.setGatewayId(one.getId());
        gatewayOperationLog.setGatewayNumber(one.getGatewayNumber());
        gatewayOperationLog.setStatus(1);
        gatewayOperationLog.setTitle("服务器配置");
        gatewayOperationLog.setContent("服务器地址：" + gatewayServerVO.getServerAddress()
                + ",上行端口：" + gatewayServerVO.getServPortUp()
                + ",下行端口：" + gatewayServerVO.getServPortDown());
        BladeUser user = AuthUtil.getUser();
        String createBy = user.getAccount();
        gatewayOperationLog.setCreateBy(createBy);

        try {
            String requestParam = HttpClientUtil.convertJsonObjectToString(jsonObject);
            HttpClientUtil.doPost(url, requestParam, null);
        } catch (Exception e) {
            gatewayOperationLog.setStatus(2);
        }
        gatewayOperationLogService.save(gatewayOperationLog);
        return R.status(true);
    }

    /**
     * 通讯网关频段设置查询
     *
     * @return
     */
    @GetMapping("/selectFrequencyList")
    public R<List<String>> selectFrequencyList() {
        List<BigDecimal> radioStartPoints = new ArrayList<>();
        BigDecimal start = new BigDecimal("*********");
        BigDecimal step = new BigDecimal("1400000");
        BigDecimal span = new BigDecimal("200000");
        for (int i = 0; i <= 88; i++) {
            radioStartPoints.add(start.add(span.multiply(BigDecimal.valueOf(i))));
        }
        List<String> frequencyList = new ArrayList<>();
        for (int i = 0; i < 12; i++) {
            BigDecimal b = start.add(step.add(span).multiply(BigDecimal.valueOf(i)));
            BigDecimal e = b.add(step);
            frequencyList.add((b.divide(new BigDecimal("1000000")) + " - " + e.divide(new BigDecimal("1000000"))));
        }
        return R.data(frequencyList);
    }

    /**
     * 通讯网关频段设置查询
     *
     * @param ip
     * @return
     */
    @GetMapping("/selectFrequencyBand")
    public R<GatewayFrequencyBandVO> selectFrequencyBand(String ip) throws IOException {
        List<BigDecimal> radioStartPoints = new ArrayList<>();
        BigDecimal start = new BigDecimal("*********.0");
        BigDecimal step = new BigDecimal("1400000.0");
        BigDecimal span = new BigDecimal("200000.0");

        for (int i = 0; i <= 88; i++) {
            radioStartPoints.add(start.add(span.multiply(BigDecimal.valueOf(i))));
        }

        String url = GatewayUtil.getGatewayApiUrl(ip);
        String params = "type=loraserver";
        String result = HttpClientUtil.doPost(url, params, null);
        Assert.notBlank(result, "获取网关配置失败");
        // 去除注释
        result = result.replaceAll("/\\*.*?\\*/", "");

        // 获取网关公共配置
        GatewayConfigCommonVO gatewayConfigCommonVO = GatewayUtil.getGatewayConfigCommonVO(result);
        BigDecimal frequencyBand = gatewayConfigCommonVO.getFrequencyBandStart().subtract(new BigDecimal("300000"));

        // 起始通道
        // 出现了：列表中：486300000.0，实际：486300000
        //int index = radioStartPoints.indexOf(frequencyBand);
        int index = -1;
        for (int i = 0; i < radioStartPoints.size(); i++) {
            if (radioStartPoints.get(i).compareTo(frequencyBand) == 0) {
                index = i;
                break;
            }
        }
        String frequencyRange = "";
        if (index >= 0) {
            BigDecimal b = radioStartPoints.get(index);
            BigDecimal e = b.add(step);
            // 频率范围
            frequencyRange = b.divide(new BigDecimal("1000000")) + " - " + e.divide(new BigDecimal("1000000"));
        }
        // 速率
        String rx2Sf = Func.toStr(gatewayConfigCommonVO.getRx2Sf());
        // 频率
        String rx2Freq = Func.toStr(gatewayConfigCommonVO.getRx2Freq());
        // 次数
        String rx2Count = Func.toStr(gatewayConfigCommonVO.getRx2Count());
        // 封装返回结果
        GatewayFrequencyBandVO gatewayFrequencyBandVO = new GatewayFrequencyBandVO();
        gatewayFrequencyBandVO.setIndex(index < 0 ? "" : index + "");
        gatewayFrequencyBandVO.setFrequencyRange(frequencyRange);
        gatewayFrequencyBandVO.setRx2Sf(rx2Sf);
        gatewayFrequencyBandVO.setRx2Freq(rx2Freq);
        gatewayFrequencyBandVO.setRx2Count(rx2Count);
        return data(gatewayFrequencyBandVO);
    }

    /**
     * 通讯网关频道设置频段修改
     *
     * @param gatewayFrequencyBandDTO
     * @return
     */
    @PutMapping("/updateFrequencyBand")
    public R updateFrequencyBand(@RequestBody GatewayFrequencyBandDTO gatewayFrequencyBandDTO) {
        String url = GatewayUtil.getGatewayApiUrl(gatewayFrequencyBandDTO.getIp());
        JsonObject params = new JsonObject();
        params.addProperty("type", "radio_set");
        List<Long> radioStartPoints = new ArrayList<>();
        long start = *********;
        long span = 200000;

        for (int i = 0; i <= 88; i++) {
            radioStartPoints.add(start + i * span);
        }
        String r0 = "";
        String r1 = "";
        if (ObjectUtil.isNotEmpty(gatewayFrequencyBandDTO.getFrequencyRange())) {
            r0 = new BigDecimal(radioStartPoints.get(gatewayFrequencyBandDTO.getFrequencyRange()))
                    .add(new BigDecimal("300000")).toString();
            r1 = new BigDecimal(radioStartPoints.get(gatewayFrequencyBandDTO.getFrequencyRange()))
                    .add(new BigDecimal("1100000")).toString();
        } else {
            r0 = gatewayFrequencyBandDTO.getR0().multiply(new BigDecimal("1000000")).add(new BigDecimal("300000")).toString();
            r1 = gatewayFrequencyBandDTO.getR1().multiply(new BigDecimal("1000000")).add(new BigDecimal("1100000")).toString();
        }
        params.addProperty("r0", r0);
        params.addProperty("r1", r1);
        // 记录操作日志
        Gateway one = gatewayService.getOne(Wrappers.<Gateway>lambdaQuery()
                .eq(Gateway::getIp, gatewayFrequencyBandDTO.getIp()));
        GatewayOperationLog gatewayOperationLog = new GatewayOperationLog();
        gatewayOperationLog.setGatewayId(one.getId());
        gatewayOperationLog.setGatewayNumber(one.getGatewayNumber());
        gatewayOperationLog.setStatus(1);
        gatewayOperationLog.setTitle("频段设置");
        if (gatewayFrequencyBandDTO.getFrequencyRange() != null) {
            gatewayOperationLog.setContent("起始通道：" + gatewayFrequencyBandDTO.getFrequencyRange());
        } else {
            gatewayOperationLog.setContent("频段：" + gatewayFrequencyBandDTO.getR0() + "-" + gatewayFrequencyBandDTO.getR1());
        }
        BladeUser user = AuthUtil.getUser();
        String createBy = user.getAccount();
        gatewayOperationLog.setCreateBy(createBy);

        try {
            String requestParam = HttpClientUtil.convertJsonObjectToString(params);
            HttpClientUtil.doPost(url, requestParam, null);
        } catch (Exception e) {
            gatewayOperationLog.setStatus(2);
        }
        gatewayOperationLogService.save(gatewayOperationLog);
        return R.status(true);
    }

    /**
     * 通讯网关频道设置修改
     *
     * @param gatewayFrequencyBandDTO
     * @return
     */
    @PutMapping("/updateRX")
    public R updateRX(@RequestBody GatewayFrequencyBandDTO gatewayFrequencyBandDTO) {
        String url = GatewayUtil.getGatewayApiUrl(gatewayFrequencyBandDTO.getIp());
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("type", gatewayFrequencyBandDTO.getType());
        jsonObject.addProperty("value", gatewayFrequencyBandDTO.getValue());
        // 记录操作日志
        Gateway one = gatewayService.getOne(Wrappers.<Gateway>lambdaQuery()
                .eq(Gateway::getIp, gatewayFrequencyBandDTO.getIp()));
        GatewayOperationLog gatewayOperationLog = new GatewayOperationLog();
        gatewayOperationLog.setGatewayId(one.getId());
        gatewayOperationLog.setGatewayNumber(one.getGatewayNumber());
        gatewayOperationLog.setStatus(1);
        gatewayOperationLog.setTitle("服务器配置");
        if ("rx2_sf_set".equals(gatewayFrequencyBandDTO.getType())) {
            gatewayOperationLog.setContent("RX2速率：" + gatewayFrequencyBandDTO.getValue());
        } else if ("rx2_freq_set".equals(gatewayFrequencyBandDTO.getType())) {
            gatewayOperationLog.setContent("RX2频率：" + gatewayFrequencyBandDTO.getValue());
        } else if ("rx2_count_set".equals(gatewayFrequencyBandDTO.getType())) {
            gatewayOperationLog.setContent("RX2次数：" + gatewayFrequencyBandDTO.getValue());
        }
        BladeUser user = AuthUtil.getUser();
        String createBy = user.getAccount();
        gatewayOperationLog.setCreateBy(createBy);

        try {
            String requestParam = HttpClientUtil.convertJsonObjectToString(jsonObject);
            HttpClientUtil.doPost(url, requestParam, null);
        } catch (Exception e) {
            gatewayOperationLog.setStatus(2);
        }
        gatewayOperationLogService.save(gatewayOperationLog);
        return R.status(true);
    }

    /**
     * 通讯网关状态信息查询
     *
     * @param ip
     * @return
     */
    @GetMapping("/selectStatus")
    public R<GatewayStatusVO> selectStatus(String ip) throws IOException {
        String url = GatewayUtil.getGatewayApiUrl(ip);
        String params = "type=status";
        String result = HttpClientUtil.doPost(url, params, null);
        JsonObject asJsonObject = JsonParser.parseString(result).getAsJsonObject().getAsJsonObject("info");
        GatewayStatusVO gatewayStatusVO = new GatewayStatusVO();
        JsonElement rntm = asJsonObject.get("rntm");
        if (Objects.nonNull(rntm)) {
            gatewayStatusVO.setRntm(rntm.getAsString());
        }
        JsonElement imei = asJsonObject.get("imei");
        if (Objects.nonNull(imei)) {
            gatewayStatusVO.setImei(imei.getAsString());
        }
        JsonElement simc = asJsonObject.get("simc");
        if (Objects.nonNull(simc)) {
            gatewayStatusVO.setSimc(simc.getAsString());
        }
        JsonElement oper = asJsonObject.get("oper");
        if (Objects.nonNull(oper)) {
            gatewayStatusVO.setOper(oper.getAsString());
        }
        JsonElement csqv = asJsonObject.get("csqv");
        if (Objects.nonNull(csqv)) {
            gatewayStatusVO.setCsqv(csqv.getAsString());
        }
        JsonElement mbip = asJsonObject.get("mbip");
        if (Objects.nonNull(mbip)) {
            gatewayStatusVO.setMbip(mbip.getAsString());
        }
        JsonElement sx130x = asJsonObject.get("1301");
        if (Objects.nonNull(sx130x)) {
            gatewayStatusVO.setSX130x(sx130x.getAsString());
        }
        JsonElement dely = asJsonObject.get("dely");
        if (Objects.nonNull(dely)) {
            gatewayStatusVO.setDely(dely.getAsString());
        }
        return R.data(gatewayStatusVO);
    }

    /**
     * 重启网关
     *
     * @param dto
     * @return
     */
    @PostMapping("/rebootGateway")
    public R rebootGateway(@RequestBody RestartGatewayDTO dto) throws IOException {
        SystemSet systemSet = systemSetService.getOne(Wrappers.<SystemSet>lambdaQuery()
                .orderByDesc(SystemSet::getId).last("limit 1"));

        //if (!PasswordUtil.base64Decode(systemSet.getGatewayLockPassword()).equals(dto.getPassword())) {
        //    return R.fail("密码错误");
        //}

        Assert.isTrue(Objects.nonNull(systemSet) && StrUtil.isNotBlank(systemSet.getControlPassword())
                , "系统密码未设定，请联系管理员");
        String controlPassword = PasswordUtil.base64Decode(systemSet.getControlPassword());
        if (!controlPassword.equals(dto.getPassword())) {
            return R.fail("密码错误");
        }

        String url = GatewayUtil.getGatewayApiUrl(dto.getIp());
        String params = "type=reboot";
        String result = HttpClientUtil.doPost(url, params, null);
        return status(true);
    }

    /**
     * 恢复出厂设置
     *
     * @param dto
     * @return
     */
    @PostMapping("/restoreGateway")
    public R restoreGateway(@RequestBody RestartGatewayDTO dto) throws IOException {
        SystemSet systemSet = systemSetService.getOne(Wrappers.<SystemSet>lambdaQuery()
                .orderByDesc(SystemSet::getId).last("limit 1"));

        //if (!PasswordUtil.base64Decode(systemSet.getGatewayUnlockPassword()).equals(dto.getPassword())) {
        //    return R.fail("密码错误");
        //}

        Assert.isTrue(Objects.nonNull(systemSet) && StrUtil.isNotBlank(systemSet.getControlPassword())
                , "系统密码未设定，请联系管理员");
        String controlPassword = PasswordUtil.base64Decode(systemSet.getControlPassword());
        if (!controlPassword.equals(dto.getPassword())) {
            return R.fail("密码错误");
        }

        String url = GatewayUtil.getGatewayApiUrl(dto.getIp());
        String params = "type=restore";
        String result = HttpClientUtil.doPost(url, params, null);
        return status(true);
    }

    /**
     * 网关报文日志查询
     *
     * @param eui
     * @return
     */
    @GetMapping("/selectMessageLog")
    public R selectMessageLog(String eui) {
        return data(gatewayService.selectMessageLog(eui));
    }

    // 测试代码
    public static void main(String[] args) throws IOException {
        String url = "http://*************/cgi-bin/info.cgi";
        String params = "type=loraserver";

        //String s = HttpClientUtil.convertJsonObjectToString(jsonObject);
        String result = HttpClientUtil.doPost(url, params, null);
        System.out.println(result);
        //String asString = JsonParser.parseString(result).getAsJsonObject()
        // .getAsJsonObject("gateway_conf").get("gateway_ID").getAsString();
        //System.out.println(asString);
    }

}

package org.springblade.thingcom.device.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.springblade.common.cache.DictCache;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.log.annotation.ApiLog;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.support.Kv;
import org.springblade.core.tool.utils.Func;
import org.springblade.thingcom.core.comparator.StringComparator;
import org.springblade.thingcom.core.excel.handler.AutoColumnWidthHandler;
import org.springblade.thingcom.core.excel.utils.ThingcomExcelUtil;
import org.springblade.thingcom.device.cache.TrackerCache;
import org.springblade.thingcom.device.dto.TrackerDTO;
import org.springblade.thingcom.device.dto.TrackerSaveDTO;
import org.springblade.thingcom.device.entity.*;
import org.springblade.thingcom.device.excel.TrackerExcel;
import org.springblade.thingcom.device.excel.TrackerStatusExcel;
import org.springblade.thingcom.device.service.*;
import org.springblade.thingcom.device.vo.TrackerVO;
import org.springblade.thingcom.device.wrapper.TrackerWrapper;
import org.springblade.thingcom.translate.utils.TranslateUtil;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.Serializable;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 跟踪器控制层
 *
 * <AUTHOR>
 * @since 2024-01-03 15:19:53
 */
@AllArgsConstructor
@RestController
@RequestMapping("/tracker")
public class TrackerController extends BladeController {
    private final TrackerService trackerService;
    private final RobotService robotService;
    private final RobotRegionalService robotRegionalService;
    private final ClearingPathTrackerService clearingPathTrackerService;
    private final TrackerRegionalService trackerRegionalService;
    private final TrackerModelService trackerModelService;

    /**
     * 跟踪器分页查询
     *
     * @param query
     * @param dto
     * @return
     */
    @GetMapping("/page")
    public R<IPage<TrackerVO>> page(Query query, TrackerDTO dto) {
        LambdaQueryWrapper<Tracker> wrapper = TrackerWrapper.build().setQueryParam(dto);
        IPage<Tracker> iPage = trackerService.page(Condition.getPage(query), wrapper);
        // 机器人数据
        Map<Long, Robot> robotMap = new HashMap<>();
        // 机器人对应的分区数据
        Map<Long, RobotRegional> robotRegionalMap = new HashMap<>();
        // 跟踪器对应的分区数据
        Map<Long, TrackerRegional> trackerRegionalMap = new HashMap<>();
        // 跟踪器对应的型号数据
        Map<Long, TrackerModel> trackerModelMap = new HashMap<>();

        // 查询跟踪器分区数据
        Set<Long> trackerRegionalIdSet = iPage.getRecords().stream().map(Tracker::getRegionalId).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(trackerRegionalIdSet)) {
            trackerRegionalMap = trackerRegionalService.listByIds(trackerRegionalIdSet)
                    .stream()
                    .collect(Collectors.toMap(TrackerRegional::getId, Function.identity(), (k1, k2) -> k1));
        }
        // 查询跟踪器型号数据
        Set<Long> trackerModelIdSet = iPage.getRecords().stream().map(Tracker::getTrackerModelId).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(trackerModelIdSet)) {
            trackerModelMap = trackerModelService.listByIds(trackerModelIdSet)
                    .stream()
                    .collect(Collectors.toMap(TrackerModel::getId, Function.identity(), (k1, k2) -> k1));
        }

        Set<Long> trackerIdSet = iPage.getRecords().stream().map(Tracker::getId).collect(Collectors.toSet());
        // 查询跟踪架绑定的清扫路径，从路径上获取机器人信息
        if (CollUtil.isNotEmpty(trackerIdSet)) {
            Map<Long, Long> trackerClearMap = clearingPathTrackerService.list(Wrappers.<ClearingPathTracker>lambdaQuery()
                            .in(ClearingPathTracker::getTrackerId, trackerIdSet))
                    .stream()
                    .collect(Collectors.toMap(ClearingPathTracker::getTrackerId, ClearingPathTracker::getClearingPathId));
            // 通过清扫路径查询机器人信息
            if (CollUtil.isNotEmpty(trackerClearMap.values())) {
                Set<Long> clearingPathIdSet = new HashSet<>(trackerClearMap.values());
                Map<Long, Robot> map = robotService.list(Wrappers.<Robot>lambdaQuery()
                                .in(Robot::getClearingPathId, clearingPathIdSet))
                        .stream()
                        .collect(Collectors.toMap(Robot::getClearingPathId, Function.identity(), (k, v) -> v));
                // 通过跟踪器数据来合并机器人数据
                Set<Map.Entry<Long, Long>> entries = trackerClearMap.entrySet();
                for (Map.Entry<Long, Long> entry : entries) {
                    Long trackerId = entry.getKey();
                    Long clearingPathId = entry.getValue();

                    // 将 map.get() 的结果存储在局部变量中
                    Robot robot = map.get(clearingPathId);
                    if (robot != null) {
                        robotMap.put(trackerId, robot);
                    }
                }
                if (CollUtil.isNotEmpty(robotMap)) {
                    // 查询机器人对应的分区数据
                    Set<Long> regionalIdSet = robotMap.values().stream().map(Robot::getRobotRegionalId).collect(Collectors.toSet());
                    if (CollUtil.isNotEmpty(regionalIdSet)) {
                        robotRegionalMap = robotRegionalService.list(Wrappers.<RobotRegional>lambdaQuery()
                                        .in(RobotRegional::getId, regionalIdSet))
                                .stream()
                                .collect(Collectors.toMap(RobotRegional::getId, Function.identity(), (k1, k2) -> k1));
                    }
                }
            }
        }
        return data(TrackerWrapper.build(robotMap, robotRegionalMap, trackerRegionalMap, trackerModelMap).pageVO(iPage));
    }

    /**
     * 跟踪器列表查询
     *
     * @param dto
     * @return
     */
    @GetMapping("/list")
    public R<List<TrackerVO>> list(TrackerDTO dto) {
        LambdaQueryWrapper<Tracker> wrapper = TrackerWrapper.build().setQueryParam(dto);
        List<Tracker> list = trackerService.list(wrapper);
        // 机器人数据
        Map<Long, Robot> robotMap = new HashMap<>();
        // 机器人对应的分区数据
        Map<Long, RobotRegional> robotRegionalMap = new HashMap<>();
        // 跟踪器对应的分区数据
        Map<Long, TrackerRegional> trackerRegionalMap = new HashMap<>();
        // 跟踪器对应的型号数据
        Map<Long, TrackerModel> trackerModelMap = new HashMap<>();
        // 查询跟踪器分区数据
        Set<Long> trackerRegionalIdSet = list.stream().map(Tracker::getRegionalId).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(trackerRegionalIdSet)) {
            trackerRegionalMap = trackerRegionalService.listByIds(trackerRegionalIdSet)
                    .stream()
                    .collect(Collectors.toMap(TrackerRegional::getId, Function.identity(), (k1, k2) -> k1));
        }
        // 查询跟踪器型号数据
        Set<Long> trackerModelIdSet = list.stream().map(Tracker::getTrackerModelId).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(trackerModelIdSet)) {
            trackerModelMap = trackerModelService.listByIds(trackerModelIdSet)
                    .stream()
                    .collect(Collectors.toMap(TrackerModel::getId, Function.identity(), (k1, k2) -> k1));
        }

        Set<Long> trackerIdSet = list.stream().map(Tracker::getId).collect(Collectors.toSet());
        // 查询跟踪架绑定的清扫路径，从路径上获取机器人信息
        if (CollUtil.isNotEmpty(trackerIdSet)) {
            Map<Long, Long> trackerClearMap = clearingPathTrackerService.list(Wrappers.<ClearingPathTracker>lambdaQuery()
                            .in(ClearingPathTracker::getTrackerId, trackerIdSet))
                    .stream()
                    .collect(Collectors.toMap(ClearingPathTracker::getTrackerId, ClearingPathTracker::getClearingPathId));
            // 通过清扫路径查询机器人信息
            if (CollUtil.isNotEmpty(trackerClearMap.values())) {
                Set<Long> clearingPathIdSet = new HashSet<>(trackerClearMap.values());
                Map<Long, Robot> map = robotService.list(Wrappers.<Robot>lambdaQuery()
                                .in(Robot::getClearingPathId, clearingPathIdSet))
                        .stream()
                        .collect(Collectors.toMap(Robot::getClearingPathId, Function.identity(), (k, v) -> v));
                // 通过跟踪器数据来合并机器人数据
                Set<Map.Entry<Long, Long>> entries = trackerClearMap.entrySet();
                for (Map.Entry<Long, Long> entry : entries) {
                    Long trackerId = entry.getKey();
                    Long clearingPathId = entry.getValue();

                    // 将 map.get() 的结果存储在局部变量中
                    Robot robot = map.get(clearingPathId);
                    if (robot != null) {
                        robotMap.put(trackerId, robot);
                    }
                }

                if (CollUtil.isNotEmpty(robotMap)) {
                    // 查询机器人对应的分区数据
                    Set<Long> regionalIdSet = robotMap.values().stream().map(Robot::getRobotRegionalId).collect(Collectors.toSet());
                    if (CollUtil.isNotEmpty(regionalIdSet)) {
                        robotRegionalMap = robotRegionalService.list(Wrappers.<RobotRegional>lambdaQuery()
                                        .in(RobotRegional::getId, regionalIdSet))
                                .stream()
                                .collect(Collectors.toMap(RobotRegional::getId, Function.identity(), (k1, k2) -> k1));
                    }
                }
            }
        }
        return data(TrackerWrapper.build(robotMap, robotRegionalMap, trackerRegionalMap, trackerModelMap).listVO(list));
    }

    /**
     * 新增跟踪器
     *
     * @param entity
     * @return
     */
    @ApiLog("新增跟踪器")
    @PostMapping
    public R save(@Valid @RequestBody Tracker entity) {
        // 检查数据合法性
        trackerService.validateData(entity);
        return status(trackerService.save(entity));
    }

    /**
     * 修改跟踪器
     *
     * @param entity
     * @return
     */
    @ApiLog("修改跟踪器")
    @PutMapping
    public R update(@Valid @RequestBody Tracker entity) {
        Assert.notNull(entity.getId(), "id不能为空");
        // 清除缓存
        TrackerCache.deleteByIds(entity.getId().toString());
        // 检查数据合法性
        trackerService.validateData(entity);
        return status(trackerService.updateById(entity));
    }

    /**
     * 修改跟踪器名称前缀
     *
     * @param dto
     * @return
     */
    @ApiLog("修改跟踪器名称前缀")
    @PutMapping("/name/prefix")
    public R updateNamePrefix(@Valid @RequestBody TrackerSaveDTO dto) {
        // 清除缓存
        TrackerCache.deleteByIds(dto.getIds());
        return status(trackerService.update(Wrappers.<Tracker>lambdaUpdate()
                .set(Tracker::getNamePrefix, dto.getNamePrefix())
                // 名称 = 名称前缀 + '-' + 编号
                .setSql("name = CONCAT(name_prefix, tracker_number)")
                .in(Tracker::getId, Func.toLongList(dto.getIds()))));
    }

    /**
     * 删除跟踪器
     *
     * @param ids
     * @return
     */
    @ApiLog("删除跟踪器")
    @DeleteMapping("/{ids}")
    public R delete(@PathVariable Serializable ids) {
        if (Objects.isNull(ids)) {
            return status(true);
        }
        // 清除缓存
        TrackerCache.deleteByIds(ids.toString());
        return status(trackerService.removeBatchByIds(Func.toLongList(ids.toString())));
    }

    /**
     * 导出跟踪器
     *
     * @param response
     * @param dto
     * @return void
     */
    @GetMapping("/export-data")
    public void exportData(HttpServletResponse response, TrackerDTO dto) throws IOException {
        LambdaQueryWrapper<Tracker> wrapper = TrackerWrapper.build().setQueryParam(dto);
        List<Tracker> list = trackerService.list(wrapper);
        String sheetName = "跟踪器数据表";
        // 判断是否需要翻译
        boolean translate = TranslateUtil.isTranslate(dto.getLang());
        // 跟踪器对应的分区数据
        Map<Long, TrackerRegional> trackerRegionalMap = new HashMap<>();
        // 跟踪器对应的型号数据
        Map<Long, TrackerModel> trackerModelMap = new HashMap<>();
        // 查询跟踪器分区数据
        Set<Long> trackerRegionalIdSet = list.stream().map(Tracker::getRegionalId).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(trackerRegionalIdSet)) {
            trackerRegionalMap = trackerRegionalService.listByIds(trackerRegionalIdSet)
                    .stream()
                    .collect(Collectors.toMap(TrackerRegional::getId, Function.identity(), (k1, k2) -> k1));
        }
        // 查询跟踪器型号数据
        Set<Long> trackerModelIdSet = list.stream().map(Tracker::getTrackerModelId).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(trackerModelIdSet)) {
            trackerModelMap = trackerModelService.listByIds(trackerModelIdSet)
                    .stream()
                    .collect(Collectors.toMap(TrackerModel::getId, Function.identity(), (k1, k2) -> k1));
        }
        // 数据列表转excel 表内数据常量翻译
        List<TrackerExcel> data = TrackerWrapper.build(trackerRegionalMap, trackerModelMap).listToExcel(list, translate);
        // 表头翻译
        List<List<String>> headerList = ThingcomExcelUtil.getHeadList(TrackerExcel.class, translate);
        // translate为真，翻译
        if (translate) {
            // 表文件名的翻译
            sheetName = TranslateUtil.chineseToEnglish(sheetName);
        }
        ThingcomExcelUtil.setExportDataFile(response, sheetName);
        EasyExcel.write(response.getOutputStream(), TrackerExcel.class)
                .inMemory(true)
                .head(headerList)
                .registerWriteHandler(new AutoColumnWidthHandler())
                .sheet(sheetName).doWrite(data);
    }

    /**
     * 支架状态分页查询
     *
     * @param query
     * @param dto
     * @return
     */
    @GetMapping("/statusPage")
    public R<IPage<TrackerVO>> statusPage(Query query, TrackerDTO dto) {
        LambdaQueryWrapper<Tracker> wrapper = TrackerWrapper.build().setQueryParam(dto);
        IPage<Tracker> iPage = trackerService.page(Condition.getPage(query), wrapper);
        // 机器人数据
        Map<Long, Robot> robotMap = new HashMap<>();
        // 机器人对应的分区数据
        Map<Long, RobotRegional> robotRegionalMap = new HashMap<>();
        // 跟踪器对应的分区数据
        Map<Long, TrackerRegional> trackerRegionalMap = new HashMap<>();
        // 跟踪器对应的型号数据
        Map<Long, TrackerModel> trackerModelMap = new HashMap<>();
        // 查询跟踪器分区数据
        Set<Long> trackerRegionalIdSet = iPage.getRecords().stream().map(Tracker::getRegionalId).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(trackerRegionalIdSet)) {
            trackerRegionalMap = trackerRegionalService.listByIds(trackerRegionalIdSet)
                    .stream()
                    .collect(Collectors.toMap(TrackerRegional::getId, Function.identity(), (k1, k2) -> k1));
        }
        // 查询跟踪器型号数据
        Set<Long> trackerModelIdSet = iPage.getRecords().stream().map(Tracker::getTrackerModelId).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(trackerModelIdSet)) {
            trackerModelMap = trackerModelService.listByIds(trackerModelIdSet)
                    .stream()
                    .collect(Collectors.toMap(TrackerModel::getId, Function.identity(), (k1, k2) -> k1));
        }

        Set<Long> trackerIdSet = iPage.getRecords().stream().map(Tracker::getId).collect(Collectors.toSet());
        // 查询跟踪架绑定的清扫路径，从路径上获取机器人信息
        if (CollUtil.isNotEmpty(trackerIdSet)) {
            Map<Long, Long> trackerClearMap = clearingPathTrackerService.list(Wrappers.<ClearingPathTracker>lambdaQuery()
                            .in(ClearingPathTracker::getTrackerId, trackerIdSet))
                    .stream()
                    .collect(Collectors.toMap(ClearingPathTracker::getTrackerId, ClearingPathTracker::getClearingPathId));
            // 通过清扫路径查询机器人信息
            if (CollUtil.isNotEmpty(trackerClearMap.values())) {
                Set<Long> clearingPathIdSet = new HashSet<>(trackerClearMap.values());
                Map<Long, Robot> map = robotService.list(Wrappers.<Robot>lambdaQuery()
                                .in(Robot::getClearingPathId, clearingPathIdSet))
                        .stream()
                        .collect(Collectors.toMap(Robot::getClearingPathId, Function.identity(), (k, v) -> v));
                // 通过跟踪器数据来合并机器人数据
                Set<Map.Entry<Long, Long>> entries = trackerClearMap.entrySet();
                for (Map.Entry<Long, Long> entry : entries) {
                    Long trackerId = entry.getKey();
                    Long clearingPathId = entry.getValue();

                    // 将 map.get() 的结果存储在局部变量中
                    Robot robot = map.get(clearingPathId);
                    if (robot != null) {
                        robotMap.put(trackerId, robot);
                    }
                }
                if (CollUtil.isNotEmpty(robotMap)) {
                    // 查询机器人对应的分区数据
                    Set<Long> regionalIdSet = robotMap.values().stream().map(Robot::getRobotRegionalId).collect(Collectors.toSet());
                    if (CollUtil.isNotEmpty(regionalIdSet)) {
                        robotRegionalMap = robotRegionalService.list(Wrappers.<RobotRegional>lambdaQuery()
                                        .in(RobotRegional::getId, regionalIdSet))
                                .stream()
                                .collect(Collectors.toMap(RobotRegional::getId, Function.identity(), (k1, k2) -> k1));
                    }
                }
            }
        }
        return data(TrackerWrapper.build(robotMap, robotRegionalMap, trackerRegionalMap, trackerModelMap).pageVO(iPage));
    }

    /**
     * 导出
     *
     * @param dto
     * @param response
     * @throws Exception
     */
    @GetMapping("/status/export-data")
    public void exportData(TrackerDTO dto, HttpServletResponse response) throws Exception {
        LambdaQueryWrapper<Tracker> wrapper = TrackerWrapper.build().setQueryParam(dto);
        List<Tracker> list = trackerService.list(wrapper);
        String sheetName = "支架状态";

        // 判断是否需要翻译
        boolean translate = TranslateUtil.isTranslate(dto.getLang());
        // 机器人数据
        Map<Long, Robot> robotMap = new HashMap<>();
        // 机器人对应的分区数据
        Map<Long, RobotRegional> robotRegionalMap = new HashMap<>();
        // 跟踪器对应的分区数据
        Map<Long, TrackerRegional> trackerRegionalMap = new HashMap<>();
        // 跟踪器对应的型号数据
        Map<Long, TrackerModel> trackerModelMap = new HashMap<>();
        // 查询跟踪器分区数据
        Set<Long> trackerRegionalIdSet = list.stream().map(Tracker::getRegionalId).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(trackerRegionalIdSet)) {
            trackerRegionalMap = trackerRegionalService.listByIds(trackerRegionalIdSet)
                    .stream()
                    .collect(Collectors.toMap(TrackerRegional::getId, Function.identity(), (k1, k2) -> k1));
        }
        // 查询跟踪器型号数据
        Set<Long> trackerModelIdSet = list.stream().map(Tracker::getTrackerModelId).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(trackerModelIdSet)) {
            trackerModelMap = trackerModelService.listByIds(trackerModelIdSet)
                    .stream()
                    .collect(Collectors.toMap(TrackerModel::getId, Function.identity(), (k1, k2) -> k1));
        }

        Set<Long> trackerIdSet = list.stream().map(Tracker::getId).collect(Collectors.toSet());
        // 查询跟踪架绑定的清扫路径，从路径上获取机器人信息
        if (CollUtil.isNotEmpty(trackerIdSet)) {
            Map<Long, Long> trackerClearMap = clearingPathTrackerService.list(Wrappers.<ClearingPathTracker>lambdaQuery()
                            .in(ClearingPathTracker::getTrackerId, trackerIdSet))
                    .stream()
                    .collect(Collectors.toMap(ClearingPathTracker::getTrackerId, ClearingPathTracker::getClearingPathId));
            // 通过清扫路径查询机器人信息
            if (CollUtil.isNotEmpty(trackerClearMap.values())) {
                Set<Long> clearingPathIdSet = new HashSet<>(trackerClearMap.values());
                Map<Long, Robot> map = robotService.list(Wrappers.<Robot>lambdaQuery()
                                .in(Robot::getClearingPathId, clearingPathIdSet))
                        .stream()
                        .collect(Collectors.toMap(Robot::getClearingPathId, Function.identity(), (k, v) -> v));
                // 通过跟踪器数据来合并机器人数据
                Set<Map.Entry<Long, Long>> entries = trackerClearMap.entrySet();
                for (Map.Entry<Long, Long> entry : entries) {
                    Long trackerId = entry.getKey();
                    Long clearingPathId = entry.getValue();

                    // 将 map.get() 的结果存储在局部变量中
                    Robot robot = map.get(clearingPathId);
                    if (robot != null) {
                        robotMap.put(trackerId, robot);
                    }
                }

                if (CollUtil.isNotEmpty(robotMap)) {
                    // 查询机器人对应的分区数据
                    Set<Long> regionalIdSet = robotMap.values().stream().map(Robot::getRobotRegionalId).collect(Collectors.toSet());
                    if (CollUtil.isNotEmpty(regionalIdSet)) {
                        robotRegionalMap = robotRegionalService.list(Wrappers.<RobotRegional>lambdaQuery()
                                        .in(RobotRegional::getId, regionalIdSet))
                                .stream()
                                .collect(Collectors.toMap(RobotRegional::getId, Function.identity(), (k1, k2) -> k1));
                    }
                }
            }
        }
        // 表内数据常量翻译
        List<TrackerStatusExcel> data = TrackerWrapper.build(robotMap, robotRegionalMap, trackerRegionalMap, trackerModelMap).statusListToExcel(list, translate);
        // 表头翻译
        List<List<String>> headerList = ThingcomExcelUtil.getHeadList(TrackerStatusExcel.class, translate);
        // translate为真，翻译
        if (translate) {
            // 表文件名的翻译
            sheetName = TranslateUtil.chineseToEnglish(sheetName);
        }

        ThingcomExcelUtil.setExportDataFile(response, sheetName);
        EasyExcel.write(response.getOutputStream(), TrackerStatusExcel.class)
                .inMemory(true)
                .registerWriteHandler(new AutoColumnWidthHandler())
                .head(headerList)
                .sheet(sheetName).doWrite(data);
    }

    /**
     * 数据统计
     *
     * @return
     */
    @GetMapping("/statistics")
    public R<TrackerVO> statistics(TrackerDTO dto) {
        return data(trackerService.statistics(dto));
    }

    /**
     * 跟踪器状态下拉列表
     *
     * @return
     */
    @GetMapping("/trackerListName")
    public R<List<Kv>> trackerListName() {
        List<Kv> collect = DictCache.getList("tracker_status")
                .stream().map(obj -> Kv.create()
                        .set("label", obj.getDictValue())
                        .set("value", obj.getDictKey()))
                .collect(Collectors.toList());
        return data(collect);
    }

    /**
     * 跟踪器下拉列表
     *
     * @return
     */
    @GetMapping("/select")
    public R select(Long regionalId) {
        List<Kv> ret = trackerService.list(Wrappers.<Tracker>lambdaQuery()
                        .eq(regionalId != null, Tracker::getRegionalId, regionalId))
                .stream()
                .sorted(Comparator.comparing(Tracker::getName, new StringComparator()))
                .map(obj -> Kv.create()
                        .set(CommonConstant.LABEL, obj.getName())
                        .set(CommonConstant.VALUE, obj.getId()))
                .collect(Collectors.toList());
        return data(ret);
    }
}
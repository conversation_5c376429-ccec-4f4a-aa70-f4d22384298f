package org.springblade.thingcom.device.controller;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.log.annotation.ApiLog;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.thingcom.base.cache.SystemCache;
import org.springblade.thingcom.base.entity.SystemSet;
import org.springblade.thingcom.core.excel.utils.ThingcomExcelUtil;
import org.springblade.thingcom.device.dto.TrackerModelDTO;
import org.springblade.thingcom.device.entity.TrackerModel;
import org.springblade.thingcom.device.service.TrackerModelService;
import org.springblade.thingcom.device.vo.TrackerModelVO;
import org.springblade.thingcom.device.wrapper.TrackerModelWrapper;
import org.springblade.thingcom.translate.utils.TranslateUtil;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * 跟踪器型号控制层
 *
 * <AUTHOR>
 * @since 2025-02-14 16:06:59
 */
@AllArgsConstructor
@RestController
@RequestMapping("/tracker-model")
public class TrackerModelController extends BladeController {
    private final TrackerModelService trackerModelService;

    /**
     * 跟踪器型号分页查询
     *
     * @param query
     * @param dto
     * @return
     */
    @GetMapping("/page")
    public R<IPage<TrackerModelVO>> page(Query query, TrackerModelDTO dto) {
        LambdaQueryWrapper<TrackerModel> wrapper = Wrappers.<TrackerModel>lambdaQuery(dto)
                .orderByDesc(TrackerModel::getId);
        IPage<TrackerModel> iPage = trackerModelService.page(Condition.getPage(query), wrapper);
        return data(TrackerModelWrapper.build().pageVO(iPage));
    }

    /**
     * 跟踪器型号列表查询
     *
     * @param dto
     * @return
     */
    @GetMapping("/list")
    public R<List<TrackerModelVO>> list(TrackerModelDTO dto) {
        LambdaQueryWrapper<TrackerModel> wrapper = Wrappers.<TrackerModel>lambdaQuery(dto)
                .orderByDesc(TrackerModel::getId);
        List<TrackerModel> list = trackerModelService.list(wrapper);
        return data(TrackerModelWrapper.build().listVO(list));
    }

    /**
     * 查看跟踪器型号详情
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public R<TrackerModelVO> show(@PathVariable Serializable id) {
        TrackerModel entity = trackerModelService.getById(id);
        return data(TrackerModelWrapper.build().entityVO(entity));
    }

    /**
     * 新增跟踪器型号
     *
     * @param entity
     * @return
     */
    @ApiLog("新增跟踪器型号")
    @PostMapping
    public R save(@Valid @RequestBody TrackerModel entity) {
        // 检查数据合法性
        trackerModelService.validateData(entity);
        return status(trackerModelService.save(entity));
    }

    /**
     * 修改跟踪器型号
     *
     * @param entity
     * @return
     */
    @ApiLog("修改跟踪器型号")
    @PutMapping
    public R update(@Valid @RequestBody TrackerModel entity) {
        Assert.notNull(entity.getId(), "id不能为空");
        // 检查数据合法性
        trackerModelService.validateData(entity);
        return status(trackerModelService.updateById(entity));
    }

    /**
     * 删除跟踪器型号
     *
     * @param ids
     * @return
     */
    @ApiLog("删除跟踪器型号")
    @DeleteMapping("/{ids}")
    public R delete(@PathVariable Serializable ids) {
        List<Long> idList = Func.toLongList(String.valueOf(ids));
        return status(trackerModelService.removeData(idList));
    }

    /**
     * 导出模板
     *
     * @param response
     * @param lang
     * @return void
     */
    @GetMapping("/export-template")
    public void exportTemplate(HttpServletResponse response, String lang) throws Exception {
        String filePath = "file/支架型号数据模板.xlsx";
        String fileName = "支架型号数据模板";
        if (TranslateUtil.isTranslate(lang)) {
            filePath = "file/TrackerModelDataTemplate.xlsx";
            fileName = "TrackerModelDataTemplate";
        }
        // 获取资源
        Resource resource = new ClassPathResource(filePath);
        Assert.isTrue(resource.exists(), "文件不存在");
        ThingcomExcelUtil.setExportTemplateFile(response, fileName);
        // 读取资源文件并写入响应输出流
        try (InputStream inputStream = resource.getInputStream()) {
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                response.getOutputStream().write(buffer, 0, bytesRead);
            }
        }
    }

    /**
     * 导入跟踪器型号
     *
     * @param file
     * @return R
     */
    @ApiLog("导入跟踪器型号")
    @PostMapping("/import-data")
    public R importData(MultipartFile file) {
        String errorReason = trackerModelService.importData(file);
        if (StrUtil.isBlank(errorReason)) {
            return data("");
        } else {
            return R.fail(errorReason);
        }
    }

    /**
     * 导出跟踪器型号
     *
     * @param response
     * @param dto
     * @return void
     */
    @GetMapping("/export-data")
    public void exportData(HttpServletResponse response, TrackerModelDTO dto) throws IOException {
        // 系统配置参数
        SystemSet systemSet = SystemCache.getById(CommonConstant.SYSTEM_CONFIG_ID);
        // 获取跟踪器型号数据
        LambdaQueryWrapper<TrackerModel> wrapper = Wrappers.<TrackerModel>lambdaQuery(dto)
                .orderByDesc(TrackerModel::getId);
        List<TrackerModel> list = trackerModelService.list(wrapper);
        String filePath = "file/支架型号数据模板.xlsx";
        String fileName = "支架型号数据";
        String sheetName = "支架型号数据";
        if (TranslateUtil.isTranslate(dto.getLang())) {
            filePath = "file/TrackerModelDataTemplate.xlsx";
            fileName = "TrackerModelData";
            sheetName = "TrackerModelData";
        }
        // 获取资源
        Resource resource = new ClassPathResource(filePath);
        Assert.isTrue(resource.exists(), "文件不存在");
        try (InputStream inputStream = resource.getInputStream();
             Workbook workbook = new XSSFWorkbook(inputStream)) {
            // 修改 Sheet 名称
            workbook.setSheetName(0, sheetName);
            // 创建边框样式
            CellStyle cellStyle = workbook.createCellStyle();
            cellStyle.setBorderTop(BorderStyle.THIN);
            cellStyle.setBorderBottom(BorderStyle.THIN);
            cellStyle.setBorderLeft(BorderStyle.THIN);
            cellStyle.setBorderRight(BorderStyle.THIN);
            cellStyle.setAlignment(HorizontalAlignment.LEFT);
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            // 获取Sheet
            Sheet sheet = workbook.getSheetAt(0);
            // 系统设置数据写入Excel
            if (Objects.nonNull(systemSet)) {
                // 在第 1 行插入数据
                Row newRow = sheet.getRow(0);
                // 第 2 列，起始桥架长度
                Cell cell = newRow.createCell(1);
                cell.setCellValue(Func.toStr(systemSet.getStartBridgeLength()));
                cell.setCellStyle(cellStyle);
                // 第 4 列，桥架长度
                cell = newRow.createCell(3);
                cell.setCellValue(Func.toStr(systemSet.getBridgeLength()));
                cell.setCellStyle(cellStyle);
                // 第 6 列，返回桥架长度
                cell = newRow.createCell(5);
                cell.setCellValue(Func.toStr(systemSet.getReturnBridgeLength()));
                cell.setCellStyle(cellStyle);
            }
            // 从第 3 行开始写入数据
            int rowIndex = 2;
            // 跟踪器型号数据写入Excel
            for (TrackerModel trackerModel : list) {
                // 在第 3 行插入新行
                Row newRow = sheet.createRow(rowIndex++);
                // 第 1 列，型号
                Cell cell = newRow.createCell(0);
                cell.setCellValue(trackerModel.getModelName());
                // 第 2 列，长度
                cell = newRow.createCell(1);
                cell.setCellValue(Func.toStr(trackerModel.getModelLength()));
                // 第 3 列，备注
                cell = newRow.createCell(2);
                cell.setCellValue(Func.toStr(trackerModel.getRemark()));
            }
            // 设置响应头
            ThingcomExcelUtil.setExportTemplateFile(response, fileName);
            // 写入输出流
            try (OutputStream outputStream = response.getOutputStream()) {
                workbook.write(outputStream);
            }
        }
    }

}

package org.springblade.thingcom.device.async;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.springblade.common.cache.CacheNames;
import org.springblade.common.utils.CommonUtil;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.thingcom.communication.enums.BatteryStatusEnum;
import org.springblade.thingcom.communication.enums.MotorStatusEnum;
import org.springblade.thingcom.historyWarning.entity.GatewayAlarmLog;
import org.springblade.thingcom.historyWarning.mapper.GatewayAlarmLogMapper;
import org.springblade.thingcom.operationMaintenance.entity.RobotClearing;
import org.springblade.thingcom.operationMaintenance.entity.RobotRunStatis;
import org.springblade.thingcom.operationMaintenance.mapper.RobotClearingMapper;
import org.springblade.thingcom.operationMaintenance.mapper.RobotRunStatisMapper;
import org.springblade.thingcom.statistic.entity.GatewayOperationLog;
import org.springblade.thingcom.statistic.entity.RobotBattery;
import org.springblade.thingcom.statistic.entity.RobotMotor;
import org.springblade.thingcom.statistic.mapper.GatewayOperationLogMapper;
import org.springblade.thingcom.statistic.mapper.RobotBatteryMapper;
import org.springblade.thingcom.statistic.mapper.RobotMotorMapper;
import org.springblade.thingcom.warning.entity.GatewayAlarm;
import org.springblade.thingcom.warning.entity.RobotAlarm;
import org.springblade.thingcom.warning.mapper.GatewayAlarmMapper;
import org.springblade.thingcom.warning.mapper.RobotAlarmMapper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName AsyncBusinessDataHandler
 * @Description: 异步业务数据处理
 * @Author: zgq
 * @Date: 2024/5/11 13:40
 * @Version: 1.0
 **/
@Service
@AllArgsConstructor
public class AsyncBusinessDataHandler {

    private final GatewayAlarmMapper gatewayAlarmMapper;
    private final GatewayAlarmLogMapper gatewayAlarmLogMapper;
    private final GatewayOperationLogMapper gatewayOperationLogMapper;
    private final RobotAlarmMapper robotAlarmMapper;
    private final RobotBatteryMapper robotBatteryMapper;
    private final RobotMotorMapper robotMotorMapper;
    private final RobotClearingMapper robotClearingMapper;
    private final RobotRunStatisMapper robotRunStatisMapper;
    private final BladeRedis bladeRedis;

    /**
     * 删除网关关联数据
     *
     * @param idList 网关 id 列表
     */
    @Async
    public void deleteGatewayRelationData(List<Long> idList) {
        // 删除网关告警
        gatewayAlarmMapper.delete(Wrappers.<GatewayAlarm>lambdaQuery()
                .in(GatewayAlarm::getGatewayId, idList));
        // 删除网关历史告警
        gatewayAlarmLogMapper.delete(Wrappers.<GatewayAlarmLog>lambdaQuery()
                .in(GatewayAlarmLog::getGatewayId, idList));
        // 删除网关操作日志
        gatewayOperationLogMapper.delete(Wrappers.<GatewayOperationLog>lambdaQuery()
                .in(GatewayOperationLog::getGatewayId, idList));
    }

    /**
     * 删除机器人关联数据
     *
     * @param idList  机器人 id 列表
     * @param euiList 机器人 eui 列表
     */
    @Async
    public void deleteRobotRelationData(List<Long> idList, List<String> euiList) {
        // 删除机器人告警
        robotAlarmMapper.delete(Wrappers.<RobotAlarm>lambdaQuery().in(RobotAlarm::getRobotId, idList));
        // 删除机器人电池
        robotBatteryMapper.delete(Wrappers.<RobotBattery>lambdaQuery().in(RobotBattery::getRobotId, idList));
        // 删除机器人电机
        robotMotorMapper.delete(Wrappers.<RobotMotor>lambdaQuery().in(RobotMotor::getRobotId, idList));
        // 删除机器人清扫
        robotClearingMapper.delete(Wrappers.<RobotClearing>lambdaQuery().in(RobotClearing::getRobotId, idList));
        // 删除机器人运行统计
        robotRunStatisMapper.delete(Wrappers.<RobotRunStatis>lambdaQuery().in(RobotRunStatis::getRobotId, idList));
        // 根据 eui 删除机器人实时数据缓存
        String cacheKey = CacheNames.ROBOT_REALDATA;
        List<String> cacheKeyList = euiList.stream().map(e -> cacheKey + e).collect(Collectors.toList());
        bladeRedis.del(cacheKeyList);
    }

    @Async
    public void initRobotRunData(Long id) {
        LocalDateTime now = CommonUtil.getZoneTime();
        // 初始化机器人清扫数据
        RobotClearing robotClearing = new RobotClearing();
        robotClearing.setRobotId(id);
        robotClearing.setTime(now);
        robotClearingMapper.insert(robotClearing);
        // 初始化机器人运行统计数据
        RobotRunStatis robotRunStatis = new RobotRunStatis();
        robotRunStatis.setRobotId(id);
        robotRunStatis.setTime(now);
        robotRunStatisMapper.insert(robotRunStatis);
        // 初始化机器人电池实时数据
        RobotBattery robotBattery = new RobotBattery();
        robotBattery.setRobotId(id);
        robotBattery.setTime(now);
        robotBattery.setStatus(BatteryStatusEnum.OFFLINE.getValue());
        robotBatteryMapper.insert(robotBattery);
        // 初始化机器人电机实时数据
        RobotMotor robotMotor = new RobotMotor();
        robotMotor.setRobotId(id);
        robotMotor.setTime(now);
        robotMotor.setStatus(MotorStatusEnum.OFFLINE.getValue());
        robotMotorMapper.insert(robotMotor);
    }

}
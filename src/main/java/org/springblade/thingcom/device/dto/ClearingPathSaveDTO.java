package org.springblade.thingcom.device.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * 清扫路径入参类
 *
 * <AUTHOR>
 * @since 2023-12-14 09:37:21
 */
@Data
public class ClearingPathSaveDTO {

    /**
     * id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 绑定&未绑定状态（1-未绑定；2-已绑定）
     */
    private Integer status;

    /**
     * 清扫路径编号
     */
    @NotBlank(message = "清扫路径编号不能为空")
    @Length(max = 50, message = "清扫路径编号长度不能超过50")
    private String pathNumber;

    /**
     * 跟踪器参数（跟踪器id，多个使用英文逗号分隔）
     */
    private String trackerIds;

    /**
     * 备注
     */
    @Length(max = 200, message = "备注长度不能超过200")
    private String remark;
}

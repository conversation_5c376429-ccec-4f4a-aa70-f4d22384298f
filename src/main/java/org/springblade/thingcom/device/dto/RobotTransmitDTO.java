package org.springblade.thingcom.device.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.logging.log4j.core.config.plugins.validation.constraints.NotBlank;
import org.springblade.thingcom.device.entity.RobotTransmit;

/**
 * 机器人转发通道入参类
 *
 * <AUTHOR>
 * @since 2024-07-29 14:34:50
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RobotTransmitDTO extends RobotTransmit {
    private static final long serialVersionUID = 1L;

    /**
     * 机器人分区id, 多个用逗号分隔
     */
    @NotBlank(message = "请选择机器人分区")
    private String robotRegionalIds;
}

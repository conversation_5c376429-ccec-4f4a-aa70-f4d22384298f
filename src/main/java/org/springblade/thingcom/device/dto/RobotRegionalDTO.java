package org.springblade.thingcom.device.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.thingcom.device.entity.RobotRegional;
import org.springblade.thingcom.device.enums.DeviceStatusEnum;

import java.math.BigDecimal;

/**
 * 机器人分区入参类
 *
 * <AUTHOR>
 * @since 2024-01-04 10:10:46
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RobotRegionalDTO extends RobotRegional {
    private static final long serialVersionUID = 1L;

    /**
     * 国家语言
     * @see org.springblade.thingcom.translate.enums.LanguageTypeEnum
     */
    private String lang;

    /**
     * id，多个使用英文逗号分隔
     */
    private String ids;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 正常状态
     */
    private Integer normalStatus;

    /**
     * 离线状态
     */
    private Integer offlineStatus;

    /**
     * 运行状态
     */
    private Integer runStatus;

    /**
     * 预警状态
     */
    private Integer warnStatus;

    /**
     * 告警状态
     */
    private Integer alarmStatus;

    /**
     * 清扫范围最小角度
     */
    private BigDecimal clearingAngleMin;

    /**
     * 清扫范围最大角度
     */
    private BigDecimal clearingAngleMax;

    /**
     * 排除的id，多个使用英文逗号分隔
     * 主要用于分区配置中，过滤掉已经绑定的分区
     */
    private String excludeIds;

    public Integer getNormalStatus() {
        return normalStatus = DeviceStatusEnum.NORMAL.getValue();
    }

    public Integer getOfflineStatus() {
        return offlineStatus = DeviceStatusEnum.OFFLINE.getValue();
    }

    public Integer getRunStatus() {
        return runStatus = DeviceStatusEnum.RUN.getValue();
    }

    public Integer getWarnStatus() {
        return warnStatus = DeviceStatusEnum.WARN.getValue();
    }

    public Integer getAlarmStatus() {
        return alarmStatus = DeviceStatusEnum.ALARM.getValue();
    }
}
package org.springblade.thingcom.device.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @ClassName TrackerSaveDTO
 * @Description: TODO
 * @Author: zgq
 * @Date: 2024/1/3 16:04
 * @Version: 1.0
 **/
@Data
public class TrackerSaveDTO {

    /**
     * 名称前缀
     */
    @NotNull(message = "名称前缀不能为空")
    private String namePrefix;

    /**
     * ids，多个使用英文逗号分隔
     */
    @NotBlank(message = "ids不能为空")
    private String ids;
}

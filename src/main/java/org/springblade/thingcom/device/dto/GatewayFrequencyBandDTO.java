package org.springblade.thingcom.device.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description
 * @date 2024/4/19 11:02
 */
@Data
public class GatewayFrequencyBandDTO {


    private BigDecimal r0;
    private BigDecimal r1;
    /**
     * 起始通道
     */
    private Integer frequencyRange;
    /**
     * IP地址
     */
    private String ip;

    /**
     * 传参类型
     */
    private String type;

    /**
     * 传参值
     */
    private String value;
}

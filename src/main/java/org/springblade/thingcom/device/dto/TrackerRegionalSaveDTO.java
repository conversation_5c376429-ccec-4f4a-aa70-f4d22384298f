package org.springblade.thingcom.device.dto;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @ClassName TrackerRegionalSaveDTO
 * @Description: TODO
 * @Author: zgq
 * @Date: 2024/1/3 16:05
 * @Version: 1.0
 **/
@Data
public class TrackerRegionalSaveDTO {

    /**
     * ids，多个使用英文逗号分隔
     */
    @NotBlank(message = "ids不能为空")
    private String ids;

    /**
     * 名称前缀
     */
    @NotNull(message = "名称前缀不能为空")
    private String namePrefix;

    /**
     * 跟踪器数量
     */
    @Min(value = 0, message = "跟踪器数量不能小于0")
    private Integer trackerCount;
}

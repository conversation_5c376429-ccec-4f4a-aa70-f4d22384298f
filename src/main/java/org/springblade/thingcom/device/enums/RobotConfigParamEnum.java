package org.springblade.thingcom.device.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @ClassName RobotConfigParamEnum
 * @Description 机器人配置参数枚举类
 * <AUTHOR>
 * @Date 2024/7/24 15:02
 * @Version 1.0
 **/
@Getter
@AllArgsConstructor
public enum RobotConfigParamEnum {

    /**
     * 控制板参数
     */
    CONTROL_BOARD(0, "控制板参数"),
    /**
     * 定时器
     */
    TIMER(1, "定时设置"),
    /**
     * 电池
     */
    BATTERY(2, "电池参数"),
    /**
     * LoRa
     */
    LORA(3, "LoRa参数"),
    /**
     * 风速
     */
    WIND_SPEED(4, "风速阈值"),
    /**
     * 停机位
     */
    STOP_BIT(5, "停位设置"),
    /**
     * 操作设置
     */
    OPERATION(6, "操作设置"),
    /**
     * 主板温度
     */
    MAIN_BOARD_TEMPERATURE(7, "主板温度"),

    ;

    private final Integer value;
    private final String label;

    public static RobotConfigParamEnum getByValue(Integer value) {
        for (RobotConfigParamEnum e : RobotConfigParamEnum.values()) {
            if (e.getValue().equals(value)) {
                return e;
            }
        }
        return null;
    }
}

package org.springblade.thingcom.device.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 升级状态枚举
 */
@Getter
@AllArgsConstructor
public enum UpgradeStatusEnum {

    /**
     * 未升级（升级失败）
     */
    UNUPGRADE(0, "未升级"),

    /**
     * 升级中
     */
    UPGRADEING(1, "升级中"),

    /**
     * 已升级（升级成功）
     */
    UPGRADE(2, "已升级"),

    ;

    final Integer value;
    final String label;

    public static String translationValue(Integer value) {
        for (UpgradeStatusEnum e : values()) {
            if (e.value.equals(value)) {
                return e.label;
            }
        }
        return UNUPGRADE.label;
    }
}

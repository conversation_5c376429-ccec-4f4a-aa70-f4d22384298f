package org.springblade.thingcom.device.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 运行模式枚举
 */
@Getter
@AllArgsConstructor
public enum RunModelEnum {

    /**
     * 手动模式
     */
    MANUAL(1, "手动"),

    /**
     * 自动模式
     */
    AUTO(2, "自动"),

    ;

    private final Integer value;
    private final String label;

    /**
     * 翻译编码
     */
    public static String translationValue(Integer value) {
        for (RunModelEnum e : RunModelEnum.values()) {
            if (e.value.equals(value)) {
                return e.label;
            }
        }
        return null;
    }

}

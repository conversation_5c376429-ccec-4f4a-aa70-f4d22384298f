package org.springblade.thingcom.device.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 环境告警类型枚举
 */
@Getter
@AllArgsConstructor
public enum EnvironmentAlarmTypeEnum {

    /**
     * 风速告警
     */
    WIND_SPEED(1, "风速告警"),

    /**
     * 温度告警
     */
    TEMPERATURE(2, "告警"),

    ;

    /**
     * 编码
     */
    private final Integer value;

    /**
     * 翻译编码
     */
    private final String label;

}

package org.springblade.thingcom.device.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springblade.core.tool.support.Kv;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName ClearingStatusEnum
 * @Description 清洗状态枚举
 * <AUTHOR>
 * @Date 2024/8/13 19:05
 * @Version 1.0
 **/
@Getter
@AllArgsConstructor
public enum ClearingStatusEnum {

    /**
     * 未清扫
     */
    UNCLEAN(0, "未清扫"),

    /**
     * 已清扫
     */
    CLEANED(1, "已清扫"),

    ;

    final Integer value;
    final String label;

    public static String translationValue(Integer value) {
        return Arrays.stream(ClearingStatusEnum.values())
                .filter(item -> item.getValue().equals(value))
                .findFirst().orElse(UNCLEAN).getLabel();
    }

    public static List<Kv> toList() {
        return Arrays.stream(ClearingStatusEnum.values())
                .map(item -> Kv.create().set("value", item.getValue())
                        .set("label", item.getLabel()))
                .collect(Collectors.toList());
    }
}
package org.springblade.thingcom.device.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 锁定状态枚举
 */
@Getter
@AllArgsConstructor
public enum LockStatusEnum {

    /**
     * 未锁定
     */
    UNLOCK(1, "未锁定"),

    /**
     * 锁定
     */
    LOCK(2, "已锁定"),

    ;

    private final Integer value;
    private final String label;

    /**
     * 翻译编码
     */
    public static String translationValue(Integer value) {
        for (LockStatusEnum e : LockStatusEnum.values()) {
            if (e.value.equals(value)) {
                return e.label;
            }
        }
        return null;
    }

}

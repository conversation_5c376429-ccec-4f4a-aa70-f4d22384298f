package org.springblade.thingcom.device.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 告警状态枚举
 */
@Getter
@AllArgsConstructor
public enum AlarmStatusEnum {

    /**
     * 正常
     */
    NORMAL(1, "正常"),

    /**
     * 预警
     */
    WARN(2, "预警"),

    /**
     * 告警
     */
    ALARM(3, "告警"),

    ;

    /**
     * 编码
     */
    private final Integer value;

    /**
     * 翻译编码
     */
    private final String label;

    /**
     * 翻译编码
     */
    public static String translationValue(Integer value) {
        for (AlarmStatusEnum e : AlarmStatusEnum.values()) {
            if (e.value.equals(value)) {
                return e.label;
            }
        }
        return null;
    }

}

package org.springblade.thingcom.device.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.springblade.thingcom.core.mybatisPlus.IBaseMapper;
import org.springblade.thingcom.device.dto.ClearingPathDTO;
import org.springblade.thingcom.device.entity.ClearingPath;
import org.springblade.thingcom.device.vo.ClearingPathVO;

/**
 * 清扫路径数据库访问层
 *
 * <AUTHOR>
 * @since 2023-12-14 09:37:22
 */
public interface ClearingPathMapper extends IBaseMapper<ClearingPath> {

    /**
     * 分页查询清扫路径数据
     *
     * @param page
     * @param dto
     * @return
     */
    IPage<ClearingPathVO> page(IPage<ClearingPath> page, @Param("param") ClearingPathDTO dto);
}

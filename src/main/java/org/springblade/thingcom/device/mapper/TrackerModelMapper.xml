<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.thingcom.device.mapper.TrackerModelMapper">
    <resultMap type="org.springblade.thingcom.device.entity.TrackerModel" id="TrackerModelMap">
        <result column="id" property="id"/>
        <result column="model_name" property="modelName"/>
        <result column="model_length" property="modelLength"/>
        <result column="remark" property="remark"/>
        <result column="creator_id" property="creatorId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_id" property="updateId"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="deleted"/>
    </resultMap>
</mapper>

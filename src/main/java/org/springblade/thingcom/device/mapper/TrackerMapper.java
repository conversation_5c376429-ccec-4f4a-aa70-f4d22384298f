package org.springblade.thingcom.device.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springblade.thingcom.core.mybatisPlus.IBaseMapper;
import org.springblade.thingcom.device.dto.ClearingPathDTO;
import org.springblade.thingcom.device.entity.ClearingPath;
import org.springblade.thingcom.device.entity.Tracker;
import org.springblade.thingcom.device.vo.ClearingPathVO;
import org.springblade.thingcom.device.vo.TrackerVO;

import java.util.List;

/**
 * 跟踪器数据库访问层
 *
 * <AUTHOR>
 * @since 2024-01-03 15:19:53
 */
@Mapper
public interface TrackerMapper extends IBaseMapper<Tracker> {

    /**
     * 分页查询清扫路径对应的跟踪器参数
     *
     * @param page
     * @param
     * @return
     */
    IPage<ClearingPathVO> pageTrackerParam(IPage<ClearingPath> page, @Param("param") ClearingPathDTO dto);

    /**
     * 列表查询清扫路径对应的跟踪器参数
     *
     * @param id
     * @return
     */
    List<ClearingPathVO> listTrackerParam(@Param("param") ClearingPathDTO dto);

    /**
     * 分页查询绑定关系跟踪器数据
     *
     * @param page
     * @param dto
     * @return
     */
    IPage<ClearingPathVO> pageTrackerBind(IPage<ClearingPath> page, @Param("param") ClearingPathDTO dto);

    /**
     * 列表查询绑定关系跟踪器数据
     *
     * @param page
     * @param dto
     * @return
     */
    List<ClearingPathVO> listTrackerBind(@Param("param") ClearingPathDTO dto);

    /**
     * 根据id集合查询跟踪器数据（查询所有的数据：包括：is_deleted=0/is_deleted=1）
     * 其中：is_deleted=1，代码需要判断是不是删除的数据，如果是删除的数据，则无法下发指令
     *
     * @param trackerIdList
     * @return
     */
    List<Tracker> listByIdList(List<Long> trackerIdList);

    /**
     * 根据清扫路径id查询告警跟踪器数据
     *
     * @param clearingPathId
     * @param alarmStatus
     * @return
     */
    Tracker getAlarmTrackerByClearingPathId(Long clearingPathId, Integer alarmStatus);

    /**
     * 根据清扫路径id查询绑定的跟踪器
     *
     * @param clearingPathId
     * @return
     */
    List<Tracker> listTrackerBindByClearingPathId(Long clearingPathId);

    /**
     * 根据清扫路径id列表查询绑定的跟踪器
     *
     * @param clearingPathIdList
     * @return
     */
    List<TrackerVO> listTrackerBindByClearingPathIdList(List<Long> clearingPathIdList);

    /**
     * 根据清扫路径id查询跟踪器列表
     *
     * @param clearingPathId
     * @return
     */
    List<TrackerVO> listTrackerByClearingPathId(Long clearingPathId);
}

package org.springblade.thingcom.device.mapper;

import org.springblade.thingcom.core.mybatisPlus.IBaseMapper;
import org.springblade.thingcom.device.entity.TransmitRobotData;

/**
 * 转发通道机器人数据数据库访问层
 *
 * <AUTHOR>
 * @since 2024-08-01 23:56:51
 */
public interface TransmitRobotDataMapper extends IBaseMapper<TransmitRobotData> {

    /**
     * 根据端口删除转发通道机器人数据
     *
     * @param port
     */
    void deleteByPort(Integer port);
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.thingcom.device.mapper.RobotMapper">

    <resultMap id="queryList" type="org.springblade.thingcom.device.vo.RobotVO">
        <result column="id" property="id"/>
        <result column="robot_number" property="robotNumber"/>
        <result column="name" property="name"/>
        <result column="eui" property="eui"/>
        <result column="status" property="status"/>
        <result column="upgrade_status" property="upgradeStatus"/>
        <!-- 多表关联映射 -->
        <collection property="trackerList" ofType="org.springblade.thingcom.device.entity.Tracker">
            <result column="tracker_number" property="trackerNumber"/>
            <result column="tracker_name" property="name"/>
            <result column="angle" property="angle"/>
            <result column="tracker_status" property="status"/>
        </collection>
    </resultMap>

    <select id="page" resultMap="queryList">
        SELECT      t1.*
                    ,t3.tracker_number
                    ,t3.name AS tracker_name
                    ,t3.angle
                    ,t3.status AS tracker_status
        FROM        tb_robot AS t1
        LEFT JOIN   tb_clearing_path AS t2
        ON          t1.clearing_path_id = t2.id
        AND         t2.is_deleted = 0
        LEFT JOIN
             (
                 SELECT         t2.clearing_path_id
                                ,t1.*
                 FROM           tb_tracker AS t1
                 INNER JOIN     tr_clearing_path_tracker AS t2
                 ON             t1.id = t2.tracker_id
                 AND            t1.is_deleted = 0
                 AND            t2.is_deleted = 0
             ) AS t3
        ON           t2.id = t3.clearing_path_id
        WHERE        t1.is_deleted = 0
        <if test="param.robotRegionalId != null">
            AND t1.robot_regional_id = #{param.robotRegionalId}
        </if>
        <if test="param.name != null and param.name != ''">
            AND t1.name LIKE CONCAT('%', #{param.name}, '%')
        </if>
        <if test="param.status != null">
            AND t1.status = #{param.status}
        </if>
        <if test="param.licenseAuth != null">
            AND t1.license_auth = #{param.licenseAuth}
        </if>
        <if test="param.idList != null and param.idList.size() > 0">
            AND t1.id IN
            <foreach collection="param.idList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.trackerStatus != null">
            <choose>
                <when test="param.trackerStatus == param.trackerNormalStatus">
                    AND (t3.status = #{param.trackerStatus} OR t1.tracker_id = 0)
                </when>
                <otherwise>
                    AND t3.status = #{param.trackerStatus}
                </otherwise>
            </choose>
        </if>
        ORDER BY    t1.robot_number + 0
    </select>

    <select id="countClearingRobot" resultType="long">
        SELECT      COUNT(*)
        FROM        tb_robot AS t1
        INNER JOIN  tb_robot_clearing AS t2
        ON          t1.id = t2.robot_id
        AND         t2.is_deleted = 0
        AND         t2.work_duration_total > 0
        WHERE       t1.is_deleted = 0
        <if test="param.robotRegionalId != null">
            AND     t1.robot_regional_id = #{param.robotRegionalId}
        </if>
        <if test="param.beginDate != null and param.endDate != null">
            AND     t2.time BETWEEN #{param.beginDate} AND #{param.endDate}
        </if>
    </select>

    <select id="listTransmitRobot" resultType="org.springblade.thingcom.device.entity.Robot">
        SELECT      t1.*
        FROM        tb_robot AS t1
        INNER JOIN  tb_robot_regional AS t2
        ON          t1.robot_regional_id = t2.id
        AND         t2.is_deleted = 0
        WHERE       t1.is_deleted = 0
        AND         t2.id IN
        <foreach collection="robotRegionalIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        ORDER BY    t2.regional_number, t1.robot_number + 0
    </select>

    <select id="listTransmitClearingRobot" resultType="org.springblade.thingcom.device.entity.Robot">
        SELECT      t1.*
        FROM        tb_robot AS t1
        INNER JOIN  tb_robot_regional AS t2
        ON          t1.robot_regional_id = t2.id
        AND         t2.is_deleted = 0
        INNER JOIN  tb_robot_clearing AS t3
        ON          t1.id = t3.robot_id
        AND         t3.is_deleted = 0
        AND         t3.time BETWEEN #{beginTime} AND #{endTime}
        AND         t3.work_duration_total > 0
        WHERE       t1.is_deleted = 0
        AND         t2.id IN
        <foreach collection="robotRegionalIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        ORDER BY    t2.regional_number, t1.robot_number + 0
    </select>

    <select id="exportRobotTodayClearingData" resultType="org.springblade.thingcom.home.excel.RobotClearingDataExcel">
        SELECT      t2.regional_number
                    ,t1.robot_number
                    ,t1.name AS robotName
                    ,IF(t3.id IS NULL, '0', '1') AS clearingStatus
        FROM        tb_robot AS t1
        INNER JOIN  tb_robot_regional AS t2
        ON          t1.robot_regional_id = t2.id
        AND         t2.is_deleted = 0
        LEFT JOIN   tb_robot_clearing AS t3
        ON          t1.id = t3.robot_id
        AND         t3.is_deleted = 0
        AND         DATE(t3.time) = CURDATE()
        AND         t3.work_duration_total > 0
        WHERE       t1.is_deleted = 0
        ORDER BY    t2.regional_number, t1.robot_number + 0
    </select>

    <select id="robotClearingStatis" resultType="org.springblade.thingcom.operationMaintenance.vo.RobotClearingVO">
        SELECT      t1.status AS robotStatus
                    ,COUNT(t1.id) AS robotCount
        FROM        tb_robot AS t1
        INNER JOIN  tb_robot_clearing AS t2
        ON          t1.id = t2.robot_id
        AND         t2.is_deleted = 0
        WHERE       t1.is_deleted = 0
        <if test="param.robotRegionalId != null">
            AND     t1.robot_regional_id = #{param.robotRegionalId}
        </if>
        <if test="param.robotId != null">
            AND     t1.id = #{param.robotId}
        </if>
        <if test="param.robotStatus != null">
            AND     t1.status = #{param.robotStatus}
        </if>
        <if test="param.robotSoftwareVersion != null and param.robotSoftwareVersion != ''">
            AND t1.robot_software_version = #{param.robotSoftwareVersion}
        </if>
        <if test="param.beginDate != null and param.endDate != null">
            AND     t2.time BETWEEN #{param.beginDate} AND #{param.endDate}
        </if>
        GROUP BY    t1.status
    </select>

</mapper>
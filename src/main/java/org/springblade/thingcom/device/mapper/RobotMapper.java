package org.springblade.thingcom.device.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springblade.thingcom.core.mybatisPlus.IBaseMapper;
import org.springblade.thingcom.device.dto.RobotDTO;
import org.springblade.thingcom.device.entity.Robot;
import org.springblade.thingcom.device.vo.RobotVO;
import org.springblade.thingcom.home.excel.RobotClearingDataExcel;
import org.springblade.thingcom.operationMaintenance.dto.RobotClearingDTO;
import org.springblade.thingcom.operationMaintenance.vo.RobotClearingVO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 机器人数据库访问层
 *
 * <AUTHOR>
 * @since 2024-01-03 19:25:49
 */
@Mapper
public interface RobotMapper extends IBaseMapper<Robot> {

    /**
     * 分页查询机器人列表
     *
     * @param page 分页对象
     * @param dto  查询条件
     * @return 机器人列表
     */
    IPage<RobotVO> page(IPage<Robot> page, @Param("param") RobotDTO dto);

    /**
     * 查询清扫的机器人数量
     *
     * @param dto
     * @return 机器人数量
     */
    long countClearingRobot(@Param("param") RobotClearingDTO dto);

    /**
     * 根据机器人分区id列表查询转发通道的机器人列表（按照分区编号、机器人编号升序排序）
     *
     * @param robotRegionalIdList 机器人分区id集合
     * @return 机器人集合
     */
    List<Robot> listTransmitRobot(List<Long> robotRegionalIdList);

    /**
     * 根据机器人分区id列表查询转发通道的清扫机器人列表（按照分区编号、机器人编号升序排序）
     *
     * @param robotRegionalIdList 机器人分区id列表
     * @param beginTime           开始时间
     * @param endTime             结束时间
     * @return
     */
    List<Robot> listTransmitClearingRobot(List<Long> robotRegionalIdList
            , LocalDateTime beginTime, LocalDateTime endTime);

    /**
     * 导出今日清扫机器人数据
     *
     * @return 机器人清扫数据集合
     */
    List<RobotClearingDataExcel> exportRobotTodayClearingData();

    /**
     * 统计清扫的机器人状态数量
     *
     * @param dto 统计条件
     * @return 机器人清扫数据集合
     */
    List<RobotClearingVO> robotClearingStatis(@Param("param") RobotClearingDTO dto);

}

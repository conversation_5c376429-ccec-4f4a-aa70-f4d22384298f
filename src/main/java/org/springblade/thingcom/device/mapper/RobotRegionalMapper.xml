<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.thingcom.device.mapper.RobotRegionalMapper">
    <resultMap type="org.springblade.thingcom.device.entity.RobotRegional" id="RobotRegionalMap">
        <result column="id" property="id"/>
        <result column="regional_number" property="regionalNumber"/>
        <result column="creator_id" property="creatorId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_id" property="updateId"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="deleted"/>
    </resultMap>

    <sql id="queryList">
        SELECT      t1.id
                    ,t1.regional_number
                    ,COUNT(t2.id) AS robotCount
                    ,IFNULL(SUM(t2.status = #{param.offlineStatus}), 0) AS offlineCount
                    ,IFNULL(SUM(t2.status = #{param.runStatus}), 0) AS runCount
                    ,IFNULL(SUM(t2.status = #{param.warnStatus}), 0) AS warnCount
                    ,IFNULL(SUM(t2.status = #{param.alarmStatus}), 0) AS alarmCount
                    ,IFNULL(SUM(t2.status = 1), 0) AS normalCount
                    ,COUNT(t3.id) AS bracketAngleAstrictCount
        FROM        tb_robot_regional AS t1
        LEFT JOIN   tb_robot AS t2
        ON          t1.id = t2.robot_regional_id
        AND         t2.is_deleted = 0
        LEFT JOIN   tb_tracker AS t3
        ON          t2.tracker_id = t3.id
        AND         t3.is_deleted = 0
        AND         (t3.angle &lt; #{systemSet.clearingAngleMin} OR t3.angle &gt; #{systemSet.clearingAngleMax})
        WHERE       t1.is_deleted = 0
        <if test="param.regionalNumber != null and param.regionalNumber != ''">
            AND t1.regional_number LIKE CONCAT('%', #{param.regionalNumber}, '%')
        </if>
        <if test="param.status != null">
            <choose>
                <when test="param.status == param.normalStatus">
                    AND t1.id NOT IN
                    (
                     SELECT robot_regional_id FROM tb_robot WHERE is_deleted = 0
                     AND status IN (#{param.warnStatus}, #{param.alarmStatus}, #{param.offlineStatus})
                     GROUP BY robot_regional_id
                    )
                </when>
                <otherwise>
                    AND t1.id IN
                    (
                    SELECT robot_regional_id FROM tb_robot WHERE is_deleted = 0
                    AND status = #{param.status} GROUP BY robot_regional_id
                    )
                </otherwise>
            </choose>
        </if>
        <if test="idList != null and idList.size() > 0">
            AND t1.id IN
            <foreach collection="idList" item="item" index ="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="excludeIdList != null and excludeIdList.size() > 0">
            AND t1.id NOT IN
            <foreach collection="excludeIdList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY t1.id
        ORDER BY t1.regional_number + 0
    </sql>

    <sql id="queryDetail">
        SELECT      t1.id
                    ,t1.regional_number
                    ,COUNT(t2.id) AS robotCount
                    ,IFNULL(SUM(t2.status = #{param.offlineStatus}), 0) AS offlineCount
                    ,IFNULL(SUM(t2.status = #{param.runStatus}), 0) AS runCount
                    ,IFNULL(SUM(t2.status = #{param.warnStatus}), 0) AS warnCount
                    ,IFNULL(SUM(t2.status = #{param.alarmStatus}), 0) AS alarmCount
                    ,IFNULL(SUM(t2.status = 1), 0) AS normalCount
                    ,COUNT(t3.id) AS bracketAngleAstrictCount
        FROM        tb_robot_regional AS t1
        LEFT JOIN   tb_robot AS t2
        ON          t1.id = t2.robot_regional_id
        AND         t2.is_deleted = 0
        AND         t2.robot_regional_id = #{param.id}
        LEFT JOIN   tb_tracker AS t3
        ON          t2.tracker_id = t3.id
        AND         t3.is_deleted = 0
        AND         (t3.angle &lt; #{param.clearingAngleMin} OR t3.angle &gt; #{param.clearingAngleMax})
        WHERE       t1.is_deleted = 0
        AND         t1.id = #{param.id}
    </sql>

    <select id="page" resultType="org.springblade.thingcom.device.vo.RobotRegionalVO">
        <include refid="queryList"/>
    </select>

    <select id="list" resultType="org.springblade.thingcom.device.vo.RobotRegionalVO">
        <include refid="queryList"/>
    </select>

    <select id="getById" resultType="org.springblade.thingcom.device.vo.RobotRegionalVO">
        <include refid="queryDetail"/>
    </select>

    <select id="exportData" resultType="org.springblade.thingcom.device.excel.RobotRegionalExcel$DataExport">
        <include refid="queryList"/>
    </select>

</mapper>
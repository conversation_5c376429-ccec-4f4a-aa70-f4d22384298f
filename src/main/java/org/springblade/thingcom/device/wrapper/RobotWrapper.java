package org.springblade.thingcom.device.wrapper;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.thingcom.base.cache.SystemCache;
import org.springblade.thingcom.base.entity.SystemSet;
import org.springblade.thingcom.command.cache.ControlTypeCache;
import org.springblade.thingcom.command.entity.ControlType;
import org.springblade.thingcom.command.enums.ControlTypeCodeEnum;
import org.springblade.thingcom.communication.entity.ControlBoard;
import org.springblade.thingcom.communication.entity.MainBoardTemperature;
import org.springblade.thingcom.communication.entity.Wind;
import org.springblade.thingcom.communication.enums.CommunicationStatusEnum;
import org.springblade.thingcom.communication.enums.ControlSwitchEnum;
import org.springblade.thingcom.communication.enums.MotorTypeEnum;
import org.springblade.thingcom.communication.enums.StopBitEnum;
import org.springblade.thingcom.communication.vo.BatteryVO;
import org.springblade.thingcom.communication.vo.LoraVO;
import org.springblade.thingcom.communication.vo.TimerVO;
import org.springblade.thingcom.data.utils.RobotDataUtil;
import org.springblade.thingcom.data.vo.RobotTrackerLocationVO;
import org.springblade.thingcom.device.cache.RobotDataCache;
import org.springblade.thingcom.device.cache.RobotRegionalCache;
import org.springblade.thingcom.device.dto.RobotDTO;
import org.springblade.thingcom.device.entity.Robot;
import org.springblade.thingcom.device.entity.RobotRegional;
import org.springblade.thingcom.device.entity.Tracker;
import org.springblade.thingcom.device.enums.*;
import org.springblade.thingcom.device.excel.*;
import org.springblade.thingcom.device.service.TrackerService;
import org.springblade.thingcom.device.vo.RobotParamVO;
import org.springblade.thingcom.device.vo.RobotRealDataVO;
import org.springblade.thingcom.device.vo.RobotVO;
import org.springblade.thingcom.device.vo.TrackerVO;
import org.springblade.thingcom.home.vo.RobotMapVO;
import org.springblade.thingcom.operationMaintenance.entity.RobotClearing;
import org.springblade.thingcom.operationMaintenance.service.RobotClearingService;
import org.springblade.thingcom.statistic.entity.RobotBattery;
import org.springblade.thingcom.statistic.entity.RobotMotor;
import org.springblade.thingcom.translate.utils.TranslateUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 机器人包装类
 *
 * <AUTHOR>
 * @since 2024-01-03 19:25:50
 */
public class RobotWrapper extends BaseEntityWrapper<Robot, RobotVO> {

    private static final TrackerService trackerService;
    private static final RobotClearingService robotClearingService;

    static {
        trackerService = SpringUtil.getBean(TrackerService.class);
        robotClearingService = SpringUtil.getBean(RobotClearingService.class);
    }

    public static RobotWrapper build() {
        return new RobotWrapper();
    }

    /**
     * 返回视图层所需的字段
     *
     * @param entity
     * @return RobotVO
     */
    @Override
    public RobotVO entityVO(Robot entity) {
        if (Objects.isNull(entity)) {
            return null;
        }
        return BeanUtil.copyProperties(entity, RobotVO.class);
    }

    /**
     * 机器人包装类,返回视图层所需的字段（针对详情）
     *
     * @param entity
     * @param systemSet
     * @return
     */
    public RobotVO entityVoExt(Robot entity, SystemSet systemSet) {
        if (Objects.isNull(entity)) {
            return null;
        }
        RobotVO vo = BeanUtil.copyProperties(entity, RobotVO.class);
        vo.setRobotNumber(StrUtil.concat(true, entity.getRobotNumberPrefix(), entity.getRobotNumber()));
        // 机器人区域编号
        if (Objects.nonNull(vo.getRobotRegionalId())) {
            RobotRegional robotRegional = RobotRegionalCache.getById(vo.getRobotRegionalId());
            if (Objects.nonNull(robotRegional)) {
                vo.setRobotRegionalNumber(robotRegional.getRegionalNumber());
            }
        }
        // 机器人清扫路径
        if (Objects.nonNull(vo.getClearingPathId())) {
            List<TrackerVO> trackerVoList = trackerService.listTrackerByClearingPathId(vo.getClearingPathId());
            // 拼接 trackerVoList 里面的 trackerRegionalNumber、trackerName
            String clearingPath = trackerVoList.stream().map(m ->
                            StrUtil.concat(true, m.getTrackerRegionalNumber(), " ", m.getName()))
                    .collect(Collectors.joining(","));
            vo.setClearingPath(clearingPath);
        }
        // 清扫数据
        RobotClearing robotClearing = robotClearingService.getOne(Wrappers.<RobotClearing>lambdaQuery()
                .eq(RobotClearing::getRobotId, entity.getId()).last("LIMIT 1"));
        double mileageTotal = Objects.isNull(robotClearing) ? 0 : robotClearing.getMileageTotal() / 100.0;
        vo.setMileageTotal(mileageTotal + CommonConstant.METERS);
        // 锁定状态名称
        vo.setLockStatusName(LockStatusEnum.translationValue(vo.getLockStatus()));
        // 机器人状态名称
        vo.setStatusName(DeviceStatusEnum.translationValue(vo.getStatus()));
        // 运行模式名称
        vo.setRunModelName(RunModelEnum.translationValue(vo.getRunModel()));
        // 通信状态名称
        vo.setCommunicationStatusName(CommunicationStatusEnum.translationValue(vo.getCommunicationStatus()));
        // 停机位名称
        vo.setStopBitName(StopBitEnum.translationValue(vo.getStopBit()));
        // 白天防误扫开开关
        vo.setDaytimePreventCleanName(ControlSwitchEnum.translationValue(vo.getDaytimePreventClean()));
        // 光线传感器状态名称
        vo.setLightSensorStatusName(LightSensorStatusEnum.translationValue(vo.getLightSensorStatus()));
        // 升级状态名称
        vo.setUpgradeStatusName(UpgradeStatusEnum.translationValue(vo.getUpgradeStatus()));
        // 急停开关
        vo.setEmergencyStopStatusName(EmergencyStopStatusEnum.translationValue(vo.getEmergencyStopStatus()));
        // 机器人位置，计算当前处于哪个跟踪器
        RobotRealDataVO robotLocationAndDirection = RobotDataUtil.getRobotRealDataByEui(vo.getEui());
        Integer location = robotLocationAndDirection.getLocation();
        Integer direction = robotLocationAndDirection.getDirection();
        RobotTrackerLocationVO robotTrackerLocation
                = RobotDataUtil.getRobotTrackerLocation(vo.getClearingPathId(), systemSet, location, direction);
        vo.setRunLocation(robotTrackerLocation.getLocationInfo());
        vo.setTrackerName(robotTrackerLocation.getLocationInfo());
        vo.setTrackerStatus(robotTrackerLocation.getTrackerStatus());
        vo.setTrackerStatusName(robotTrackerLocation.getTrackerStatusName());
        // 获取机器人参数
        RobotParamVO robotParamVo = RobotDataCache.getParamDataByRobotEui(vo.getEui());
        if (Objects.nonNull(robotParamVo)) {
            vo.setSoftwareVersion(robotParamVo.getSoftwareVersion());
            vo.setPvPanelOutputVoltage(robotParamVo.getPvPanelOutputVoltage());
            vo.setPvPanelOutputElectricity(robotParamVo.getPvPanelOutputElectricity());
            vo.setTime(robotParamVo.getTime());
            vo.setWeek(robotParamVo.getWeek());
            vo.setLora(robotParamVo.getLora());
            vo.setBattery(robotParamVo.getBattery());
            vo.setControlBoard(robotParamVo.getControlBoard());
            vo.setWind(robotParamVo.getWind());
            vo.setTimerList(robotParamVo.getTimerList());
            vo.setMainBoardTemperature(robotParamVo.getMainBoardTemperature());
        }
        return vo;
    }

    public void voWrapper(RobotVO vo, SystemSet systemSet) {
        // 锁定状态名称
        vo.setLockStatusName(LockStatusEnum.translationValue(vo.getLockStatus()));
        // 机器人状态名称
        vo.setStatusName(DeviceStatusEnum.translationValue(vo.getStatus()));
        // 通信状态名称
        vo.setCommunicationStatusName(CommunicationStatusEnum.translationValue(vo.getCommunicationStatus()));
        // 机器人位置，计算当前处于哪个跟踪器
        RobotTrackerLocationVO robotTrackerLocation
                = RobotDataUtil.getRobotTrackerLocation(vo.getEui(), vo.getTrackerList(), systemSet);
        vo.setRunLocation(robotTrackerLocation.getLocationInfo());
        vo.setTrackerName(robotTrackerLocation.getLocationInfo());
        vo.setTrackerStatus(robotTrackerLocation.getTrackerStatus());
        vo.setTrackerStatusName(robotTrackerLocation.getTrackerStatusName());
    }

    public List<RobotVO> listVoExt(List<RobotVO> list, SystemSet systemSet) {
        return list.stream().map(m -> {
                    voWrapper(m, systemSet);
                    return m;
                })
                .collect(Collectors.toList());
    }

    public IPage<RobotVO> pageVoExt(IPage<RobotVO> pages) {
        // 系统配置
        SystemSet systemSet = SystemCache.getById(CommonConstant.SYSTEM_CONFIG_ID);
        List<RobotVO> records = this.listVoExt(pages.getRecords(), systemSet);
        IPage<RobotVO> pageVo = new Page(pages.getCurrent(), pages.getSize(), pages.getTotal());
        pageVo.setRecords(records);
        return pageVo;
    }

    /**
     * 机器人包装类,返回视图层所需的字段（设备地图）
     *
     * @param entity
     * @return
     */
    public RobotMapVO entityVoMap(Robot entity, SystemSet systemSet) {
        if (Objects.isNull(entity)) {
            return null;
        }
        RobotMapVO vo = BeanUtil.copyProperties(entity, RobotMapVO.class);
        vo.setRobotNumber(StrUtil.concat(true, entity.getRobotNumberPrefix(), entity.getRobotNumber()));
        // 机器人区域编号
        if (Objects.nonNull(vo.getRobotRegionalId())) {
            RobotRegional robotRegional = RobotRegionalCache.getById(vo.getRobotRegionalId());
            if (Objects.nonNull(robotRegional)) {
                vo.setRobotRegionalNumber(robotRegional.getRegionalNumber());
            }
        }
        // 锁定状态名称
        vo.setLockStatusName(LockStatusEnum.translationValue(vo.getLockStatus()));
        // 机器人状态名称
        vo.setStatusName(DeviceStatusEnum.translationValue(vo.getStatus()));
        // 运行模式名称
        vo.setRunModelName(RunModelEnum.translationValue(vo.getRunModel()));
        // 通信状态名称
        vo.setCommunicationStatusName(CommunicationStatusEnum.translationValue(vo.getCommunicationStatus()));
        // 光线传感器状态名称
        vo.setLightSensorStatusName(LightSensorStatusEnum.translationValue(vo.getLightSensorStatus()));
        // 升级状态名称
        vo.setUpgradeStatusName(UpgradeStatusEnum.translationValue(vo.getUpgradeStatus()));
        // 今日清扫状态
        RobotClearing robotClearing = robotClearingService.getOne(Wrappers.<RobotClearing>lambdaQuery()
                .eq(RobotClearing::getRobotId, entity.getId()).last("LIMIT 1"));
        Integer todayClearingStatus = RobotDataUtil.getTodayClearingStatus(robotClearing);
        String todayClearingStatusName = ClearingStatusEnum.translationValue(todayClearingStatus);
        vo.setTodayClearingStatus(todayClearingStatus);
        vo.setTodayClearingStatusName(todayClearingStatusName);
        // 机器人位置，计算当前处于哪个跟踪器
        RobotRealDataVO robotLocationAndDirection = RobotDataUtil.getRobotRealDataByEui(vo.getEui());
        Integer location = robotLocationAndDirection.getLocation();
        Integer direction = robotLocationAndDirection.getDirection();
        RobotTrackerLocationVO robotTrackerLocation
                = RobotDataUtil.getRobotTrackerLocation(vo.getClearingPathId(), systemSet, location, direction);
        vo.setRunLocation(robotTrackerLocation.getLocationInfo());
        vo.setTrackerName(robotTrackerLocation.getLocationInfo());
        vo.setTrackerStatus(robotTrackerLocation.getTrackerStatus());
        vo.setTrackerStatusName(robotTrackerLocation.getTrackerStatusName());
        // 获取机器人电池参数
        RobotBattery robotBattery = RobotDataCache.getBatteryDataByRobotEui(vo.getEui());
        if (Objects.nonNull(robotBattery)) {
            vo.setVoltage(robotBattery.getVoltage());
            vo.setTemperature(robotBattery.getTemperature());
            vo.setRemainingCapacity(robotBattery.getRemainingCapacity());
        }
        // 获取机器人电机参数
        RobotMotor robotMotor = RobotDataCache.getMotorDataByRobotEui(vo.getEui());
        if (Objects.nonNull(robotMotor)) {
            vo.setElectricity(robotMotor.getMasterElectricity());
            vo.setRunRate(robotMotor.getRunRate());
        }
        return vo;
    }

    /**
     * 机器人跟踪器位置转换
     *
     * @param entity
     * @param vo
     * @return
     */
    private static void robotTrackerLocationConvert(Robot entity, RobotMapVO vo, SystemSet systemSet) {
        // 机器人位置，计算当前处于哪个跟踪器
        RobotRealDataVO robotRealData = RobotDataUtil.getRobotRealDataByEui(entity.getEui());
        Integer location = robotRealData.getLocation();
        Integer direction = robotRealData.getDirection();
        RobotTrackerLocationVO robotTrackerLocation
                = RobotDataUtil.getRobotTrackerLocation(vo.getClearingPathId(), systemSet, location, direction);
        vo.setRunLocation(robotTrackerLocation.getLocationInfo());
        vo.setTrackerName(robotTrackerLocation.getLocationInfo());
        vo.setTrackerStatus(robotTrackerLocation.getTrackerStatus());
        vo.setTrackerStatusName(robotTrackerLocation.getTrackerStatusName());
    }

    /**
     * 数据列表转excel
     *
     * @param list
     * @return
     */
    public List<RobotExcel.DataExport> listToExcel(List<Robot> list, Boolean translate) {
        // 系统配置
        SystemSet systemSet = SystemCache.getById(CommonConstant.SYSTEM_CONFIG_ID);
        return list.stream().map(m -> {
            RobotExcel.DataExport excel = cn.hutool.core.bean.BeanUtil.copyProperties(m, RobotExcel.DataExport.class);
            // 机器人区域
            if (Objects.nonNull(m.getRobotRegionalId())) {
                excel.setRobotRegional(m.getRobotRegionalId().toString());
            }
            // 机器人清扫路径
            if (Objects.nonNull(m.getClearingPathId())) {
                List<TrackerVO> trackerVoList = trackerService.listTrackerByClearingPathId(m.getClearingPathId());
                // 拼接 trackerVoList 里面的 trackerRegionalNumber、trackerName
                String clearingPath = trackerVoList.stream()
                        .map(tracker -> tracker.getTrackerRegionalNumber() + " " + tracker.getName())
                        .collect(Collectors.joining(","));
                excel.setClearingPath(clearingPath);
            }
            // 机器人名称
            excel.setName(StrUtil.concat(true, m.getNamePrefix(), m.getRobotNumber()));
            // 机器人状态
            if (StrUtil.isNotBlank(excel.getStatus())) {
                // translate为真 常量名翻译
                if (translate) {
                    excel.setStatus(TranslateUtil.chineseToEnglish
                            (DeviceStatusEnum.translationValue(Func.toInt(excel.getStatus()))));
                } else {
                    excel.setStatus(DeviceStatusEnum.translationValue(Func.toInt(excel.getStatus())));
                }
            }
            // 锁定状态
            if (StrUtil.isNotBlank(excel.getLockStatus())) {
                if (translate) {
                    excel.setLockStatus(TranslateUtil.chineseToEnglish
                            (LockStatusEnum.translationValue(Func.toInt(excel.getLockStatus()))));
                } else {
                    excel.setLockStatus(LockStatusEnum.translationValue(Func.toInt(excel.getLockStatus())));
                }
            }
            // 运行模式
            if (StrUtil.isNotBlank(excel.getRunModel())) {
                // translate为真 常量名翻译
                if (translate) {
                    excel.setRunModel(TranslateUtil.chineseToEnglish
                            (RunModelEnum.translationValue(Func.toInt(excel.getRunModel()))));
                } else {
                    excel.setRunModel(RunModelEnum.translationValue(Func.toInt(excel.getRunModel())));
                }
            }
            // 光线传感器状态
            if (StrUtil.isNotBlank(excel.getLightSensorStatus())) {
                // translate为真 常量名翻译
                if (translate) {
                    excel.setRunModel(TranslateUtil.chineseToEnglish
                            (LightSensorStatusEnum.translationValue(Func.toInt(excel.getLightSensorStatus()))));
                } else {
                    excel.setLightSensorStatus(LightSensorStatusEnum.translationValue(Func.toInt(excel.getLightSensorStatus())));
                }
            }
            // 获取机器人位置和方向
            RobotRealDataVO robotRealData = RobotDataUtil.getRobotRealDataByEui(m.getEui());
            Integer location = robotRealData.getLocation();
            Integer direction = robotRealData.getDirection();
            RobotTrackerLocationVO robotTrackerLocation
                    = RobotDataUtil.getRobotTrackerLocation(m.getClearingPathId(), systemSet, location, direction);
            if (translate) {
                excel.setTrackerName(TranslateUtil.chineseToEnglish(robotTrackerLocation.getLocationInfo()));
                excel.setTrackerStatus(TranslateUtil.chineseToEnglish(robotTrackerLocation.getTrackerStatusName()));
            } else {
                excel.setTrackerName(robotTrackerLocation.getLocationInfo());
                excel.setTrackerStatus(robotTrackerLocation.getTrackerStatusName());
            }
            // 光伏板输出电压、电流
            excel.setPvPanelOutputVoltage(robotRealData.getPvPanelOutputVoltage());
            excel.setPvPanelOutputElectricity(robotRealData.getPvPanelOutputElectricity());
            return excel;
        }).collect(Collectors.toList());
    }

    /**
     * 设置查询参数
     *
     * @param dto
     * @return LambdaQueryWrapper<Robot>
     */
    public LambdaQueryWrapper<Robot> setQueryParam(RobotDTO dto) {
        LambdaQueryWrapper<Robot> lqw = new LambdaQueryWrapper<>();
        // 机器人分区
        lqw.eq(Objects.nonNull(dto.getRobotRegionalId()), Robot::getRobotRegionalId, dto.getRobotRegionalId());
        // 名称
        lqw.like(StrUtil.isNotBlank(dto.getName()), Robot::getName, dto.getName());
        // 机器人状态
        lqw.eq(Objects.nonNull(dto.getStatus()), Robot::getStatus, dto.getStatus());
        // License授权
        lqw.eq(Objects.nonNull(dto.getLicenseAuth()), Robot::getLicenseAuth, dto.getLicenseAuth());
        // 跟踪器状态
        if (Objects.nonNull(dto.getTrackerStatus())) {
            List<Tracker> list = trackerService.list(Wrappers.<Tracker>lambdaQuery().select(Tracker::getId)
                    .eq(Tracker::getStatus, dto.getTrackerStatus()));
            List<Long> trackerIdList;
            if (CollUtil.isEmpty(list)) {
                trackerIdList = CollUtil.toList(-1L);
            } else {
                trackerIdList = list.stream().map(Tracker::getId).collect(Collectors.toList());
            }
            // 正常状态下，需要包含未绑定跟踪器的机器人 (Robot::getTrackerId == 0)
            if (dto.getTrackerStatus().equals(TrackerStatusEnum.NORMAL.getValue())) {
                lqw.and(wrapper -> wrapper.in(Robot::getTrackerId, trackerIdList).or().eq(Robot::getTrackerId, 0L));
            } else {
                lqw.in(Robot::getTrackerId, trackerIdList);
            }
        }
        // id列表
        lqw.in(StrUtil.isNotBlank(dto.getIds()), Robot::getId, Func.toLongList(dto.getIds()));
        // 排序
        lqw.last("ORDER BY robot_number + 0");
        return lqw;
    }

    /**
     * 控制板参数列表导出
     *
     * @param controlBoard
     * @param translate
     * @return
     */
    public List<ControlPanelExcel> controlBoardToExcel(ControlBoard controlBoard, boolean translate) {
        ControlPanelExcel excel = BeanUtil.copy(controlBoard, ControlPanelExcel.class);
        controlBoard.getMotorList().forEach(motor -> {
            // 行走电机
            if (MotorTypeEnum.WALK.getValue().equals(motor.getMotorType())) {
                String runRate = motor.getRunRate();
                ControlType controlTypeRunRate = ControlTypeCache.getOneByCommandValue(ControlTypeCodeEnum.RUN_RATE.getLabel(), runRate);
                if (Objects.nonNull(controlTypeRunRate)) {
                    runRate = controlTypeRunRate.getValue();
                    if (translate) {
                        runRate = TranslateUtil.chineseToEnglish(runRate);
                    }
                }
                excel.setWalkMotorRunRate(runRate);
                excel.setWalkMotorElectricityMaxAstrictStop(motor.getElectricityMaxAstrictStop());
                excel.setWalkMotorElectricityMaxAstrictWarn(motor.getElectricityMaxAstrictWarn());
                excel.setWalkMotorOverstepRunTime(motor.getOverstepRunTime());
            }
            // 毛刷电机
            //if (MotorTypeEnum.BRUSH.getValue().equals(motor.getMotorType())) {
            //    excel.setBrushMotorUpperWarning(motor.getElectricityMaxAstrictWarn());
            //    excel.setBrushMotorUpperValue(motor.getElectricityMaxAstrictStop());
            //    excel.setBrushMotorRunTimeout(motor.getOverstepRunTime());
            //    excel.setBrushMotorOs(motor.getRunRate());
            //}
            // 防风电机
            //if (MotorTypeEnum.WIND_PROTECTION.getValue().equals(motor.getMotorType())) {
            //    excel.setWindproofMotorUpperWarning(motor.getElectricityMaxAstrictWarn());
            //    excel.setWindproofMotorUpperValue(motor.getElectricityMaxAstrictStop());
            //    excel.setWindproofMotorRunTimeout(motor.getOverstepRunTime());
            //    excel.setWindproofMotorOs(motor.getRunRate());
            //}
        });
        List<ControlPanelExcel> list = new ArrayList<>();
        list.add(excel);
        return list;
    }

    /**
     * 定时设置列表导出
     *
     * @param timerList
     * @param translate
     * @return
     */
    public List<TimerExcel> timeToExcel(List<TimerVO> timerList, boolean translate) {
        return timerList.stream().map(timer -> {
            TimerExcel excel = BeanUtil.copy(timer, TimerExcel.class);
            String week = timer.getWeekName();
            if (translate) {
                week = TranslateUtil.chineseToEnglish(week);
            }
            excel.setWeek(week);
            excel.setTime(timer.getTime());
            return excel;
        }).collect(Collectors.toList());
    }

    /**
     * LoRa参数列表导出
     *
     * @param lora
     * @return
     */
    public List<LoRaExcel> loRaToExcel(LoraVO lora) {
        LoRaExcel excel = BeanUtil.copy(lora, LoRaExcel.class);
        List<LoRaExcel> list = new ArrayList<>();
        list.add(excel);
        return list;
    }

    /**
     * 电池参数列表导出
     *
     * @param batteryVO
     * @return
     */
    public List<BatteryParameterExcel> batteryToExcel(BatteryVO batteryVO) {
        BatteryParameterExcel excel = BeanUtil.copy(batteryVO, BatteryParameterExcel.class);
        List<BatteryParameterExcel> list = new ArrayList<>();
        list.add(excel);
        return list;
    }

    /**
     * 风速阈值列表导出
     *
     * @param wind
     * @return
     */
    public List<WindExcel> windToExcel(Wind wind) {
        WindExcel excel = BeanUtil.copy(wind, WindExcel.class);
        List<WindExcel> list = new ArrayList<>();
        list.add(excel);
        return list;
    }

    /**
     * 停位设置列表导出
     *
     * @param stopBit
     * @param translate
     * @return
     */
    public List<StopBitExcel> stopBitToExcel(String stopBit, boolean translate) {
        StopBitExcel excel = new StopBitExcel();
        if (translate) {
            stopBit = TranslateUtil.chineseToEnglish(stopBit);
        }
        excel.setValue(stopBit);
        List<StopBitExcel> list = new ArrayList<>();
        list.add(excel);
        return list;
    }

    /**
     * 电池参数列表导出
     *
     * @param batteryVO
     * @param translate
     * @return
     */
    public List<OperationSetParamExcel> operationSetParamToExcel(RobotParamVO robotParamVO, boolean translate) {
        OperationSetParamExcel excel = new OperationSetParamExcel();
        String lockStatus = robotParamVO.getLockStatus();
        String daytimePreventClean = robotParamVO.getDaytimePreventClean();
        if (translate) {
            lockStatus = TranslateUtil.chineseToEnglish(lockStatus);
            daytimePreventClean = TranslateUtil.chineseToEnglish(daytimePreventClean);
        }
        excel.setLock(lockStatus);
        excel.setDaytimePreventClean(daytimePreventClean);
        List<OperationSetParamExcel> list = new ArrayList<>();
        list.add(excel);
        return list;
    }

    /**
     * 主板温度参数导出
     *
     * @param mainBoardTemperature
     * @return
     */
    public List<MainBoardTemperatureExcel> mainBoardTemperatureToExcel(MainBoardTemperature mainBoardTemperature) {
        MainBoardTemperatureExcel excel = new MainBoardTemperatureExcel();
        if (Objects.nonNull(mainBoardTemperature)) {
            excel.setProtectionTemperature(Func.toStr(mainBoardTemperature.getProtectionTemperature()));
            excel.setRecoveryTemperature(Func.toStr(mainBoardTemperature.getRecoveryTemperature()));
        }
        List<MainBoardTemperatureExcel> list = new ArrayList<>();
        list.add(excel);
        return list;
    }

}

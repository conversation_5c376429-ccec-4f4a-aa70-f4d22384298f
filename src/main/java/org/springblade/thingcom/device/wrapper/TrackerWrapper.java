package org.springblade.thingcom.device.wrapper;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.thingcom.device.dto.TrackerDTO;
import org.springblade.thingcom.device.entity.*;
import org.springblade.thingcom.device.enums.TrackerStatusEnum;
import org.springblade.thingcom.device.excel.TrackerExcel;
import org.springblade.thingcom.device.excel.TrackerStatusExcel;
import org.springblade.thingcom.device.service.TrackerRegionalService;
import org.springblade.thingcom.device.vo.TrackerVO;
import org.springblade.thingcom.translate.utils.TranslateUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 跟踪器包装类
 *
 * <AUTHOR>
 * @since 2024-01-03 15:19:54
 */
public class TrackerWrapper extends BaseEntityWrapper<Tracker, TrackerVO> {

    private static final TrackerRegionalService trackerRegionalService;

    static {
        trackerRegionalService = SpringUtil.getBean(TrackerRegionalService.class);
    }

    /**
     * 机器人实时数据集
     * key: 跟踪器id
     */
    private final Map<Long, Robot> robotMap;

    /**
     * 机器人区域实时数据集
     * key: 机器人区域id
     */
    private final Map<Long, RobotRegional> robotRegionalMap;

    /**
     * 跟踪器区域实时数据集
     * key: 跟踪器区域id
     */
    private final Map<Long, TrackerRegional> trackerRegionalMap;

    /**
     * 跟踪器型号实时数据集
     * key: 跟踪器型号id
     */
    private final Map<Long, TrackerModel> trackerModelMap;

    public TrackerWrapper() {
        this.robotMap = new HashMap<>();
        this.robotRegionalMap = new HashMap<>();
        this.trackerRegionalMap = new HashMap<>();
        this.trackerModelMap = new HashMap<>();
    }

    public TrackerWrapper(Map<Long, Robot> robotMap,
                          Map<Long, RobotRegional> robotRegionalMap,
                          Map<Long, TrackerRegional> trackerRegionalMap,
                          Map<Long, TrackerModel> trackerModelMap) {
        this.robotMap = robotMap;
        this.robotRegionalMap = robotRegionalMap;
        this.trackerRegionalMap = trackerRegionalMap;
        this.trackerModelMap = trackerModelMap;
    }

    public static TrackerWrapper build() {
        return new TrackerWrapper();
    }

    public static TrackerWrapper build(Map<Long, Robot> robotMap,
                                       Map<Long, RobotRegional> robotRegionalMap,
                                       Map<Long, TrackerRegional> trackerRegionalMap,
                                       Map<Long, TrackerModel> trackerModelMap) {
        return new TrackerWrapper(robotMap, robotRegionalMap, trackerRegionalMap, trackerModelMap);
    }

    public static TrackerWrapper build(Map<Long, TrackerRegional> trackerRegionalMap, Map<Long, TrackerModel> trackerModelMap) {
        return new TrackerWrapper(new HashMap<>(), new HashMap<>(), trackerRegionalMap, trackerModelMap);
    }

    /**
     * 返回视图层所需的字段
     *
     * @param entity
     * @return TrackerVO
     */
    @Override
    public TrackerVO entityVO(Tracker entity) {
        if (Objects.isNull(entity)) {
            return null;
        }
        TrackerVO vo = BeanUtil.copyProperties(entity, TrackerVO.class);
        // 状态
        vo.setStatusName(TrackerStatusEnum.translationValue(vo.getStatus()));
        // 机器人
        Robot robot = robotMap.get(vo.getId());
        if (Objects.nonNull(robot)) {
            vo.setRobotName(robot.getName());
            // 获取机器人分区
            RobotRegional robotRegional = robotRegionalMap.get(robot.getRobotRegionalId());
            if (Objects.nonNull(robotRegional)) {
                vo.setRobotRegionalNumber(robotRegional.getRegionalNumber());
            }
        }
        // 跟踪器分区
        if (Objects.nonNull(vo.getRegionalId())) {
            TrackerRegional trackerRegional = trackerRegionalMap.get(vo.getRegionalId());
            if (Objects.nonNull(trackerRegional)) {
                vo.setTrackerRegionalName(trackerRegional.getName());
            }
        }
        // 跟踪器型号
        if (Objects.nonNull(vo.getTrackerModelId())) {
            TrackerModel trackerModel = trackerModelMap.get(vo.getTrackerModelId());
            if (Objects.nonNull(trackerModel)) {
                vo.setTrackerModelName(trackerModel.getModelName());
            }
        }
        return vo;
    }

    /**
     * 返回视图层所需的字段(设备地图)
     *
     * @param entity
     * @return TrackerVO
     */
    public TrackerVO entityVOMap(Tracker entity) {
        if (Objects.isNull(entity)) {
            return null;
        }
        TrackerVO vo = BeanUtil.copyProperties(entity, TrackerVO.class);
        // 状态
        vo.setStatusName(TrackerStatusEnum.translationValue(vo.getStatus()));
        // 跟踪器分区
        if (Objects.nonNull(vo.getRegionalId())) {
            TrackerRegional trackerRegional = trackerRegionalService.getById(vo.getRegionalId());
            if (Objects.nonNull(trackerRegional)) {
                vo.setTrackerRegionalName(trackerRegional.getName());
            }
        }
        // 跟踪器型号
        if (Objects.nonNull(vo.getTrackerModelId())) {
            TrackerModel trackerModel = trackerModelMap.get(vo.getTrackerModelId());
            if (Objects.nonNull(trackerModel)) {
                vo.setTrackerModelName(trackerModel.getModelName());
            }
        }
        return vo;
    }

    /**
     * 数据列表转excel
     *
     * @param list
     * @return
     */
    public List<TrackerExcel> listToExcel(List<Tracker> list, Boolean translate) {
        return list.stream().map(m -> {
            TrackerExcel excel = BeanUtil.copyProperties(m, TrackerExcel.class);
            // 跟踪器分区
            if (Objects.nonNull(m.getRegionalId())) {
                TrackerRegional trackerRegional = trackerRegionalMap.get(m.getRegionalId());
                if (Objects.nonNull(trackerRegional)) {
                    excel.setTrackerRegionalName(trackerRegional.getName());
                }
            }
            // 跟踪器名称
            excel.setTrackerName(m.getName());
            // 状态
            if (Objects.nonNull(m.getStatus())) {
                // translate为真 常量名翻译
                if (translate) {
                    excel.setStatus(TranslateUtil.chineseToEnglish
                            (TrackerStatusEnum.translationValue(m.getStatus())));
                } else {
                    excel.setStatus(TrackerStatusEnum.translationValue(m.getStatus()));
                }
            }
            return excel;
        }).collect(Collectors.toList());
    }

    /**
     * 导出列表
     *
     * @param list
     * @return
     */
    public List<TrackerStatusExcel> statusListToExcel(List<Tracker> list, Boolean translate) {
        return list.stream().map(entity -> {
            TrackerStatusExcel excel = BeanUtil.copy(entity, TrackerStatusExcel.class);
            if (Objects.nonNull(entity.getStatus())) {
                // translate为真 常量名翻译
                if (translate) {
                    excel.setStatus(TranslateUtil.chineseToEnglish
                            (TrackerStatusEnum.translationValue(entity.getStatus())));
                } else {
                    excel.setStatus(TrackerStatusEnum.translationValue(entity.getStatus()));
                }
            }
            // 机器人
            Robot robot = robotMap.get(entity.getId());
            if (Objects.nonNull(robot)) {
                excel.setRobotName(robot.getName());
                // 获取机器人分区
                RobotRegional robotRegional = robotRegionalMap.get(robot.getRobotRegionalId());
                if (Objects.nonNull(robotRegional)) {
                    excel.setRobotRegionalNumber(robotRegional.getRegionalNumber());
                }
            }
            // 跟踪器分区
            if (Objects.nonNull(entity.getRegionalId())) {
                TrackerRegional trackerRegional = trackerRegionalMap.get(entity.getRegionalId());
                if (Objects.nonNull(trackerRegional)) {
                    excel.setTrackerRegionalName(trackerRegional.getName());
                }
            }
            // 跟踪器型号
            if (Objects.nonNull(entity.getTrackerModelId())) {
                TrackerModel trackerModel = trackerModelMap.get(entity.getTrackerModelId());
                if (Objects.nonNull(trackerModel)) {
                    excel.setTrackerModelName(trackerModel.getModelName());
                }
            }
            return excel;
        }).collect(Collectors.toList());
    }

    /**
     * 设置请求参数
     *
     * @param dto
     * @return LambdaQueryWrapper<Tracker>
     */
    public LambdaQueryWrapper<Tracker> setQueryParam(TrackerDTO dto) {
        LambdaQueryWrapper<Tracker> lqw = new LambdaQueryWrapper<>();
        // 名称
        lqw.like(StrUtil.isNotBlank(dto.getName()), Tracker::getName, dto.getName());
        // id
        lqw.eq(Objects.nonNull(dto.getId()), Tracker::getId, dto.getId());
        // 状态
        lqw.eq(Objects.nonNull(dto.getStatus()), Tracker::getStatus, dto.getStatus());
        // 分区id
        lqw.eq(Objects.nonNull(dto.getRegionalId()), Tracker::getRegionalId, dto.getRegionalId());
        // 分区名称
        if (StrUtil.isNotBlank(dto.getTrackerRegionalName())) {
            List<TrackerRegional> regionalList = trackerRegionalService.list(Wrappers.<TrackerRegional>lambdaQuery()
                    .like(TrackerRegional::getName, dto.getTrackerRegionalName()));
            if (CollUtil.isEmpty(regionalList)) {
                lqw.eq(Tracker::getRegionalId, -1);
                return lqw;
            } else {
                lqw.in(Tracker::getRegionalId, regionalList.stream().map(TrackerRegional::getId).toArray());
            }
        }
        // 起始日期
        if (Objects.nonNull(dto.getBeginDate()) && Objects.nonNull(dto.getEndDate())) {
            lqw.between(Tracker::getUpdateTime, dto.getBeginDate(), dto.getEndDate());
        }
        // 排序
        lqw.orderByAsc(Tracker::getRegionalId);
        return lqw;
    }
}
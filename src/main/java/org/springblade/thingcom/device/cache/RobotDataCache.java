package org.springblade.thingcom.device.cache;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import org.springblade.common.cache.CacheNames;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.thingcom.communication.constant.MonitorAliasConstant;
import org.springblade.thingcom.communication.entity.ControlBoard;
import org.springblade.thingcom.communication.entity.MainBoardTemperature;
import org.springblade.thingcom.communication.entity.Wind;
import org.springblade.thingcom.communication.enums.ControlSwitchEnum;
import org.springblade.thingcom.communication.enums.StopBitEnum;
import org.springblade.thingcom.communication.enums.WeekEnum;
import org.springblade.thingcom.communication.vo.BatteryVO;
import org.springblade.thingcom.communication.vo.LoraVO;
import org.springblade.thingcom.communication.vo.TimerVO;
import org.springblade.thingcom.data.constant.RobotDataConstant;
import org.springblade.thingcom.data.utils.RobotDataUtil;
import org.springblade.thingcom.device.enums.LockOperationEnum;
import org.springblade.thingcom.device.vo.RobotParamVO;
import org.springblade.thingcom.statistic.entity.RobotBattery;
import org.springblade.thingcom.statistic.entity.RobotMotor;
import org.springblade.thingcom.statistic.service.RobotBatteryService;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @ClassName RobotDataCache
 * @Description: 机器人数据缓存
 * @Author: zgq
 * @Date: 2024/1/8 17:55
 * @Version: 1.0
 **/
public class RobotDataCache {

    public static final BladeRedis bladeRedis;
    public static final RobotBatteryService robotBatteryService;

    static {
        bladeRedis = SpringUtil.getBean(BladeRedis.class);
        robotBatteryService = SpringUtil.getBean(RobotBatteryService.class);
    }

    /**
     * 根据机器人 eui 获取电池数据
     *
     * @param robotEui
     * @return
     */
    public static RobotBattery getBatteryDataByRobotEui(String robotEui) {
        if (StrUtil.isBlank(robotEui)) {
            return null;
        }
        // 获取缓存数据
        String cacheKey = CacheNames.ROBOT_REALDATA + robotEui;
        Map mapData = bladeRedis.hGetAll(cacheKey);
        if (Objects.isNull(mapData)) {
            return null;
        }
        return RobotDataUtil.getBatteryBaseData(mapData);
    }

    /**
     * 根据机器人 eui 获取电机数据
     *
     * @param robotEui
     * @return
     */
    public static RobotMotor getMotorDataByRobotEui(String robotEui) {
        if (StrUtil.isBlank(robotEui)) {
            return null;
        }
        // 获取缓存数据
        String cacheKey = CacheNames.ROBOT_REALDATA + robotEui;
        Map mapData = bladeRedis.hGetAll(cacheKey);
        if (Objects.isNull(mapData)) {
            return null;
        }
        // 电机
        RobotMotor motor = new RobotMotor();
        // 主电机电流
        Object masterMotorElectricityObj = mapData.get(MonitorAliasConstant.MASTER_MOTOR_ELECTRICITY);
        motor.setMasterElectricity(Objects.isNull(masterMotorElectricityObj)
                ? null : NumberUtil.toBigDecimal(masterMotorElectricityObj.toString()));
        // 从电机电流
        Object slaveMotorElectricityObj = mapData.get(MonitorAliasConstant.SLAVE_MOTOR_ELECTRICITY);
        motor.setSlaveElectricity(Objects.isNull(slaveMotorElectricityObj)
                ? null : NumberUtil.toBigDecimal(slaveMotorElectricityObj.toString()));
        // 从机数据缓存 key
        String slave = RobotDataConstant.SLAVE;
        // 电机速率列表（行走、毛刷、防风）
        Map motorRunRateMap = (Map) mapData.get(MonitorAliasConstant.MOTOR_RUN_RATE);
        if (Objects.nonNull(motorRunRateMap) && Objects.nonNull(motorRunRateMap.get(slave))) {
            List<Object> motorRunRateList = (ArrayList<Object>) motorRunRateMap.get(slave);
            if (CollUtil.isNotEmpty(motorRunRateList)) {
                // 行走电机运行速率
                Object runRateObj = motorRunRateList.get(0);
                motor.setRunRate(Objects.isNull(runRateObj) ? null : runRateObj.toString());
            }
        }
        return motor;
    }

    /**
     * 根据机器人 eui 获取参数数据
     *
     * @param robotEui
     * @return
     */
    public static RobotParamVO getParamDataByRobotEui(String robotEui) {
        if (StrUtil.isBlank(robotEui)) {
            return null;
        }
        // 获取缓存数据
        String cacheKey = CacheNames.ROBOT_REALDATA + robotEui;
        Map mapData = bladeRedis.hGetAll(cacheKey);
        if (Objects.isNull(mapData)) {
            return null;
        }
        // 机器人参数vo
        RobotParamVO vo = new RobotParamVO();
        // 软件版本
        Object softwareVersionObj = mapData.get(MonitorAliasConstant.SOFTWARE_VERSION);
        String softwareVersion = RobotDataUtil.getSoftwareVersion(softwareVersionObj);
        vo.setSoftwareVersion(softwareVersion);
        // 光伏板输出电压、电流
        String pvPanelOutputVoltage = mapData.getOrDefault(MonitorAliasConstant.PV_PANEL_OUTPUT_VOLTAGE, "").toString();
        String pvPanelOutputElectricity = mapData.getOrDefault(MonitorAliasConstant.PV_PANEL_OUTPUT_ELECTRICITY, "").toString();
        vo.setPvPanelOutputVoltage(pvPanelOutputVoltage);
        vo.setPvPanelOutputElectricity(pvPanelOutputElectricity);
        // 时间
        String time = RobotDataUtil.getTimeData(mapData);
        vo.setTime(time);
        // 星期
        String week = mapData.getOrDefault(MonitorAliasConstant.ROBOT_TIME_WEEK, "").toString();
        if (StrUtil.isNotBlank(week)) {
            vo.setWeek(WeekEnum.translationValue(Func.toInt(week)));
        }
        // lora
        LoraVO lora = RobotDataUtil.getLoraVO(mapData);
        vo.setLora(lora);
        // 电池
        BatteryVO battery = RobotDataUtil.getBatteryVO(mapData);
        vo.setBattery(battery);
        // 控制板
        ControlBoard controlBoard = RobotDataUtil.getControlBoard(mapData, robotEui, false);
        vo.setControlBoard(controlBoard);
        // 风
        Wind wind = RobotDataUtil.getWind(mapData);
        vo.setWind(wind);
        // 停机位
        Integer stopBit = Func.toInt(mapData.getOrDefault(MonitorAliasConstant.STOP_BIT_VALUE, -1));
        vo.setStopBit(StopBitEnum.translationValue(stopBit));
        // 定时器列表
        List<TimerVO> timerList = RobotDataUtil.getTimerList(mapData);
        vo.setTimerList(timerList);
        // 主板温度
        MainBoardTemperature mainBoardTemperature = RobotDataUtil.getMainBoardTemperature(mapData);
        vo.setMainBoardTemperature(mainBoardTemperature);
        // 取 FA 状态，2进制字符串
        Object faObj = mapData.get(MonitorAliasConstant.FA);
        if (Objects.nonNull(faObj)) {
            // 开（触发）
            String open = ControlSwitchEnum.OPEN.getValue().toString();
            // bit31 ~ bit0 反转 bit0 ~ bit31
            String binaryStr = StrUtil.reverse(faObj.toString());
            // bit0 ~ bit31
            String[] binaryArray = StrUtil.split(binaryStr, 1);
            // 机器人锁定状态
            if (binaryArray.length > 0) {
                String lockStatus = open.equals(binaryArray[0]) ? LockOperationEnum.LOCK.getZh()
                        : LockOperationEnum.UNLOCK.getZh();
                vo.setLockStatus(lockStatus);
            }
            // 白天防误扫触发
            if (binaryArray.length > 18) {
                vo.setDaytimePreventClean(ControlSwitchEnum.translationValue(Func.toInt(binaryArray[18], 0)));
            }
        }
        return vo;
    }

}

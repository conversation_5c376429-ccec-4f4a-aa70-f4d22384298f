<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.thingcom.historyWarning.mapper.DcsAlarmLogMapper">

    <resultMap type="org.springblade.thingcom.historyWarning.entity.DcsAlarmLog" id="DcsAlarmLogMap">
        <result column="id" property="id"/>
        <result column="robot_count" property="robotCount"/>
        <result column="normal_count" property="normalCount"/>
        <result column="alarm_count" property="alarmCount"/>
        <result column="work_count" property="workCount"/>
        <result column="time" property="time"/>
        <result column="creator_id" property="creatorId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_id" property="updateId"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="deleted"/>
    </resultMap>
    
    <!--新增所有列-->
<!--    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into tl_dcs_alarm_log(robot_count, normal_count, alarm_count, work_count, time, creator_id, create_time, update_id, update_time, is_deleted)
        values (#{robotCount}, #{normalCount}, #{alarmCount}, #{workCount}, #{time}, #{creatorId}, #{createTime}, #{updateId}, #{updateTime}, #{isDeleted})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into tl_dcs_alarm_log(robot_count, normal_count, alarm_count, work_count, time, creator_id, create_time, update_id, update_time, is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.robotCount}, #{entity.normalCount}, #{entity.alarmCount}, #{entity.workCount}, #{entity.time}, #{entity.creatorId}, #{entity.createTime}, #{entity.updateId}, #{entity.updateTime}, #{entity.isDeleted})
        </foreach>
    </insert>-->

</mapper>

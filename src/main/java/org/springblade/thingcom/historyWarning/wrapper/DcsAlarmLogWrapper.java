package org.springblade.thingcom.historyWarning.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.thingcom.historyWarning.entity.DcsAlarmLog;
import org.springblade.thingcom.historyWarning.excel.DcsAlarmLogExcel;
import org.springblade.thingcom.historyWarning.vo.DcsAlarmLogVO;

import java.util.List;
import java.util.stream.Collectors;

/**
 * DCS历史告警日志包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-03-20 14:47:40
 */
public class DcsAlarmLogWrapper extends BaseEntityWrapper<DcsAlarmLog, DcsAlarmLogVO> {

//  private static final DcsAlarmLogService dcsAlarmLogService;
//
//	static {
//		dcsAlarmLogService = SpringUtil.getBean(DcsAlarmLogService.class);
//	}

    public static DcsAlarmLogWrapper build() {
        return new DcsAlarmLogWrapper();
    }

    @Override
    public DcsAlarmLogVO entityVO(DcsAlarmLog entity) {
        if (null == entity) {
            return null;
        }
        DcsAlarmLogVO vo = BeanUtil.copy(entity, DcsAlarmLogVO.class);
        return vo;
    }

    /**
     * 导出列表
     *
     * @param list
     * @return
     */
    public List<DcsAlarmLogExcel> listToExcel(List<DcsAlarmLog> list) {
        return list.stream()
                .map(entity -> {
                    DcsAlarmLogExcel excel = BeanUtil.copyProperties(entity, DcsAlarmLogExcel.class);
                    return excel;
                }).collect(Collectors.toList());
    }
}

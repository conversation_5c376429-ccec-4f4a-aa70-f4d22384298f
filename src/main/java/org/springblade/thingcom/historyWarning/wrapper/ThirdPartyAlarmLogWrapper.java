package org.springblade.thingcom.historyWarning.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.thingcom.historyWarning.entity.ThirdPartyAlarmLog;
import org.springblade.thingcom.historyWarning.excel.ThirdPartyAlarmLogExcel;
import org.springblade.thingcom.historyWarning.utils.AlarmDataUtil;
import org.springblade.thingcom.historyWarning.vo.ThirdPartyAlarmLogVo;
import org.springblade.thingcom.translate.utils.TranslateUtil;
import org.springblade.thingcom.warning.enums.AlarmTypeEnum;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 第三方系统历史告警包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-07-26 10:11:39
 */
public class ThirdPartyAlarmLogWrapper extends BaseEntityWrapper<ThirdPartyAlarmLog, ThirdPartyAlarmLogVo> {

    public static ThirdPartyAlarmLogWrapper build() {
        return new ThirdPartyAlarmLogWrapper();
    }

    @Override
    public ThirdPartyAlarmLogVo entityVO(ThirdPartyAlarmLog entity) {
        if (null == entity) {
            return null;
        }
        ThirdPartyAlarmLogVo vo = BeanUtil.copy(entity, ThirdPartyAlarmLogVo.class);
        vo.setAlarmTypeName(AlarmTypeEnum.translationValue(entity.getAlarmType()));
        String result = AlarmDataUtil.parseDuration(vo.getDuration(), vo.getCreateTime());
        vo.setDurationVo(result);
        return vo;
    }

    public List<ThirdPartyAlarmLogExcel> listToExcel(List<ThirdPartyAlarmLog> list, Boolean translate) {
        return list.stream()
                .map(entity -> {
                    ThirdPartyAlarmLogExcel excel = BeanUtil.copy(entity, ThirdPartyAlarmLogExcel.class);
                    // translate为真 常量名翻译
                    if (translate) {
                        excel.setAlarmType(TranslateUtil.chineseToEnglish
                                (AlarmTypeEnum.translationValue(entity.getAlarmType())));
                    } else {
                        excel.setAlarmType(AlarmTypeEnum.translationValue(entity.getAlarmType()));
                    }
                    // 告警持续时间
                    String result = AlarmDataUtil.parseDuration(entity.getDuration(), entity.getCreateTime());
                    excel.setDuration(result);
                    return excel;
                })
                .collect(Collectors.toList());
    }
}

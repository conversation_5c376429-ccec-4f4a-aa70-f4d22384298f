package org.springblade.thingcom.historyWarning.wrapper;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.Func;
import org.springblade.thingcom.device.enums.AlarmStatusEnum;
import org.springblade.thingcom.historyWarning.dto.RobotAlarmLogDTO;
import org.springblade.thingcom.historyWarning.entity.RobotAlarmLog;
import org.springblade.thingcom.historyWarning.excel.RobotAlarmLogExcel;
import org.springblade.thingcom.historyWarning.utils.AlarmDataUtil;
import org.springblade.thingcom.historyWarning.vo.RobotAlarmLogVO;
import org.springblade.thingcom.translate.utils.TranslateUtil;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 机器人历史告警记录包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-01-24 14:59:12
 */
public class RobotAlarmLogWrapper extends BaseEntityWrapper<RobotAlarmLog, RobotAlarmLogVO> {

    public static RobotAlarmLogWrapper build() {
        return new RobotAlarmLogWrapper();
    }

    @Override
    public RobotAlarmLogVO entityVO(RobotAlarmLog entity) {
        if (null == entity) {
            return null;
        }
        RobotAlarmLogVO vo = BeanUtil.toBean(entity, RobotAlarmLogVO.class);
        // 告警类型
        if (Objects.nonNull(vo.getAlarmType())) {
            vo.setAlarmTypeVo(AlarmStatusEnum.translationValue(vo.getAlarmType()));
        }
        // 告警持续时间
        String result = AlarmDataUtil.parseDuration(entity.getDuration(), entity.getTime());
        vo.setDurationVo(result);
        return vo;
    }

    public RobotAlarmLogVO entityVoExt(Map<String, Object> map) {
        if (null == map) {
            return null;
        }
        RobotAlarmLogVO vo = BeanUtil.toBean(map, RobotAlarmLogVO.class);
        // 告警类型
        if (Objects.nonNull(vo.getAlarmType())) {
            vo.setAlarmTypeVo(AlarmStatusEnum.translationValue(vo.getAlarmType()));
        }
        // 告警持续时间
        String result = AlarmDataUtil.parseDuration(vo.getDuration(), vo.getTime());
        vo.setDurationVo(result);
        return vo;
    }

    public List<RobotAlarmLogVO> listVoExt(List<Map<String, Object>> list) {
        return list.stream().map(this::entityVoExt).collect(Collectors.toList());
    }

    public IPage<RobotAlarmLogVO> pageVoExt(IPage<Map<String, Object>> pages) {
        List<RobotAlarmLogVO> records = this.listVoExt(pages.getRecords());
        IPage<RobotAlarmLogVO> pageVo = new Page(pages.getCurrent(), pages.getSize(), pages.getTotal());
        pageVo.setRecords(records);
        return pageVo;
    }

    /**
     * 设置sql查询
     *
     * @param dto
     * @return
     */
    public LambdaQueryWrapper<RobotAlarmLog> setQueryParam(RobotAlarmLogDTO dto) {
        LambdaQueryWrapper<RobotAlarmLog> lqw = new LambdaQueryWrapper<>();
        // 告警类型
        lqw.eq(Objects.nonNull(dto.getAlarmType()), RobotAlarmLog::getAlarmType, dto.getAlarmType());
        // 机器人区域
        lqw.eq(Objects.nonNull(dto.getRobotRegionalId()), RobotAlarmLog::getRobotRegionalId, dto.getRobotRegionalId());
        // 机器人名称
        lqw.eq(Objects.nonNull(dto.getRobotId()), RobotAlarmLog::getRobotId, dto.getRobotId());
        // 时间查询
        if (Objects.nonNull(dto.getBeginDate()) && Objects.nonNull(dto.getEndDate())) {
            lqw.between(RobotAlarmLog::getTime, dto.getBeginDate(), dto.getEndDate());
        }
        // 时间倒序
        lqw.orderByDesc(RobotAlarmLog::getTime);
        return lqw;
    }

    /**
     * 导出列表
     *
     * @param mapList
     * @return
     */
    public List<RobotAlarmLogExcel> listToExcel(List<Map<String, Object>> mapList, Boolean translate) {
        return mapList.stream()
                .map(map -> {
                    RobotAlarmLogExcel excel = BeanUtil.toBean(map, RobotAlarmLogExcel.class);
                    // 告警类型
                    if (Objects.nonNull(excel.getAlarmType())) {
                        // translate为真 翻译
                        if (translate) {
                            excel.setAlarmType(TranslateUtil.chineseToEnglish
                                    (AlarmStatusEnum.translationValue(Func.toInt(excel.getAlarmType()))));
                        } else {
                            excel.setAlarmType(AlarmStatusEnum.translationValue(Func.toInt(excel.getAlarmType())));
                        }
                    }
                    // 告警内容翻译
                    if (translate && StrUtil.isNotBlank(excel.getAlarmContent())) {
                        excel.setAlarmContent(TranslateUtil.chineseToEnglish(excel.getAlarmContent()));
                    }
                    // 解决建议翻译
                    if (translate && StrUtil.isNotBlank(excel.getSolution())) {
                        excel.setSolution(TranslateUtil.chineseToEnglish(excel.getSolution()));
                    }
                    // 告警持续时间
                    String result = AlarmDataUtil.parseDuration(Func.toLong(excel.getDuration()), excel.getTime());
                    excel.setDuration(result);
                    return excel;
                }).collect(Collectors.toList());
    }
}

package org.springblade.thingcom.historyWarning.wrapper;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.thingcom.device.entity.Gateway;
import org.springblade.thingcom.device.service.GatewayService;
import org.springblade.thingcom.historyWarning.dto.GatewayAlarmLogDTO;
import org.springblade.thingcom.historyWarning.entity.GatewayAlarmLog;
import org.springblade.thingcom.historyWarning.excel.GatewayAlarmLogExcel;
import org.springblade.thingcom.historyWarning.utils.AlarmDataUtil;
import org.springblade.thingcom.historyWarning.vo.GatewayAlarmLogVO;
import org.springblade.thingcom.translate.utils.TranslateUtil;
import org.springblade.thingcom.warning.enums.CommunicationStatusEnum;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 网关历史告警记录包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-01-24 16:05:24
 */
public class GatewayAlarmLogWrapper extends BaseEntityWrapper<GatewayAlarmLog, GatewayAlarmLogVO> {

    private static final GatewayService GATEWAY_SERVICE;

    static {
        GATEWAY_SERVICE = SpringUtil.getBean(GatewayService.class);
    }

    public static GatewayAlarmLogWrapper build() {
        return new GatewayAlarmLogWrapper();
    }

    @Override
    public GatewayAlarmLogVO entityVO(GatewayAlarmLog entity) {
        if (null == entity) {
            return null;
        }
        GatewayAlarmLogVO vo = BeanUtil.copyProperties(entity, GatewayAlarmLogVO.class);
        // 转换告警类型
        if (Objects.nonNull(vo.getAlarmType())) {
            vo.setAlarmTypeVo(CommunicationStatusEnum.translationValue(vo.getAlarmType()));
        }
        if (Objects.nonNull(vo.getGatewayId())) {
            Gateway gateway = GATEWAY_SERVICE.getById(vo.getGatewayId());
            if (Objects.nonNull(gateway)) {
                vo.setGatewayNumber(gateway.getGatewayNumber());
            }
        }
        // 告警持续时间
        String result = AlarmDataUtil.parseDuration(entity.getDuration(), entity.getTime());
        vo.setDurationVo(result);
        return vo;
    }

    /**
     * 设置sql查询条件
     *
     * @param dto
     * @return
     */
    public LambdaQueryWrapper<GatewayAlarmLog> setQueryParam(GatewayAlarmLogDTO dto) {
        LambdaQueryWrapper<GatewayAlarmLog> lqw = new LambdaQueryWrapper<>();
        // 告警类型
        lqw.eq(ObjectUtil.isNotEmpty(dto.getAlarmType()), GatewayAlarmLog::getAlarmType, dto.getAlarmType());
        // 告警内容
        lqw.like(Func.isNotBlank(dto.getAlarmContent()), GatewayAlarmLog::getAlarmContent, dto.getAlarmContent());
        // 网关查询
        lqw.eq(ObjectUtil.isNotEmpty(dto.getGatewayId()), GatewayAlarmLog::getGatewayId, dto.getGatewayId());
        // 查询时间
        if (Objects.nonNull(dto.getBeginDate()) && Objects.nonNull(dto.getEndDate())) {
            lqw.between(GatewayAlarmLog::getTime, dto.getBeginDate(), dto.getEndDate());
        }
        // 时间倒序
        lqw.orderByDesc(GatewayAlarmLog::getTime);
        return lqw;
    }

    /**
     * 导出列表
     *
     * @param entityList
     * @return
     */
    public List<GatewayAlarmLogExcel> listToExcel(List<GatewayAlarmLog> entityList, Boolean translate) {
        return entityList.stream()
                .map(entity -> {
                    GatewayAlarmLogExcel excel = BeanUtil.copyProperties(entity, GatewayAlarmLogExcel.class);
                    // 转换告警类型
                    if (Objects.nonNull(entity.getAlarmType())) {
                        // translate为真 翻译
                        if (translate) {
                            excel.setAlarmType(TranslateUtil.chineseToEnglish(CommunicationStatusEnum.translationValue(entity.getAlarmType())));
                        } else {
                            excel.setAlarmType(CommunicationStatusEnum.translationValue(entity.getAlarmType()));
                        }
                    }
                    if (Objects.nonNull(entity.getGatewayId())) {
                        Gateway gateway = GATEWAY_SERVICE.getById(entity.getGatewayId());
                        if (Objects.nonNull(gateway)) {
                            excel.setGatewayNumber(gateway.getGatewayNumber());
                        }
                    }
                    // 告警内容翻译
                    if (translate && StrUtil.isNotBlank(entity.getAlarmContent())) {
                        excel.setAlarmContent(TranslateUtil.chineseToEnglish(entity.getAlarmContent()));
                    }
                    // 告警持续时间
                    String result = AlarmDataUtil.parseDuration(entity.getDuration(), entity.getTime());
                    excel.setDuration(result);
                    return excel;
                }).collect(Collectors.toList());
    }
}
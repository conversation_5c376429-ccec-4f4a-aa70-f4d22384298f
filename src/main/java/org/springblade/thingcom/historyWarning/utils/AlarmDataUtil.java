package org.springblade.thingcom.historyWarning.utils;

import org.springblade.common.utils.CommonUtil;
import org.springblade.core.tool.utils.StringUtil;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * @ClassName AlarmDataUtil
 * @Description 告警数据工具类
 * <AUTHOR>
 * @Date 2024/7/27 14:16
 * @Version 1.0
 **/
public class AlarmDataUtil {

    /**
     * 解析告警持续时间
     *
     * @param duration   持续时间
     * @param createTime 创建时间
     * @return String
     **/
    public static String parseDuration(Long duration, LocalDateTime createTime) {
        // 未解除的告警，持续时间计算（截止到当前时间）
        if (Objects.isNull(duration) || duration == 0) {
            Long createTimestamp = CommonUtil.localDateTimeToTimestamp(createTime);
            duration = (System.currentTimeMillis() - createTimestamp) / 1000L;
        }
        long day = duration / 86400;
        // 剩余的秒数用于小时、分钟、秒的计算
        long remainingSeconds = duration % 86400;
        long h = remainingSeconds / 3600;
        long min = (remainingSeconds % 3600) / 60;
        long s = remainingSeconds % 3600 % 60;
        String result = "";
        if (day != 0) {
            result = day + "d";
        }
        if (h != 0) {
            result = result + h + "h";
        }
        if (min != 0) {
            result = result + min + "min";
        }
        if (s != 0) {
            result = result + s + "s";
        }
        return StringUtil.isBlank(result) ? "0s" : result;
    }
}

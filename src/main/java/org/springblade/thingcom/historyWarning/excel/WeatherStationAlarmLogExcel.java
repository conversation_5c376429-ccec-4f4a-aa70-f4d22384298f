package org.springblade.thingcom.historyWarning.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 气象站历史告警日志excel类
 *
 * <AUTHOR>
 * @since 2024-03-20 11:50:01
 */
@Data
@ColumnWidth(16)
@HeadRowHeight(52)
@ContentRowHeight(18)
public class WeatherStationAlarmLogExcel implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 气象站编号
     */
    @ExcelProperty(value = "气象站编号")
    private String weatherStationNumber;

    /**
     * 告警类型
     */
    @ExcelProperty(value = "告警类型")
    private String alarmType;

    /**
     * 告警持续时间
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "告警持续时间")
    private String duration;

    /**
     * 风速
     */
    @ColumnWidth(35)
    @ExcelProperty(value = "风速(m/s)")
    private BigDecimal windSpeed;

    /**
     * 告警时间
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "告警时间")
    private LocalDateTime time;
}

package org.springblade.thingcom.historyWarning.service.impl;

import org.springblade.thingcom.core.mybatisPlus.impl.BaseServiceImpl;
import org.springblade.thingcom.historyWarning.entity.GatewayAlarmLog;
import org.springblade.thingcom.historyWarning.mapper.GatewayAlarmLogMapper;
import org.springblade.thingcom.historyWarning.service.GatewayAlarmLogService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 网关历史告警记录服务实现类
 *
 * <AUTHOR>
 * @since 2024-01-24 16:05:24
 */
@Service
public class GatewayAlarmLogServiceImpl extends BaseServiceImpl<GatewayAlarmLogMapper, GatewayAlarmLog> implements GatewayAlarmLogService {
    
    @Override
    public String validateData(GatewayAlarmLog entity) {
        return null;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveData(GatewayAlarmLog entity) {
        // 新增网关历史告警记录
        boolean ret = save(entity);
        return ret;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateData(GatewayAlarmLog entity) {
        // 修改网关历史告警记录
        boolean ret = updateById(entity);
        return ret;
    }
}

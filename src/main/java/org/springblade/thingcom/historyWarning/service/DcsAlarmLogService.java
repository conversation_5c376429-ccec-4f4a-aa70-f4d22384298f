package org.springblade.thingcom.historyWarning.service;

import org.springblade.thingcom.core.mybatisPlus.IBaseService;
import org.springblade.thingcom.historyWarning.entity.DcsAlarmLog;

/**
 * DCS历史告警日志服务接口
 *
 * <AUTHOR>
 * @since 2024-03-20 14:47:40
 */
public interface DcsAlarmLogService extends IBaseService<DcsAlarmLog> {

    /**
     * 检查数据合法性
     *
     * @param entity 实体对象
     * @return 异常消息，正常时为null
     */
    String validateData(DcsAlarmLog entity);
    
    /**
     * 新增DCS历史告警日志
     *
     * @param entity
     * @return
     */
    boolean saveData(DcsAlarmLog entity);

    /**
     * 修改DCS历史告警日志
     *
     * @param entity
     * @return
     */
    boolean updateData(DcsAlarmLog entity);
}

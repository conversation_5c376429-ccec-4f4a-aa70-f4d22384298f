package org.springblade.thingcom.historyWarning.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.springblade.common.utils.CommonUtil;
import org.springblade.thingcom.core.mybatisPlus.impl.BaseServiceImpl;
import org.springblade.thingcom.data.enums.HandleStatusEnum;
import org.springblade.thingcom.historyWarning.entity.ServerAlarmLog;
import org.springblade.thingcom.historyWarning.mapper.ServerAlarmLogMapper;
import org.springblade.thingcom.historyWarning.service.ServerAlarmLogService;
import org.springblade.thingcom.operationMaintenance.entity.Server;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 服务器历史告警日志服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-11 17:10:13
 */
@Service
@AllArgsConstructor
public class ServerAlarmLogServiceImpl extends BaseServiceImpl<ServerAlarmLogMapper, ServerAlarmLog> implements ServerAlarmLogService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveServerAlarm(Server server, List<ServerAlarmLog> alarmDataList) {
        // 查询未处理的告警
        List<ServerAlarmLog> unhandledAlarmList = list(Wrappers.<ServerAlarmLog>lambdaQuery()
                .eq(ServerAlarmLog::getServerId, server.getId())
                .eq(ServerAlarmLog::getHandleStatus, HandleStatusEnum.UNHANDLED.getValue()));
        // 查找已处理的告警（unhandledAlarmList 中有，alarmDataList 中没有）
        List<ServerAlarmLog> handledAlarmList = unhandledAlarmList.stream()
                .filter(unhandledAlarm -> alarmDataList.stream()
                        .noneMatch(alarmData -> unhandledAlarm.getServerId().equals(alarmData.getServerId())
                                && unhandledAlarm.getMonitorType().equals(alarmData.getMonitorType())
                                && unhandledAlarm.getAlarmName().equals(alarmData.getAlarmName())))
                .collect(Collectors.toList());
        // 更新状态和告警持续时间
        if (CollUtil.isNotEmpty(handledAlarmList)) {
            LocalDateTime time = CommonUtil.getZoneTime();
            for (ServerAlarmLog serverAlarmLog : handledAlarmList) {
                serverAlarmLog.setHandleStatus(HandleStatusEnum.HANDLED.getValue());
                if (Objects.nonNull(serverAlarmLog.getTime())) {
                    serverAlarmLog.setDuration(serverAlarmLog.getTime().until(time, ChronoUnit.SECONDS));
                }
            }
            updateBatchById(handledAlarmList);
        }
        // 查找新增的告警（alarmDataList 中有，unhandledAlarmList 中没有）
        List<ServerAlarmLog> newAlarmDataList = alarmDataList.stream()
                .filter(alarmData -> unhandledAlarmList.stream()
                        .noneMatch(unhandledAlarm -> alarmData.getServerId().equals(unhandledAlarm.getServerId())
                                && alarmData.getMonitorType().equals(unhandledAlarm.getMonitorType())
                                && alarmData.getAlarmName().equals(unhandledAlarm.getAlarmName())))
                .collect(Collectors.toList());
        // 批量新增告警数据
        if (CollUtil.isNotEmpty(newAlarmDataList)) {
            saveBatch(newAlarmDataList);
        }
    }

}

package org.springblade.thingcom.historyWarning.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springblade.thingcom.core.mybatisPlus.entity.BaseEntity;
import org.springblade.thingcom.operationMaintenance.enums.WeatherStationTypeEnum;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 气象站历史告警日志实体类
 *
 * <AUTHOR>
 * @since 2024-03-20 11:50:01
 */
@Data
@TableName("tl_weather_station_alarm_log")
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class WeatherStationAlarmLog extends BaseEntity<WeatherStationAlarmLog> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 气象站id
     */
    private Long weatherStationId;

    /**
     * 气象站类型，1-Thingcom；2-Arctech
     *
     * @see WeatherStationTypeEnum
     */
    private Integer weatherStationType;

    /**
     * 气象站编号
     */
    private String weatherStationNumber;

    /**
     * 位置
     */
    private String location;

    /**
     * 告警类型，详见字典表
     */
    private Integer alarmType;

    /**
     * 告警内容
     */
    private String alarmContent;

    /**
     * 处理状态，0-未处理；1-已处理
     *
     * @see org.springblade.thingcom.data.enums.HandleStatusEnum
     */
    private Integer handleStatus;

    /**
     * 解决建议
     */
    private String solution;

    /**
     * 风向
     *
     * @see org.springblade.thingcom.communication.enums.WindDirectionEnum
     */
    private String windDirection;

    /**
     * 风速，单位 m/s
     */
    private BigDecimal windSpeed;

    /**
     * 温度，单位 ℃
     */
    private BigDecimal temperature;

    /**
     * 湿度，单位 RH%
     */
    private BigDecimal humidity;

    /**
     * 数据上报时间
     */
    private LocalDateTime time;

    /**
     * 告警持续时间，单位 s
     */
    private Long duration;

}
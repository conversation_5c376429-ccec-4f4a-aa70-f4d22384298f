package org.springblade.thingcom.historyWarning.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springblade.thingcom.core.mybatisPlus.entity.BaseEntity;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 网关历史告警记录实体类
 *
 * <AUTHOR>
 * @since 2024-01-24 16:05:24
 */
@Data
@TableName("tl_gateway_alarm_log")
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class GatewayAlarmLog extends BaseEntity<GatewayAlarmLog> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
	 * 网关id
	 */
    private Long gatewayId;

    /**
	 * 网关编号
	 */
    private String gatewayNumber;

    /**
	 * 告警类型
	 */
    private Integer alarmType;

    /**
	 * 告警内容
	 */
    private String alarmContent;

    /**
	 * 解决建议
	 */
    private String solution;

    /**
	 * 数据上报时间
	 */
    private LocalDateTime time;

    /**
	 * 告警持续时间
	 */
    private Long duration;

    /**
     * 告警结束标识
     */
    private Integer completeIdentification;

}

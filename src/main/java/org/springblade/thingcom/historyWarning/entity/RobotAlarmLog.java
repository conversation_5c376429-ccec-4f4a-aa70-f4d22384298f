package org.springblade.thingcom.historyWarning.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springblade.thingcom.core.mybatisPlus.entity.BaseEntity;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 机器人历史告警记录实体类
 *
 * <AUTHOR>
 * @since 2024-01-24 14:59:12
 */
@Data
@TableName("tl_robot_alarm_log")
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class RobotAlarmLog extends BaseEntity<RobotAlarmLog> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 机器人分区id
     */
    private Long robotRegionalId;

    /**
     * 机器人分区编号
     */
    private String robotRegionalNumber;

    /**
     * 机器人id
     */
    private Long robotId;

    /**
     * 机器人名称
     */
    private String robotName;

    /**
     * 状态解析id
     */
    private Long statusAnalysisId;

    /**
     * 告警类型
     */
    private Integer alarmType;

    /**
     * 告警内容
     */
    private String alarmContent;

    /**
     * 处理状态，0-未处理；1-已处理
     *
     * @see org.springblade.thingcom.data.enums.HandleStatusEnum
     */
    private Integer handleStatus;

    /**
     * 解决建议
     */
    private String solution;

    /**
     * 时间
     */
    private LocalDateTime time;

    /**
     * 告警持续时间
     */
    private Long duration;

}

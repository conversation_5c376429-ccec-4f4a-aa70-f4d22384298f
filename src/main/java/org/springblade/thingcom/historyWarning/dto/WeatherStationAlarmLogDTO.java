package org.springblade.thingcom.historyWarning.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.thingcom.core.base.dto.BaseDTO;
import org.springblade.thingcom.operationMaintenance.enums.WeatherStationTypeEnum;

/**
 * <AUTHOR>
 * @since 2024-03-20 13:59
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WeatherStationAlarmLogDTO extends BaseDTO {
    private static final long serialVersionUID = -2125244466368042545L;

    /**
     * 告警类型
     */
    private Integer alarmType;
    /**
     * 告警内容
     */
    private String alarmContent;
    /**
     * 气象站编号
     */
    private Long weatherStationId;
    /**
     * 气象站类型，1-Thingcom；2-Arctech
     * @see WeatherStationTypeEnum
     */
    private Integer weatherStationType;
    /**
     * 气象站位置
     */
    private String location;
}

package org.springblade.thingcom.historyWarning.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.thingcom.core.base.dto.BaseDTO;

/**
 * <AUTHOR>
 * @since 2024-01-24 16:06
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GatewayAlarmLogDTO extends BaseDTO {
    private static final long serialVersionUID = 8594218620942851979L;

    /**
     * 告警类型
     */
    private Integer alarmType;

    /**
     * 告警内容
     */
    private String alarmContent;

    /**
     * 网关id
     */
    private Long gatewayId;
}

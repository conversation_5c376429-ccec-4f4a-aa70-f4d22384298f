package org.springblade.thingcom.historyWarning.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.thingcom.core.base.dto.BaseDTO;

/**
 * <AUTHOR>
 * @since 2024-03-21 15:51
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TrackerAlarmLogDTO extends BaseDTO {
    private static final long serialVersionUID = 6670687461865376614L;

    /**
     * 告警类型
     */
    private Integer alarmType;
    /**
     * 告警内容
     */
    private String alarmContent;
    /**
     * 跟踪器区域名称
     */
    private String trackerRegionalName;
    /**
     * 跟踪器名称
     */
    private String trackerName;
}

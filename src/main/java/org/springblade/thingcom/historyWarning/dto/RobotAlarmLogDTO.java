package org.springblade.thingcom.historyWarning.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.thingcom.core.base.dto.BaseDTO;

/**
 * <AUTHOR>
 * @since 2024-01-24 15:01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RobotAlarmLogDTO extends BaseDTO {
    private static final long serialVersionUID = -8567188157348379271L;

    /**
     * 告警类型
     */
    private Integer alarmType;

    /**
     * 机器人区域id
     */
    private Long robotRegionalId;

    /**
     * 机器人id
     */
    private Long robotId;
}

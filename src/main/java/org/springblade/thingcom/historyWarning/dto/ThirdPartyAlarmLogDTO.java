package org.springblade.thingcom.historyWarning.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.thingcom.core.base.dto.BaseDTO;

/**
 * <AUTHOR>
 * @since 2024-07-26 10:16
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ThirdPartyAlarmLogDTO extends BaseDTO {
    private static final long serialVersionUID = -7107720320441484959L;

    /**
     * 第三方对接信息表ID
     */
    private Long thirdPartyMessageId;

    /**
     * 名称
     */
    private String name;

    /**
     * 告警内容
     */
    private String alarmContent;
}

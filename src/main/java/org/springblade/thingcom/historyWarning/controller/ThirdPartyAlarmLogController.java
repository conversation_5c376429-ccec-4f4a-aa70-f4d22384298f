package org.springblade.thingcom.historyWarning.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import io.jsonwebtoken.lang.Collections;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.log.annotation.ApiLog;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.thingcom.core.excel.handler.AutoColumnWidthHandler;
import org.springblade.thingcom.core.excel.utils.ThingcomExcelUtil;
import org.springblade.thingcom.historyWarning.dto.ThirdPartyAlarmLogDTO;
import org.springblade.thingcom.historyWarning.dto.ThirdPartyAlarmLogSaveDTO;
import org.springblade.thingcom.historyWarning.entity.ThirdPartyAlarmLog;
import org.springblade.thingcom.historyWarning.excel.ThirdPartyAlarmLogExcel;
import org.springblade.thingcom.historyWarning.service.ThirdPartyAlarmLogService;
import org.springblade.thingcom.historyWarning.vo.ThirdPartyAlarmLogVo;
import org.springblade.thingcom.historyWarning.wrapper.ThirdPartyAlarmLogWrapper;
import org.springblade.thingcom.translate.utils.TranslateUtil;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * 第三方系统历史告警控制层
 *
 * <AUTHOR>
 * @since 2024-07-26 10:11:39
 */
@AllArgsConstructor
@RestController
@RequestMapping("/thirdPartyAlarmLog")
public class ThirdPartyAlarmLogController extends BladeController {
    private final ThirdPartyAlarmLogService thirdPartyAlarmLogService;

    /**
     * 第三方系统历史告警分页查询
     *
     * @param query
     * @param dto
     * @return
     */
    @GetMapping("/page")
    public R<IPage<ThirdPartyAlarmLogVo>> page(Query query, ThirdPartyAlarmLogDTO dto) {
        LambdaQueryWrapper<ThirdPartyAlarmLog> wrapper = setQueryParam(dto);
        IPage<ThirdPartyAlarmLog> iPage = thirdPartyAlarmLogService.page(Condition.getPage(query), wrapper);
        return data(ThirdPartyAlarmLogWrapper.build().pageVO(iPage));
    }

    /**
     * 第三方系统历史告警列表查询
     *
     * @param thirdPartyAlarmLog
     * @return
     */
    @GetMapping("/list")
    public R<List<ThirdPartyAlarmLogVo>> list(ThirdPartyAlarmLog thirdPartyAlarmLog) {
        List<ThirdPartyAlarmLog> list = thirdPartyAlarmLogService.list(Wrappers.<ThirdPartyAlarmLog>lambdaQuery(thirdPartyAlarmLog));
        return data(ThirdPartyAlarmLogWrapper.build().listVO(list));
    }

    /**
     * 查看第三方系统历史告警详情
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public R<ThirdPartyAlarmLogVo> show(@PathVariable Serializable id) {
        ThirdPartyAlarmLog entity = thirdPartyAlarmLogService.getById(id);
        return data(ThirdPartyAlarmLogWrapper.build().entityVO(entity));
    }

    /**
     * 新增第三方系统历史告警
     *
     * @param dto
     * @return
     */
    @ApiLog("新增第三方系统历史告警")
    @PostMapping
    public R save(@Valid @RequestBody ThirdPartyAlarmLogSaveDTO dto) {
        ThirdPartyAlarmLog entity = BeanUtil.copy(dto, ThirdPartyAlarmLog.class);

        // 检查数据合法性
        String errMsg = thirdPartyAlarmLogService.validateData(entity);
        if (errMsg != null) {
            throw new ServiceException(errMsg);
        }
        return status(thirdPartyAlarmLogService.save(entity));
    }

    /**
     * 修改第三方系统历史告警
     *
     * @param dto
     * @return
     */
    @ApiLog("修改第三方系统历史告警")
    @PutMapping
    public R update(@Valid @RequestBody ThirdPartyAlarmLogSaveDTO dto) {
        ThirdPartyAlarmLog entity = BeanUtil.copy(dto, ThirdPartyAlarmLog.class);

        // 检查数据合法性
        String errMsg = thirdPartyAlarmLogService.validateData(entity);
        if (errMsg != null) {
            throw new ServiceException(errMsg);
        }
        return status(thirdPartyAlarmLogService.updateById(entity));
    }

    /**
     * 批量删除第三方系统历史告警
     *
     * @param ids 用逗号分隔的多个id
     * @return
     */
    @ApiLog("批量删除第三方系统历史告警")
    @DeleteMapping("/{ids}")
    public R batchDelete(@PathVariable Serializable ids) {
        List<Long> idList = Func.toLongList(ids.toString());
        if (Collections.isEmpty(idList)) {
            return status(true);
        }
        return status(thirdPartyAlarmLogService.removeWithFill(new ThirdPartyAlarmLog(),
                Wrappers.<ThirdPartyAlarmLog>lambdaQuery().in(ThirdPartyAlarmLog::getId, idList)));
    }

    /**
     * 导出第三方系统历史告警数据
     *
     * @param response
     * @param dto
     * @throws IOException
     */
    @GetMapping("/export-data")
    public void exportData(HttpServletResponse response, ThirdPartyAlarmLogDTO dto) throws IOException {
        LambdaQueryWrapper<ThirdPartyAlarmLog> wrapper = setQueryParam(dto);
        List<ThirdPartyAlarmLog> list = thirdPartyAlarmLogService.list(wrapper);
        String sheetName = "第三方系统历史告警表";
        // 判断是否需要翻译
        boolean translate = TranslateUtil.isTranslate(dto.getLang());
        // 表内数据翻译
        List<ThirdPartyAlarmLogExcel> data = ThirdPartyAlarmLogWrapper.build().listToExcel(list, translate);
        // 表头翻译
        List<List<String>> headerList = ThingcomExcelUtil.getHeadList(ThirdPartyAlarmLogExcel.class, translate);
        // translate为真，翻译
        if (translate) {
            // 表文件名的翻译
            sheetName = TranslateUtil.chineseToEnglish(sheetName);
        }
        ThingcomExcelUtil.setExportDataFile(response, sheetName);
        EasyExcel.write(response.getOutputStream(), ThirdPartyAlarmLogExcel.class)
                .inMemory(true)
                .registerWriteHandler(new AutoColumnWidthHandler())
                .head(headerList)
                .sheet(sheetName).doWrite(data);
    }

    /**
     * sql检索条件
     *
     * @param dto
     * @return
     */
    private LambdaQueryWrapper<ThirdPartyAlarmLog> setQueryParam(ThirdPartyAlarmLogDTO dto) {
        LambdaQueryWrapper<ThirdPartyAlarmLog> lqw = new LambdaQueryWrapper<>();
        // 第三方对接信息表ID
        lqw.eq(Objects.nonNull(dto.getThirdPartyMessageId()), ThirdPartyAlarmLog::getThirdPartyMessageId
                , dto.getThirdPartyMessageId());
        // 名称，模糊查询
        lqw.like(StrUtil.isNotBlank(dto.getName()), ThirdPartyAlarmLog::getName, dto.getName());
        // 告警内容
        lqw.like(Func.isNotBlank(dto.getAlarmContent()), ThirdPartyAlarmLog::getAlarmContent, dto.getAlarmContent());
        // 起始日期
        if (Objects.nonNull(dto.getBeginDate()) && Objects.nonNull(dto.getEndDate())) {
            lqw.between(ThirdPartyAlarmLog::getUpdateTime, dto.getBeginDate(), dto.getEndDate());
        }
        // 更新时间倒序
        lqw.orderByDesc(ThirdPartyAlarmLog::getUpdateTime);
        return lqw;
    }

}

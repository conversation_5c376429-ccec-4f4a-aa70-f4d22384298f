package org.springblade.thingcom.historyWarning.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.thingcom.core.excel.handler.AutoColumnWidthHandler;
import org.springblade.thingcom.core.excel.utils.ThingcomExcelUtil;
import org.springblade.thingcom.historyWarning.dto.WeatherStationAlarmLogDTO;
import org.springblade.thingcom.historyWarning.entity.WeatherStationAlarmLog;
import org.springblade.thingcom.historyWarning.excel.GpsGatewayAlarmLogExcel;
import org.springblade.thingcom.historyWarning.excel.WeatherStationAlarmLogExcel;
import org.springblade.thingcom.historyWarning.service.WeatherStationAlarmLogService;
import org.springblade.thingcom.historyWarning.vo.WeatherStationAlarmLogVO;
import org.springblade.thingcom.historyWarning.wrapper.WeatherStationAlarmLogWrapper;
import org.springblade.thingcom.operationMaintenance.enums.WeatherStationTypeEnum;
import org.springblade.thingcom.translate.utils.TranslateUtil;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Objects;

/**
 * 气象站历史告警日志控制层
 *
 * <AUTHOR>
 * @since 2024-03-20 11:50:01
 */
@AllArgsConstructor
@RestController
@RequestMapping("/weatherStationAlarmLog")
public class WeatherStationAlarmLogController extends BladeController {
    private final WeatherStationAlarmLogService weatherStationAlarmLogService;

    /**
     * 气象站历史告警日志分页查询
     *
     * @param query
     * @param dto
     * @return
     */
    @GetMapping("/page")
    public R<IPage<WeatherStationAlarmLogVO>> page(Query query, WeatherStationAlarmLogDTO dto) {
        LambdaQueryWrapper<WeatherStationAlarmLog> wrapper = setQueryParam(dto);
        IPage<WeatherStationAlarmLog> iPage = weatherStationAlarmLogService.page(Condition.getPage(query), wrapper);
        return data(WeatherStationAlarmLogWrapper.build().pageVO(iPage));
    }

    /**
     * sql检索条件
     *
     * @param dto
     * @return
     */
    private LambdaQueryWrapper<WeatherStationAlarmLog> setQueryParam(WeatherStationAlarmLogDTO dto) {
        LambdaQueryWrapper<WeatherStationAlarmLog> lqw = new LambdaQueryWrapper<>();
        lqw.eq(Objects.nonNull(dto.getWeatherStationId()), WeatherStationAlarmLog::getWeatherStationId
                , dto.getWeatherStationId());
        lqw.eq(Objects.nonNull(dto.getAlarmType()), WeatherStationAlarmLog::getAlarmType, dto.getAlarmType());
        // 告警内容
        lqw.like(Func.isNotBlank(dto.getAlarmContent()), WeatherStationAlarmLog::getAlarmContent, dto.getAlarmContent());
        // 气象站类型，默认1
        if (Objects.isNull(dto.getWeatherStationType())) {
            dto.setWeatherStationType(WeatherStationTypeEnum.THINGCOM.getValue());
        }
        lqw.eq(WeatherStationAlarmLog::getWeatherStationType, dto.getWeatherStationType());
        // 位置
        lqw.like(StrUtil.isNotBlank(dto.getLocation()), WeatherStationAlarmLog::getLocation, dto.getLocation());
        // 起始时间
        if (Objects.nonNull(dto.getBeginDate()) && Objects.nonNull(dto.getEndDate())) {
            lqw.between(WeatherStationAlarmLog::getTime, dto.getBeginDate(), dto.getEndDate());
        }
        // 时间倒序
        lqw.orderByDesc(WeatherStationAlarmLog::getTime);
        return lqw;
    }

    /**
     * 导出
     *
     * @param dto
     * @param response
     * @throws Exception
     */
    @GetMapping("/export-data")
    public void exportData(WeatherStationAlarmLogDTO dto, HttpServletResponse response) throws Exception {
        // 气象站类型
        if (Objects.isNull(dto.getWeatherStationType())) {
            dto.setWeatherStationType(WeatherStationTypeEnum.THINGCOM.getValue());
        }
        LambdaQueryWrapper<WeatherStationAlarmLog> wrapper = setQueryParam(dto);
        List<WeatherStationAlarmLog> list = weatherStationAlarmLogService.list(wrapper);
        if (WeatherStationTypeEnum.THINGCOM.getValue().equals(dto.getWeatherStationType())) {
            String sheetName = "采集器历史告警";
            // 判断是否需要翻译
            boolean translate = TranslateUtil.isTranslate(dto.getLang());
            // 表内数据常量翻译
            List<GpsGatewayAlarmLogExcel> data = WeatherStationAlarmLogWrapper.build().listToExcel1(list, translate);
            // 表头翻译
            List<List<String>> headerList = ThingcomExcelUtil.getHeadList(GpsGatewayAlarmLogExcel.class, translate);
            // translate为真，翻译
            if (translate) {
                // 表文件名的翻译
                sheetName = TranslateUtil.chineseToEnglish(sheetName);
            }
            ThingcomExcelUtil.setExportDataFile(response, sheetName);
            EasyExcel.write(response.getOutputStream(), GpsGatewayAlarmLogExcel.class)
                    .inMemory(true)
                    .registerWriteHandler(new AutoColumnWidthHandler())
                    .head(headerList)
                    .sheet(sheetName).doWrite(data);
        } else {
            String sheetName = "气象站历史告警";
            // 判断是否需要翻译
            boolean translate = TranslateUtil.isTranslate(dto.getLang());
            // 表内数据常量翻译
            List<WeatherStationAlarmLogExcel> data = WeatherStationAlarmLogWrapper.build().listToExcel2(list, translate);
            // 表头翻译
            List<List<String>> headerList = ThingcomExcelUtil.getHeadList(WeatherStationAlarmLogExcel.class, translate);
            // translate为真，翻译
            if (translate) {
                // 表文件名的翻译
                sheetName = TranslateUtil.chineseToEnglish(sheetName);
            }
            ThingcomExcelUtil.setExportDataFile(response, sheetName);
            EasyExcel.write(response.getOutputStream(), WeatherStationAlarmLogExcel.class)
                    .inMemory(true)
                    .registerWriteHandler(new AutoColumnWidthHandler())
                    .head(headerList)
                    .sheet(sheetName).doWrite(data);
        }
    }
}

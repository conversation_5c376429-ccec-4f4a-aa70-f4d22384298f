package org.springblade.thingcom.historyWarning.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.thingcom.core.excel.handler.AutoColumnWidthHandler;
import org.springblade.thingcom.core.excel.utils.ThingcomExcelUtil;
import org.springblade.thingcom.historyWarning.dto.TrackerAlarmLogDTO;
import org.springblade.thingcom.historyWarning.entity.TrackerAlarmLog;
import org.springblade.thingcom.historyWarning.excel.TrackerAlarmLogExcel;
import org.springblade.thingcom.historyWarning.service.TrackerAlarmLogService;
import org.springblade.thingcom.historyWarning.vo.TrackerAlarmLogVO;
import org.springblade.thingcom.historyWarning.wrapper.TrackerAlarmLogWrapper;
import org.springblade.thingcom.translate.utils.TranslateUtil;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Objects;

/**
 * 跟踪器历史告警日志控制层
 *
 * <AUTHOR>
 * @since 2024-03-21 15:46:55
 */
@AllArgsConstructor
@RestController
@RequestMapping("/trackerAlarmLog")
public class TrackerAlarmLogController extends BladeController {
    private final TrackerAlarmLogService trackerAlarmLogService;

    /**
     * 跟踪器历史告警日志分页查询
     *
     * @param query
     * @param dto
     * @return
     */
    @GetMapping("/page")
    public R<IPage<TrackerAlarmLogVO>> page(Query query, TrackerAlarmLogDTO dto) {
        LambdaQueryWrapper<TrackerAlarmLog> wrapper = setQueryParam(dto);
        IPage<TrackerAlarmLog> iPage = trackerAlarmLogService.page(Condition.getPage(query), wrapper);
        return data(TrackerAlarmLogWrapper.build().pageVO(iPage));
    }

    /**
     * sql检索条件
     *
     * @param dto
     * @return
     */
    private LambdaQueryWrapper<TrackerAlarmLog> setQueryParam(TrackerAlarmLogDTO dto) {
        LambdaQueryWrapper<TrackerAlarmLog> lqw = new LambdaQueryWrapper<>();
        // 告警类型
        lqw.eq(Objects.nonNull(dto.getAlarmType()), TrackerAlarmLog::getAlarmType, dto.getAlarmType());
        // 告警内容
        lqw.like(Func.isNotBlank(dto.getAlarmContent()), TrackerAlarmLog::getAlarmContent, dto.getAlarmContent());
        // 跟踪器区域名称
        lqw.like(StrUtil.isNotBlank(dto.getTrackerRegionalName()), TrackerAlarmLog::getTrackerRegionalName, dto.getTrackerRegionalName());
        // 跟踪器名称
        lqw.like(StrUtil.isNotBlank(dto.getTrackerName()), TrackerAlarmLog::getTrackerName, dto.getTrackerName());
        // 时间查询
        if (Objects.nonNull(dto.getBeginDate()) && Objects.nonNull(dto.getEndDate())) {
            lqw.between(TrackerAlarmLog::getTime, dto.getBeginDate(), dto.getEndDate());
        }
        // 时间倒序
        lqw.orderByDesc(TrackerAlarmLog::getTime);
        return lqw;
    }

    /**
     * 导出
     *
     * @param dto
     * @param response
     * @throws Exception
     */
    @GetMapping("/export-data")
    public void exportData(TrackerAlarmLogDTO dto, HttpServletResponse response) throws Exception {
        LambdaQueryWrapper<TrackerAlarmLog> wrapper = setQueryParam(dto);
        List<TrackerAlarmLog> list = trackerAlarmLogService.list(wrapper);
        String sheetName = "支架历史告警";

        // 判断是否需要翻译
        boolean translate = TranslateUtil.isTranslate(dto.getLang());
        // 表内数据常量翻译
        List<TrackerAlarmLogExcel> data = TrackerAlarmLogWrapper.build().listToExcel(list, translate);
        // 表头翻译
        List<List<String>> headerList = ThingcomExcelUtil.getHeadList(TrackerAlarmLogExcel.class, translate);
        // translate为真，翻译
        if (translate) {
            // 表文件名的翻译
            sheetName = TranslateUtil.chineseToEnglish(sheetName);
        }

        ThingcomExcelUtil.setExportDataFile(response, sheetName);
        EasyExcel.write(response.getOutputStream(), TrackerAlarmLogExcel.class)
                .inMemory(true)
                .registerWriteHandler(new AutoColumnWidthHandler())
                .head(headerList)
                .sheet(sheetName).doWrite(data);
    }
}

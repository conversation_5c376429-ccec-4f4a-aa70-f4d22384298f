package org.springblade.thingcom.historyWarning.controller;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.thingcom.core.excel.handler.AutoColumnWidthHandler;
import org.springblade.thingcom.core.excel.utils.ThingcomExcelUtil;
import org.springblade.thingcom.historyWarning.dto.ServerAlarmLogDTO;
import org.springblade.thingcom.historyWarning.entity.ServerAlarmLog;
import org.springblade.thingcom.historyWarning.excel.ServerAlarmLogExcel;
import org.springblade.thingcom.historyWarning.service.ServerAlarmLogService;
import org.springblade.thingcom.historyWarning.vo.ServerAlarmLogVO;
import org.springblade.thingcom.historyWarning.wrapper.ServerAlarmLogWrapper;
import org.springblade.thingcom.translate.utils.TranslateUtil;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 服务器历史告警日志控制层
 *
 * <AUTHOR>
 * @since 2024-03-11 17:10:13
 */
@AllArgsConstructor
@RestController
@RequestMapping("/serverAlarmLog")
public class ServerAlarmLogController extends BladeController {
    private final ServerAlarmLogService serverAlarmLogService;

    /**
     * 服务器历史告警日志分页查询
     *
     * @param query
     * @param dto
     * @return
     */
    @GetMapping("/page")
    public R<IPage<ServerAlarmLogVO>> page(Query query, ServerAlarmLogDTO dto) {
        LambdaQueryWrapper<ServerAlarmLog> wrapper = ServerAlarmLogWrapper.setQueryParam(dto);
        IPage<ServerAlarmLog> iPage = serverAlarmLogService.page(Condition.getPage(query), wrapper);
        return data(ServerAlarmLogWrapper.build().pageVO(iPage));
    }

    /**
     * 导出
     *
     * @param dto
     * @param response
     * @throws Exception
     */
    @GetMapping("/export-data")
    public void exportData(ServerAlarmLogDTO dto, HttpServletResponse response) throws Exception {
        LambdaQueryWrapper<ServerAlarmLog> wrapper = ServerAlarmLogWrapper.setQueryParam(dto);
        List<ServerAlarmLog> list = serverAlarmLogService.list(wrapper);
        String sheetName = "服务器历史告警";
        // 判断是否需要翻译
        boolean translate = TranslateUtil.isTranslate(dto.getLang());
        // 表头翻译
        List<List<String>> headerList = ThingcomExcelUtil.getHeadList(ServerAlarmLogExcel.class,translate);
        // 表内数据翻译
        List<ServerAlarmLogExcel> data = ServerAlarmLogWrapper.build().listToExcel(list, translate);
        // translate为真，翻译
        if (translate) {
            // 表文件名的翻译
            sheetName = TranslateUtil.chineseToEnglish(sheetName);
        }
        ThingcomExcelUtil.setExportDataFile(response, sheetName);
        EasyExcel.write(response.getOutputStream(), ServerAlarmLogExcel.class)
                .inMemory(true)
                .registerWriteHandler(new AutoColumnWidthHandler())
                .head(headerList)
                .sheet(sheetName).doWrite(data);
    }
}

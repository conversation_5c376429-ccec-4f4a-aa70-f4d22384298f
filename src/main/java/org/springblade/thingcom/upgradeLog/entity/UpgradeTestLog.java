package org.springblade.thingcom.upgradeLog.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springblade.thingcom.core.mybatisPlus.entity.BaseEntity;

import java.io.Serializable;

/**
 * 机器人升级测试日志实体类
 *
 * <AUTHOR>
 * @since 2024-11-27 09:33:18
 */
@Data
@TableName("tl_upgrade_test_log")
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class UpgradeTestLog extends BaseEntity<UpgradeTestLog> implements Serializable {
    private static final long serialVersionUID = 1L;

    private String content;

    private String number;

}

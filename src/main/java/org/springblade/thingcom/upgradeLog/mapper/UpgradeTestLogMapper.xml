<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.thingcom.upgradeLog.mapper.UpgradeTestLogMapper">

    <resultMap type="org.springblade.thingcom.upgradeLog.entity.UpgradeTestLog" id="UpgradeTestLogMap">
        <result column="id" property="id"/>
        <result column="content" property="content"/>
        <result column="number" property="number"/>
        <result column="creator_id" property="creatorId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_id" property="updateId"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="deleted"/>
    </resultMap>

    <!--新增所有列-->
    <!--    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
            insert into tl_upgrade_test_log(content, number, creator_id, create_time, update_id, update_time, is_deleted)
            values (#{content}, #{number}, #{creatorId}, #{createTime}, #{updateId}, #{updateTime}, #{isDeleted})
        </insert>

        <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
            insert into tl_upgrade_test_log(content, number, creator_id, create_time, update_id, update_time, is_deleted)
            values
            <foreach collection="entities" item="entity" separator=",">
            (#{entity.content}, #{entity.number}, #{entity.creatorId}, #{entity.createTime}, #{entity.updateId}, #{entity.updateTime}, #{entity.isDeleted})
            </foreach>
        </insert>-->

</mapper>

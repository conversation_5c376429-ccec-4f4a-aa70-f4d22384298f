package org.springblade.thingcom.upgradeLog.service;

import org.springblade.thingcom.core.mybatisPlus.IBaseService;
import org.springblade.thingcom.upgradeLog.entity.UpgradeTestLog;

/**
 * 机器人升级测试日志服务接口
 *
 * <AUTHOR>
 * @since 2024-11-27 09:33:18
 */
public interface UpgradeTestLogService extends IBaseService<UpgradeTestLog> {

    /**
     * 检查数据合法性
     *
     * @param entity 实体对象
     * @return 异常消息，正常时为null
     */
    String validateData(UpgradeTestLog entity);

    /**
     * 新增机器人升级测试日志
     *
     * @param entity
     * @return
     */
    boolean saveData(UpgradeTestLog entity);

    /**
     * 修改机器人升级测试日志
     *
     * @param entity
     * @return
     */
    boolean updateData(UpgradeTestLog entity);
}

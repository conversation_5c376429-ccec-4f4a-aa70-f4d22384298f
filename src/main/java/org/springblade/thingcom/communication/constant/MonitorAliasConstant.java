package org.springblade.thingcom.communication.constant;

/**
 * @ClassName MonitorAliasConstant
 * @Description: 监控点别名常量（tb_monitor 表中 monitor_alias 字段）
 * @Author: zgq
 * @Date: 2024/3/26 16:59
 * @Version: 1.0
 **/
public interface MonitorAliasConstant {

    /**
     * 通信质量
     */
    String RSSI = "rssi";

    /**
     * 信噪比
     */
    String LORA_SNR = "loraSnr";

    /**
     * 电流
     */
    String ELECTRICITY = "Electricity";

    /**
     * 清扫
     */
    String CLEARING = "clearing";

    /*** 电池参数 ***/

    /**
     * 电池电压
     */
    String BATTERY_VOLTAGE = "batteryVoltage";

    /**
     * 电池电流
     */
    String BATTERY_ELECTRICITY = "batteryElectricity";

    /**
     * 电池状态
     */
    String BATTERY_STATUS = "batteryStatus";

    /**
     * 电池电量
     */
    String BATTERY_CAPACITY = "batteryCapacity";

    /**
     * 电池温度
     */
    String BATTERY_TEMPERATURE = "batteryTemperature";

    /**
     * 高温阈值
     */
    String BATTERY_HIGH_TEMPERATURE_THRESHOLD = "batteryHighTemperatureThreshold";

    /**
     * 低温阈值
     */
    String BATTERY_LOW_TEMPERATURE_THRESHOLD = "batteryLowTemperatureThreshold";

    /**
     * 保护温度
     */
    String BATTERY_PROTECT_TEMPERATURE = "batteryProtectTemperature";

    /**
     * 恢复温度
     */
    String BATTERY_RECOVER_TEMPERATURE = "batteryRecoverTemperature";

    /**
     * 保护电压
     */
    String BATTERY_PROTECT_VOLTAGE = "batteryProtectVoltage";

    /**
     * 恢复电压
     */
    String BATTERY_RECOVER_VOLTAGE = "batteryRecoverVoltage";

    /**
     * 保护电量
     */
    String BATTERY_PROTECT_CAPACITY = "batteryProtectCapacity";

    /**
     * 恢复电量
     */
    String BATTERY_RECOVER_CAPACITY = "batteryRecoverCapacity";

    /**
     * 保护电流
     */
    String BATTERY_PROTECT_ELECTRICITY = "batteryProtectElectricity";

    /**
     * 限制电量
     */
    String BATTERY_ASTRICT_CAPACITY = "batteryAstrictCapacity";

    /*** 机器人时间&环境数据 参数 ***/

    /**
     * 白夜状态（光线传感器状态）
     */
    String DAY_AND_NIGHT_STATUS = "dayAndNightStatus";

    /*** Lora 参数 ***/

    /**
     * Lora 发射功率
     */
    String LORA_SEND_POWER = "loraSendPower";

    /**
     * Lora 频段
     */
    String LORA_FREQUENCY_BAND = "loraFrequencyBand";

    /**
     * Lora 速率
     */
    String LORA_SPEED_RATE = "loraSpeedRate";

    /**
     * 机器人编号
     */
    String ROBOT_NUMBER = "robotNumber";

    /*** 控制板参数 ***/

    /**
     * 运行反转时间
     */
    String REVERSE_WAIT_TIME = "reverseWaitTime";

    /**
     * 保护角度
     */
    String PROTECT_ANGLE = "protectAngle";

    /**
     * 电机速率
     */
    String MOTOR_RUN_RATE = "motorRunRate";

    /**
     * 电机上限电流停机值
     */
    String MOTOR_ELECTRICITY_MAX_ASTRICT_STOP = "motorElectricityMaxAstrictStop";

    /**
     * 电机上限电流预警值
     */
    String MOTOR_ELECTRICITY_MAX_ASTRICT_WARN = "motorElectricityMaxAstrictWarn";

    /**
     * 电机运行超时时间
     */
    String MOTOR_OVERSTEP_RUN_TIME = "motorOverstepRunTime";

    /*** 风速 ***/

    /**
     * 风速阈值
     */
    String WIND_SPEED_THRESHOLD = "windSpeedThreshold";

    /**
     * 本地风速
     */
    String CURRENT_WIND_SPEED = "currentWindSpeed";

    /*** 停机位 ***/

    /**
     * 停机位
     */
    String STOP_BIT_VALUE = "stopBitValue";

    /**
     * 白天防误扫开关标志
     */
    String DAYTIME_PREVENT_CLEAN = "daytimePreventClean";

    /*** 定时器 ***/

    /**
     * 定时器-星期
     */
    String TIMER_WEEK = "timerWeek";

    /**
     * 定时器-时
     */
    String TIMER_HOUR = "timerHour";

    /**
     * 定时器-分
     */
    String TIMER_MINUTE = "timerMinute";

    /**
     * 定时器-运行次数
     */
    String TIMER_RUN_COUNT = "timerRunCount";

    /**
     * 锁定状态
     */
    String LOCK_STATUS = "lockStatus";

    /*** 主板温度 ***/

    /**
     * 主板温度
     */
    String MAIN_BOARD_TEMPERATURE = "mainBoardTemperature";

    /**
     * 保护温度
     */
    String MAIN_BOARD_PROTECTION_TEMPERATURE = "mainBoardProtectionTemperature";

    /**
     * 恢复温度
     */
    String MAIN_BOARD_RECOVERY_TEMPERATURE = "mainBoardRecoveryTemperature";

    /*** 电机状态 ***/

    /**
     * f01（上下线，0-离线；1-上线）
     */
    String F01 = "f01";

    /*** fa - fd，参考：tb_status_analysis 数据表 ***/

    /**
     * fa（模式&状态）
     */
    String FA = "fa";

    /**
     * fb（保留）
     */
    String FB = "fb";

    /**
     * fc（故障）
     */
    String FC = "fc";

    /**
     * fd（预警）
     */
    String FD = "fd";

    /*** 电机电流 ***/

    /**
     * 主电机电流（单个）
     */
    String MASTER_MOTOR_ELECTRICITY = "masterMotorElectricity";

    /**
     * 从电机电流（单个）
     */
    String SLAVE_MOTOR_ELECTRICITY = "slaveMotorElectricity";

    /**
     * 主电机电流（多个）
     */
    String MASTER_MOTOR_ELECTRICITYS = "masterMotorElectricitys";

    /**
     * 从电机电流（多个）
     */
    String SLAVE_MOTOR_ELECTRICITYS = "slaveMotorElectricitys";

    /**
     * 位置
     */
    String LOCATION = "location";

    /**
     * 方向，1-前进；2-后退
     */
    String DIRECTION = "direction";

    /**
     * 电流上报时间间隔(秒)
     */
    String ELECTRICITY_DATA_INTERVAL = "electricityDataInterval";

    /**
     * 工作时长（秒）
     */
    String WORK_DURATION = "workDuration";

    /**
     * 软件版本
     */
    String SOFTWARE_VERSION = "softwareVersion";

    /**
     * 光伏板输出电压（V）
     */
    String PV_PANEL_OUTPUT_VOLTAGE = "pvPanelOutputVoltage";

    /**
     * 光伏板输出电流（A）
     */
    String PV_PANEL_OUTPUT_ELECTRICITY = "pvPanelOutputElectricity";

    /**
     * 定时器启动请求
     */
    String TIMER_START_REQUEST = "timerStartRequest";

    /**
     * 当前运行圈数
     */
    String CURRENT_RUN_LAPS = "currentRunLaps";

    /**
     * 机器人本地时间-年
     */
    String ROBOT_TIME_YEAR = "robotTimeYear";

    /**
     * 机器人本地时间-月
     */
    String ROBOT_TIME_MONTH = "robotTimeMonth";

    /**
     * 机器人本地时间-日
     */
    String ROBOT_TIME_DAY = "robotTimeDay";

    /**
     * 机器人本地时间-时
     */
    String ROBOT_TIME_HOUR = "robotTimeHour";

    /**
     * 机器人本地时间-分
     */
    String ROBOT_TIME_MINUTE = "robotTimeMinute";

    /**
     * 机器人本地时间-秒
     */
    String ROBOT_TIME_SECOND = "robotTimeSecond";

    /**
     * 机器人本地时间-星期
     */
    String ROBOT_TIME_WEEK = "robotTimeWeek";

    /**
     * 机器人异常定时器-编号
     */
    String EXCEPTION_TIMER_NUMBER = "exceptionTimerNumber";

    /**
     * 机器人异常定时器-星期
     */
    String EXCEPTION_TIMER_WEEK = "exceptionTimerWeek";

    /**
     * 机器人异常定时器-时
     */
    String EXCEPTION_TIMER_HOUR = "exceptionTimerHour";

    /**
     * 机器人异常定时器-分
     */
    String EXCEPTION_TIMER_MINUTE = "exceptionTimerMinute";

    /**
     * 定时未请求原因
     */
    String EXCEPTION_TIMER_CAUSES = "exceptionTimerCauses";

    /**
     * 定时未请求-FC故障
     */
    String EXCEPTION_TIMER_FC = "exceptionTimerFc";

    /**
     * 未启动原因
     */
    String EXCEPTION_START_CAUSES = "exceptionStartCauses";

    /**
     * 未启动原因-FC故障
     */
    String EXCEPTION_START_FC = "exceptionStartFc";

    /**
     * 清扫状态变化-时
     */
    String CLEARING_STATUS_CHANGE_HOUR = "clearingStatusChangeHour";

    /**
     * 清扫状态变化-分
     */
    String CLEARING_STATUS_CHANGE_MINUTE = "clearingStatusChangeMinute";

    /**
     * 清扫状态变化-秒
     */
    String CLEARING_STATUS_CHANGE_SECOND = "clearingStatusChangeSecond";

    /**
     * 清扫开始时间-日
     */
    String CLEARING_TIME_DAY = "clearingTimeDay";

    /**
     * 清扫开始时间-时
     */
    String CLEARING_TIME_HOUR = "clearingTimeHour";

    /**
     * 清扫开始时间-分
     */
    String CLEARING_TIME_MINUTE = "clearingTimeMinute";

    /**
     * 清扫时长，设备上报为分钟，数据处理时转换为秒（和 workDuration、数据库中的单位保持一致）
     */
    String CLEARING_DURATION = "clearingDuration";

    /**
     * 国家代码
     */
    String COUNTRY_CODE = "countryCode";

    /**
     * 地区代码
     */
    String REGION_CODE = "regionCode";

    /**
     * 项目代码
     */
    String PROJECT_CODE = "projectCode";
}

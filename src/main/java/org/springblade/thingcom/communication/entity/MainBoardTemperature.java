package org.springblade.thingcom.communication.entity;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @ClassName MainBoardTemperature
 * @Description 主板温度
 * <AUTHOR>
 * @Date 2025/4/8 11:49
 * @Version 1.0
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MainBoardTemperature implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主板温度，单位 ℃
     */
    private BigDecimal mainBoardTemperature;

    /**
     * 保护温度，单位 ℃
     */
    private BigDecimal protectionTemperature;

    /**
     * 恢复温度，单位 ℃
     */
    private BigDecimal recoveryTemperature;
}

package org.springblade.thingcom.communication.entity;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @ClassName Timer
 * @Description: 定时器
 * @Author: zgq
 * @Date: 2024/1/20 15:23
 * @Version: 1.0
 **/
@Data
public class Timer implements Serializable {
    private static final long serialVersionUID = -4157500227508328842L;

    /**
     * 定时器编号
     */
    private Integer number;

    /**
     * 星期，1-7分别表示周一到周日；255表示每天
     */
    @NotNull(message = "星期不能为空")
    private Integer week;

    /**
     * 时间，格式：HH:mm，默认00:00
     */
    @NotBlank(message = "时间不能为空")
    private String time;

    /**
     * 时间-时
     */
    private Integer hour;

    /**
     * 时间-分
     */
    private Integer minute;

    /**
     * 运行次数，默认1次
     * 下发到设备端的次数 * 2 = 设备端回复的次数（往返算 1 次，设备端是：往返算 2 次）
     */
    @Min(value = 1, message = "运行次数必须大于 1")
    @Max(value = 99, message = "运行次数不能超过 99")
    private Integer runCount = 1;
}

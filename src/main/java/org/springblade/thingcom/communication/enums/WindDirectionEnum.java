package org.springblade.thingcom.communication.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @ClassName WindDirectionEnum
 * @Description 风向枚举类
 * <AUTHOR>
 * @Date 2024/10/9 19:35
 * @Version 1.0
 **/
@Getter
@AllArgsConstructor
public enum WindDirectionEnum {

    NORTH_EXT("N", "北", 348.76, 11.25),
    NORTH("N", "北", 11.26, 33.75),

    EAST_NORTH("EN", "东北", 33.76, 78.75),

    EAST("E", "东", 78.76, 123.75),

    EAST_SOUTH("ES", "东南", 123.76, 168.75),

    SOUTH("S", "南", 168.76, 213.75),

    WEST_SOUTH("WS", "西南", 213.76, 258.75),

    WEST("W", "西", 258.76, 303.75),

    WEST_NORTH("WN", "西北", 303.76, 348.75),

    ;

    private final String sign;
    private final String name;
    private final double startAngle;
    private final double endAngle;

    /**
     * 根据角度获取风向枚举类
     *
     * @param angle 角度
     * @return 风向枚举类
     */
    public static WindDirectionEnum getByAngle(double angle) {
        WindDirectionEnum northExt = WindDirectionEnum.NORTH_EXT;
        if (angle < northExt.endAngle || angle > northExt.startAngle) {
            return northExt;
        }
        return Arrays.stream(WindDirectionEnum.values())
                .filter(direction -> !direction.equals(northExt))
                .filter(direction -> angle >= direction.getStartAngle() && angle <= direction.getEndAngle())
                .findFirst().orElse(null);
    }

}

package org.springblade.thingcom.communication.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @ClassName RegionFrequencyBandEnum
 * @Description 区域频段枚举
 * <AUTHOR>
 * @Date 2024/9/26 16:13
 * @Version 1.0
 **/
@Getter
@AllArgsConstructor
public enum RegionFrequencyBandEnum {

    /**
     * 中国CN470
     */
    CHINA_CN470(100, "中国CN470"),

    /**
     * 印度
     */
    INDIA(101, "印度"),

    /**
     * 北美
     */
    NORTH_AMERICA(102, "北美"),

    /**
     * 欧洲
     */
    EUROPE(103, "欧洲"),

    /**
     * 亚洲1版本
     */
    ASIA1(104, "亚洲1版本"),

    /**
     * 亚洲2版本
     */
    ASIA2(105, "亚洲2版本"),

    ;

    private final Integer value;
    private final String label;

    /**
     * 根据value获取label
     *
     * @param value
     * @return
     */
    public static String getLabelByValue(Integer value) {
        if (value < RegionFrequencyBandEnum.CHINA_CN470.getValue()) {
            value = RegionFrequencyBandEnum.CHINA_CN470.getValue();
        }
        Integer finalValue = value;
        RegionFrequencyBandEnum regionFrequencyBandEnum = Arrays.stream(RegionFrequencyBandEnum.values())
                .filter(item -> item.getValue().equals(finalValue))
                .findFirst().orElse(null);
        if (regionFrequencyBandEnum == null) {
            return null;
        }
        return regionFrequencyBandEnum.getLabel();
    }

}

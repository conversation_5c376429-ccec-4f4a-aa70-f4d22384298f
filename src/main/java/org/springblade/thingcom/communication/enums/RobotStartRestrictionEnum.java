package org.springblade.thingcom.communication.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 机器人启动限制条件枚举
 */
@Getter
@AllArgsConstructor
public enum RobotStartRestrictionEnum {

    /**
     * 时差
     */
    TIME_DIFFERENCE_EXCEED_10_MINUTES(1, "机器人请求时间与系统时间相差超过 10 分钟"),

    /**
     * 清扫路径无跟踪架
     */
    NO_TRACKER_IN_PATH(2, "机器人对应的清扫路径下暂无跟踪架"),

    /**
     * 有离线的跟踪架
     */
    TRACKER_OFFLINE(3, "有离线的跟踪架"),

    /**
     * 有告警的跟踪架
     */
    TRACKER_ALARM(4, "有告警的跟踪架"),

    /**
     * 跟踪架角度不符合范围
     */
    TRACKER_ANGLE_NOT_IN_RANGE(5, "有角度不符合要求的跟踪架"),

    /**
     * 风速超过阈值
     */
    WIND_SPEED_EXCEED_THRESHOLD(6, "风速超过阈值"),

    /**
     * 湿度超过阈值
     */
    HUMIDITY_EXCEED_THRESHOLD(7, "湿度超过阈值"),

    ;

    private final Integer value;
    private final String label;

}

package org.springblade.thingcom.communication.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 机器人位置枚举
 */
@Getter
@AllArgsConstructor
public enum RobotLocationEnum {

    /**
     * 起始桥架
     */
    START(StopBitEnum.START.getValue(), "起始桥架"),
    /**
     * 返回桥架
     */
    END(StopBitEnum.END.getValue(), "返回桥架"),
    /**
     * 2 个跟踪架中间的连接处
     */
    MIDDLE_CONNECT(2, "{}和{}之间的桥架"),
    /**
     * 位于跟踪器上面
     */
    TRACKER(3, "跟踪器{}上面"),
    ;

    private final Integer value;
    private final String label;

    /**
     * 翻译编码
     */
    public static String translationValue(Integer value) {
        for (RobotLocationEnum e : RobotLocationEnum.values()) {
            if (e.value.equals(value)) {
                return e.label;
            }
        }
        return null;
    }
}

package org.springblade.thingcom.communication.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 电池状态枚举
 */
@Getter
@AllArgsConstructor
public enum BatteryStatusEnum {

    /**
     * 正常
     */
    NORMAL(0, "正常"),

    /**
     * 电池包通信故障
     */
    COMMUNICATION_FAILURE(1, "电池包通信故障"),

    /**
     * 电池电压保护
     */
    OVER_VOLTAGE_PROTECTION(2, "电压保护"),

    /**
     * 电池温度保护
     */
    OVER_TEMPERATURE_PROTECTION(4, "过热保护"),

    /**
     * 电池电流保护
     */
    OVER_CURRENT_PROTECTION(8, "电流保护"),

    /**
     * 低电量保护
     */
    LOW_BATTERY_PROTECTION(16, "低电量保护"),

    /**
     * 放电欠压
     */
    DISCHARGE_UNDERVOLTAGE(32, "放电欠压"),

    /**
     * 放电温度故障
     */
    DISCHARGE_TEMPERATURE_FAULT(64, "放电温度故障"),

    /**
     * 放电过流
     */
    DISCHARGE_OVERCURRENT(128, "放电过流"),

    /**
     * 放电短路
     */
    DISCHARGE_SHORT_CIRCUIT(256, "放电短路"),

    /**
     * 充电过压
     */
    CHARGE_OVERVOLTAGE(512, "充电过压"),

    /**
     * 充电过温
     */
    CHARGE_OVERTEMPERATURE(1024, "充电过温"),

    /**
     * 超低压或者断线
     */
    CHARGE_UNDERVOLTAGE(2048, "超低压或者断线"),

    /**
     * 电池寿命到期
     */
    BATTERY_LIFE_EXPIRED(4096, "电池寿命到期"),

    /**
     * 离线
     */
    OFFLINE(65535, "离线"),

    ;

    /**
     * 编码，10 进制，转 2 进制后，兼容多种状态
     */
    private final Integer value;

    /**
     * 翻译编码
     */
    private final String label;

    /**
     * 翻译编码
     */
    public static String translationValue(Integer value) {
        if (Objects.isNull(value)) {
            return NORMAL.label;
        }
        if (NORMAL.getValue().equals(value)) {
            return NORMAL.label;
        }
        if (OFFLINE.getValue().equals(value)) {
            return OFFLINE.label;
        }
        // 使用位运算，兼容所有的状态
        String statusName = Arrays.stream(BatteryStatusEnum.values())
                .filter(batteryStatusEnum -> !NORMAL.getValue().equals(batteryStatusEnum.getValue())
                        && !OFFLINE.getValue().equals(batteryStatusEnum.getValue())
                        && (value & batteryStatusEnum.getValue()) == batteryStatusEnum.getValue())
                .map(BatteryStatusEnum::getLabel)
                .collect(Collectors.joining(","));
        if (StrUtil.isBlank(statusName)) {
            return NORMAL.label;
        }
        return statusName;
    }

}
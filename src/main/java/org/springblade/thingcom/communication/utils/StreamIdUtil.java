package org.springblade.thingcom.communication.utils;

import cn.hutool.core.util.StrUtil;
import org.springblade.common.cache.CacheNames;
import org.springblade.common.utils.NumberUtil;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.thingcom.command.constant.CommandConstant;
import org.springblade.thingcom.command.enums.CommandEnum;
import org.springblade.thingcom.communication.constant.ParamConstant;
import org.springblade.thingcom.communication.constant.StreamIdConstant;
import org.springblade.thingcom.device.entity.Robot;

/**
 * @ClassName StreamIdUtil
 * @Description: StreamId工具类
 * @Author: zgq
 * @Date: 2024/4/3 10:06
 * @Version: 1.0
 **/
public class StreamIdUtil {

    private static final BladeRedis bladeRedis;

    static {
        bladeRedis = SpringUtil.getBean(BladeRedis.class);
    }

    /**
     * 获取 数据 streamId
     * data 由以下组成，详见：<a href="https://thingcom.yuque.com/rfub4n/aws2ly/faxvm6qrf6h1x8oc#b50Yb">...</a>
     * 1. 帧头（2个字符）（内容固定）
     * 2. 控制码（2个字符）（内容固定）
     * 3. 机器人编号（4个字符，机器人10进制编号转为16进制，占用4个字符，最大65534，因为65535 == FFFF）（内容不固定）
     * 4. 帧计数器（2个字符）（内容固定）
     * 5. 数据域长度（2个字符）（内容不固定）
     * 6. 数据域：{命令标识（2个字符）（内容固定）；参数（内容不固定）}
     * 7. 校验位（2个字符）（内容不固定）
     * 8. 帧尾（2个字符）（内容固定）
     * streamId = 帧头 + 控制码 + 帧计数器 + 数据域中的命令标识
     * 长度
     * (帧头：2, 控制码：2, 机器人编号：4, 帧计数器：2, 数据域长度：2, 数据域：2 + N, 校验位：2, 帧尾：2)
     *
     * @param data 数据
     * @return streamId（不包含帧计数器）
     */
    public static String getDataStreamId(String data) {
        if (StrUtil.isEmpty(data) || data.length() < 14) {
            return data;
        }
        // 帧头 + 控制码
        String frameHeaderAndControlCode = StrUtil.sub(data, 0, 4);
        // 数据域中的命令标识
        String commandIdentifier = StrUtil.sub(data, 12, 14);
        return frameHeaderAndControlCode + commandIdentifier;
    }

    /**
     * 获取 streamId（指令标识）
     *
     * @param dataStreamId
     * @return String
     */
    public static String getStreamId(String dataStreamId) {
        if (StrUtil.isEmpty(dataStreamId)) {
            return null;
        }
        int length = dataStreamId.length();
        if (length >= 2) {
            return dataStreamId.substring(length - 2);
        } else {
            return dataStreamId;
        }
    }

    /**
     * 获取 控制类 streamId（下发指令，包含帧计数器）
     *
     * @param data
     * @return String
     */
    public static String getControlStreamId(String data) {
        if (StrUtil.isEmpty(data) || data.length() < 14) {
            return data;
        }
        // 帧头 + 控制码
        String frameHeaderAndControlCode = StrUtil.sub(data, 0, 4);
        // 帧计数器
        String frameCounter = StrUtil.sub(data, 8, 10);
        // 数据域中的命令标识
        String commandIdentifier = StrUtil.sub(data, 12, 14);
        return frameHeaderAndControlCode + frameCounter + commandIdentifier;
    }

    /**
     * 获取控制-帧计数器，针对广播模式，控制-streamId 作为广播标识（redis 计数器）
     *
     * @param streamId
     * @return string
     */
    public static String getControlFrameCounter(String streamId) {
        String cacheKey = CacheNames.ROBOT_MULTICASTGROUP_FRAME_COUNTER + streamId;
        // 帧计数器，0-255
        Long frameCounter = bladeRedis.incrBy(cacheKey, 1L);
        // 初始值为 0，所以需要减 1
        frameCounter = frameCounter - 1;
        if (frameCounter >= ParamConstant.FRAME_COUNTER) {
            // 删除（等同于重置）
            bladeRedis.del(cacheKey);
        }
        // 帧计数器转 16 进制
        return NumberUtil.decToHex(String.valueOf(frameCounter), 2);
    }

    /**
     * 获取控制-帧计数器，针对单播模式，机器人-EUI + 控制-streamId 作为单播标识（redis 计数器）
     *
     * @param devEui
     * @param streamId
     * @return string
     */
    public static String getControlFrameCounter(String devEui, String streamId) {
        String cachePrefix = CacheNames.ROBOT_CONTROL_FRAME_COUNTER + devEui;
        // 帧计数器，0-255
        Long frameCounter = bladeRedis.hIncrBy(cachePrefix, streamId, 1L);
        // 初始值为 0，所以需要减 1
        frameCounter = frameCounter - 1;
        if (frameCounter >= ParamConstant.FRAME_COUNTER) {
            // 删除（等同于重置）
            bladeRedis.hDel(cachePrefix, streamId);
        }
        // 帧计数器转 16 进制
        return NumberUtil.decToHex(String.valueOf(frameCounter), 2);
    }

    /**
     * 获取控制类 streamId
     *
     * @param devEui
     * @param streamId
     * @return string
     */
    public static String getControlStreamId(String devEui, String streamId) {
        if (StrUtil.isEmpty(streamId) || streamId.length() < 4) {
            return "00";
        }
        // 获取控制-帧计数器
        String frameCounterHex = getControlFrameCounter(devEui, streamId);
        // （帧头 + 控制码） + 帧计数器 + 命令标识
        return streamId.substring(0, 4) + frameCounterHex + streamId.substring(streamId.length() - 2);
    }

    /**
     * 获取广播控制类 streamId
     *
     * @param streamId
     * @return string
     */
    public static String getMulticastGroupControlStreamId(String streamId) {
        if (StrUtil.isEmpty(streamId) || streamId.length() < 4) {
            return "00";
        }
        // 获取控制-帧计数器
        String frameCounterHex = getControlFrameCounter(CommandConstant.BROADCAST_NUMBER, streamId);
        // （帧头 + 控制码） + 帧计数器 + 命令标识
        return streamId.substring(0, 4) + frameCounterHex + streamId.substring(streamId.length() - 2);
    }

    /**
     * 根据控制类 streamId 获取帧计数器（截取字符串中，帧计数的部分）
     *
     * @param controlStreamId
     * @return string
     */
    public static String getFrameCounterByControlStreamId(String controlStreamId) {
        if (StrUtil.isEmpty(controlStreamId) || controlStreamId.length() < 6) {
            return "00";
        }
        // controlStreamId ==（帧头 + 控制码） + 帧计数器 + 命令标识，每两个字符为一组
        return controlStreamId.substring(4, 6);
    }

    /**
     * 根据控制类 streamId 获取控制类型（截取字符串中，去除帧计数的部分）
     *
     * @param controlStreamId
     * @return string
     */
    public static String getControlTypeByControlStreamId(String controlStreamId) {
        if (StrUtil.isEmpty(controlStreamId) || controlStreamId.length() < 8) {
            return "00";
        }
        // controlStreamId ==（帧头 + 控制码） + 帧计数器 + 命令标识，每两个字符为一组
        return controlStreamId.substring(0, 4) + controlStreamId.substring(6, 8);
    }

    /**
     * 根据数据上报 data 获取帧计数器（截取字符串中，帧计数的部分）
     * <a href="https://thingcom.yuque.com/rfub4n/aws2ly/faxvm6qrf6h1x8oc#ZRRV4">...</a>
     *
     * @param data
     * @return string
     */
    public static String getFrameCounterByData(String data) {
        if (StrUtil.isEmpty(data) || data.length() < 10) {
            return "00";
        }
        // data ==（帧头-2 + 控制码-2 + 机器人编号-4） + 帧计数器-2 + 数据域长度 + （命令标识 + 参数） + 校验位 + 帧尾
        return data.substring(8, 10);
    }

    /**
     * 获取控制类校验数据缓存key
     *
     * @param devEui
     * @return string
     */
    public static String getControlValidateDataCacheKey(String devEui) {
        return CacheNames.ROBOT_CONTROLDATA + devEui;
    }

    /**
     * 获取广播控制类校验数据缓存key
     *
     * @param devEui
     * @return string
     */
    public static String getMulticastGroupControlValidateDataCacheKey(String devEui) {
        return CacheNames.ROBOT_MULTICASTGROUP_CONTROLDATA + devEui;
    }

    /**
     * 获取控制类初始化数据校验数据缓存key
     *
     * @param devEui
     * @return string
     */
    public static String getControlInitDataCacheKey(String devEui) {
        return CacheNames.ROBOT_CONTROL_INITDATA + devEui;
    }

    /**
     * 是否是状态数据
     *
     * @param streamId
     * @return
     */
    public static boolean isStatusData(String streamId) {
        return validateStreamId(streamId);
    }

    /**
     * 是否是电池数据
     *
     * @param streamId
     * @return
     */
    public static boolean isBatteryData(String streamId) {
        return validateStreamId(streamId);
    }

    /**
     * 是否是电流数据（单个主从机）
     *
     * @param streamId
     * @return
     */
    public static boolean isElectricityData(String streamId) {
        return validateStreamId(streamId);
    }

    /**
     * 是否是电流数据（批量主从机）
     *
     * @param streamId
     * @return
     */
    public static boolean isElectricityDatas(String streamId) {
        if (StrUtil.isEmpty(streamId)) {
            return false;
        }
        return StreamIdConstant.E5.equals(streamId);
    }

    /**
     * 需要更新机器人数据的 streamId
     *
     * @param streamId
     * @return
     */
    public static boolean isUpdateRobotData(String streamId) {
        if (StrUtil.isEmpty(streamId)) {
            return false;
        }
        // StreamIdConstant.B1 ~ StreamIdConstant.B9
        boolean streamIdB1AndB9 = isB0AndB9(streamId);
        return StreamIdConstant.A3.equals(streamId)
                || StreamIdConstant.A6.equals(streamId)
                || streamIdB1AndB9
                || StreamIdConstant.C0.equals(streamId)
                || StreamIdConstant.C2.equals(streamId)
                || StreamIdConstant.C4.equals(streamId)
                || StreamIdConstant.E0.equals(streamId)
                || StreamIdConstant.E2.equals(streamId)
                || StreamIdConstant.E4.equals(streamId)
                || StreamIdConstant.E5.equals(streamId);
    }

    /**
     * 所有的 streamId 都包含机器人编号，6882 + 机器人编号 + ......
     *
     * @param streamId
     * @return
     */
    public static boolean isRobotNumber(String streamId) {
        return StrUtil.isNotEmpty(streamId);
    }

    /**
     * 机器人定时器异常的 streamId
     *
     * @param streamId
     * @return
     */
    public static boolean isRobotTimerException(String streamId) {
        if (StrUtil.isEmpty(streamId)) {
            return false;
        }
        return StreamIdConstant.E6.equals(streamId);
    }

    /**
     * 机器人实时数据用于 WebSocket 发送的 streamId
     *
     * @param streamId
     * @return
     */
    public static boolean isRobotRealDataForWebSocketSend(String streamId) {
        if (StrUtil.isEmpty(streamId)) {
            return false;
        }
        return StreamIdConstant.C0.equals(streamId)
                || StreamIdConstant.C1.equals(streamId)
                || StreamIdConstant.C2.equals(streamId)
                || StreamIdConstant.C4.equals(streamId)
                || StreamIdConstant.A8.equals(streamId);
    }

    /**
     * 是否是：控制指令
     *
     * @param streamId
     * @return boolean
     */
    public static boolean isControlCommand(String streamId) {
        return streamId.startsWith(StreamIdConstant.B);
    }

    /**
     * 校验 streamId
     *
     * @param streamId
     * @return
     */
    private static boolean validateStreamId(String streamId) {
        if (StrUtil.isEmpty(streamId)) {
            return false;
        }
        // StreamIdConstant.B0 ~ StreamIdConstant.B9
        boolean streamIdB0AndB9 = isB0AndB9(streamId);
        return streamIdB0AndB9
                || StreamIdConstant.C4.equals(streamId)
                || StreamIdConstant.E4.equals(streamId);
    }

    /**
     * 校验参数配置 streamId
     *
     * @param streamId
     * @return
     */
    public static boolean validateParamConfigStreamId(String streamId) {
        if (StrUtil.isEmpty(streamId)) {
            return false;
        }
        return StreamIdConstant.C0.equals(streamId)
                || StreamIdConstant.C1.equals(streamId)
                || StreamIdConstant.E0.equals(streamId)
                || StreamIdConstant.E1.equals(streamId);
    }

    /**
     * B0~B9 streamId
     *
     * @param streamId
     * @return
     */
    public static boolean isB0AndB9(String streamId) {
        if (StrUtil.isEmpty(streamId)) {
            return false;
        }
        // StreamIdConstant.B0 ~ StreamIdConstant.B9
        boolean streamIdB0AndB9 = false;
        if (streamId.startsWith(StreamIdConstant.B)) {
            // 取最后一位
            String lastStr = streamId.substring(streamId.length() - 1);
            int lastInt = Func.toInt(lastStr);
            streamIdB0AndB9 = lastInt >= 0 && lastInt <= 9;
        }
        return streamIdB0AndB9;
    }

    /**
     * 获取升级-帧计数器
     */
    public static String getUpgradeFrameCounter(Robot robot) {
        // 帧计数器，0-255
        Long frameCounter = bladeRedis.incrBy(CacheNames.ROBOT_UPGRADE_FRAME_COUNTER, 1L);
        // 初始值为 0，所以需要减 1
        frameCounter = frameCounter - 1;
        if (frameCounter >= ParamConstant.FRAME_COUNTER) {
            // 删除（等同于重置）
            bladeRedis.del(CacheNames.ROBOT_UPGRADE_FRAME_COUNTER);
            // 升级回复缓存清空
            bladeRedis.del(CacheNames.ROBOT_UPGRADE_RECEIVE + robot.getEui());
        }
        // 帧计数器转 16 进制
        return NumberUtil.decToHex(String.valueOf(frameCounter), 2);
    }

    /**
     * 组播升级-帧计数器
     */
    public static String getMulticastGroupFrameCounter() {
        // 帧计数器，0-255
        Long frameCounter = bladeRedis.incrBy(CacheNames.ROBOT_UPGRADE_FRAME_COUNTER, 1L);
        // 初始值为 0，所以需要减 1
        frameCounter = frameCounter - 1;
        if (frameCounter >= ParamConstant.FRAME_COUNTER) {
            // 删除（等同于重置）
            bladeRedis.del(CacheNames.ROBOT_UPGRADE_FRAME_COUNTER);
        }
        // 帧计数器转 16 进制
        return NumberUtil.decToHex(String.valueOf(frameCounter), 2);
    }

    /**
     * 校验是否需要带上通信箱数量、机器人数量
     */
    public static boolean validateNeedGatewayAndRobotCount(String streamId) {
        if (StrUtil.isEmpty(streamId)) {
            return false;
        }
        return streamId.endsWith(CommandEnum.CONTROL_BOARD.getId())
                || streamId.endsWith(CommandEnum.BATTERY.getId())
                || streamId.endsWith(CommandEnum.TIMER.getId())
                || streamId.endsWith(CommandEnum.STOP_BIT.getId())
                || streamId.endsWith(CommandEnum.LORA.getId())
                || streamId.endsWith(CommandEnum.WIND_SPEED.getId())
                || streamId.endsWith(CommandEnum.DAYTIME_PREVENT_CLEAN.getId())
                || streamId.endsWith(CommandEnum.UNLOCK.getId())
                || streamId.endsWith(CommandEnum.LOCK.getId())
                || streamId.endsWith(CommandEnum.MAIN_BOARD_TEMPERATURE.getId());
    }

}

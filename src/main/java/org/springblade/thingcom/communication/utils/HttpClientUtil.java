package org.springblade.thingcom.communication.utils;

import cn.hutool.json.JSONObject;
import cn.hutool.json.XML;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.ParseException;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.params.HttpConnectionParams;
import org.apache.http.params.HttpParams;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.springblade.common.constant.CommonConstant;
import org.springblade.thingcom.core.chirpStack.constant.ParamConstant;

import javax.net.ssl.SSLContext;
import java.io.IOException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@SuppressWarnings({"resource", "deprecation"})
public class HttpClientUtil {

    /**
     * get请求
     */
    public static String doGet(String url, String token) {
        String response = null;
        try {
            HttpClient httpClient = new DefaultHttpClient();
            HttpGet httpGet = new HttpGet(url);
            if (token != null && !token.isEmpty()) {
                httpGet.setHeader(ParamConstant.HEADER_AUTHORIZATION, token);
            }
            HttpResponse httpResponse = httpClient.execute(httpGet);
            if (httpResponse.getStatusLine().getStatusCode() == 200) {
                // 请求和响应都成功了
                HttpEntity entity = httpResponse.getEntity();// 调用getEntity()方法获取到一个HttpEntity实例
                response = EntityUtils.toString(entity, "utf-8");// 用EntityUtils.toString()这个静态方法将HttpEntity转换成字符串,防止
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return response;
    }

    /**
     * delete请求
     */
    public static String doDelete(String url, String token) {
        String response = null;
        try {
            HttpClient httpClient = new DefaultHttpClient();
            HttpDelete httpDelete = new HttpDelete(url);
            if (token != null && !token.isEmpty()) {
                httpDelete.setHeader(ParamConstant.HEADER_AUTHORIZATION, token);
            }
            HttpResponse httpResponse = httpClient.execute(httpDelete);
            if (httpResponse.getStatusLine().getStatusCode() == 200) {
                // 请求和响应都成功了
                HttpEntity entity = httpResponse.getEntity();// 调用getEntity()方法获取到一个HttpEntity实例
                response = EntityUtils.toString(entity, "utf-8");// 用EntityUtils.toString()这个静态方法将HttpEntity转换成字符串,防止
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return response;
    }

    /**
     * doPost
     * url：请求地址
     * params：json字符串
     */
    public static String doPost(String url, String params, String token) throws IOException {
        String response = null;
        HttpClient httpClient = new DefaultHttpClient();
        HttpPost httpPost = new HttpPost(url);
        if (params != null && !"".equals(params)) {
            httpPost.setEntity(new StringEntity(params, "utf-8"));
        }
        httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
        if (token != null && !token.isEmpty()) {
            httpPost.setHeader(ParamConstant.HEADER_AUTHORIZATION, token);
        }
        HttpParams httpParams = httpClient.getParams();
        // 设置超时时间为3秒
        int timeout = CommonConstant.API_TIMEOUT;
        HttpConnectionParams.setConnectionTimeout(httpParams, timeout);
        HttpConnectionParams.setSoTimeout(httpParams, timeout);
        HttpResponse httpResponse = httpClient.execute(httpPost);
        if (httpResponse.getStatusLine().getStatusCode() == 200) {
            // 请求和响应都成功了
            HttpEntity entity = httpResponse.getEntity();// 调用getEntity()方法获取到一个HttpEntity实例
            response = EntityUtils.toString(entity, "utf-8");// 用EntityUtils.toString()这个静态方法将HttpEntity转换成字符串,防止
            // //服务器返回的数据带有中文,所以在转换的时候将字符集指定成utf-8就可以了
        }
        return response;
    }

    /**
     * @return java.lang.String
     * @Description post请求发送xml
     * @Param [url, requestDataXml]
     **/
    public static Map doPostByXml(String url, String requestDataXml) throws Exception {
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse httpResponse = null;

        //创建httpClient连接对象
        //httpClient = HttpClients.createDefault();
        httpClient = HttpClientUtil.createSSLClientDefault();
        //创建post请求连接对象
        HttpPost httpPost = new HttpPost(url);
//        //创建连接请求参数对象，并设置连接参数
//        RequestConfig requestConfig = RequestConfig.custom()
//                .setConnectTimeout(15000)		//连接服务器主机超时时间
//                .setConnectionRequestTimeout(60000)  //连接请求超时时间
//                .setSocketTimeout(6000)				//设置读取响应数据超时时间
//                .build();
//
//        //为httpPost请求设置参数
//        httpPost.setConfig(requestConfig);
        //将上传参数存放到entity属性中
        httpPost.setEntity(new StringEntity(requestDataXml, "GBK"));
        //添加头信息
        httpPost.setHeader("Content-Type", "text/xml");

        String result = "";
        try {
            //发送请求
            httpResponse = httpClient.execute(httpPost);
            //获取返回内容
            HttpEntity httpEntity = httpResponse.getEntity();
            result = EntityUtils.toString(httpEntity, "GBK");
        } catch (IOException e) {
            e.printStackTrace();
        }
        //将请求的返回值转换成json格式
        JSONObject jsonObject = XML.toJSONObject(result);
        Map map = (Map) jsonObject.get("stream");
        if (!"AAAAAAA".equals(map.get("status").toString())) {
            throw new Exception(map.get("statusText").toString());
        }
        return map;
    }

    /**
     * 创建一个SSL信任所有证书的httpClient对象
     *
     * @return
     */
    public static CloseableHttpClient createSSLClientDefault() {
        try {
            // 信任所有
            SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, (TrustStrategy) (chain, authType) -> true).build();
            SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext);
            return HttpClients.custom().setSSLSocketFactory(sslsf).build();
        } catch (KeyManagementException e) {
            e.printStackTrace();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (KeyStoreException e) {
            e.printStackTrace();
        }
        return HttpClients.createDefault();
    }

    /**
     * POST---无参测试
     */
    public static String doPostWuCan(String url) {
        String result = "";
        // 获得Http客户端(可以理解为:你得先有一个浏览器;注意:实际上HttpClient与浏览器是不一样的)
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();

        // 创建Post请求
        HttpPost httpPost = new HttpPost(url);
        // 响应模型
        CloseableHttpResponse response = null;
        try {
            // 由客户端执行(发送)Post请求
            response = httpClient.execute(httpPost);
            // 从响应模型中获取响应实体
            HttpEntity responseEntity = response.getEntity();
            if (responseEntity != null) {
                result = EntityUtils.toString(responseEntity);
            }
        } catch (ClientProtocolException e) {
            e.printStackTrace();
        } catch (ParseException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                // 释放资源
                if (httpClient != null) {
                    httpClient.close();
                }
                if (response != null) {
                    response.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return result;
    }

    /**
     * 模拟浏览器Post
     * url：请求地址
     * params：组合参数，后期拼接到url中；
     */
    public static void doPostClient(String url, StringBuffer params) {

        // 获得Http客户端(可以理解为:你得先有一个浏览器;注意:实际上HttpClient与浏览器是不一样的)
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        // 创建Post请求
        HttpPost httpPost = new HttpPost(url + params);

        // 设置ContentType(注:如果只是传普通参数的话,ContentType不一定非要用application/json)
        httpPost.setHeader("Content-Type", "application/json;charset=utf8");

        // 响应模型
        CloseableHttpResponse response = null;
        try {
            // 由客户端执行(发送)Post请求
            response = httpClient.execute(httpPost);
            // 从响应模型中获取响应实体
            HttpEntity responseEntity = response.getEntity();
        } catch (ClientProtocolException e) {
            e.printStackTrace();
        } catch (ParseException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                // 释放资源
                if (httpClient != null) {
                    httpClient.close();
                }
                if (response != null) {
                    response.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    // 将JsonObject转换为String
    public static String convertJsonObjectToString(JsonObject jsonObject) {
        List<String> keyValuePairs = new ArrayList<>();
        for (Map.Entry<String, JsonElement> entry : jsonObject.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue().getAsString();
            keyValuePairs.add(key + "=" + value);
        }
        return String.join("&", keyValuePairs);
    }
}
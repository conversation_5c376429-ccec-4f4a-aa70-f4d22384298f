package org.springblade.thingcom.logs.service.impl;

import org.springblade.thingcom.core.mybatisPlus.impl.BaseServiceImpl;
import org.springblade.thingcom.logs.entity.WeatherStationLog;
import org.springblade.thingcom.logs.mapper.WeatherStationLogMapper;
import org.springblade.thingcom.logs.service.WeatherStationLogService;
import org.springframework.stereotype.Service;

/**
 * 气象站日志服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-17 18:02:04
 */
@Service
public class WeatherStationLogServiceImpl extends BaseServiceImpl<WeatherStationLogMapper, WeatherStationLog> implements WeatherStationLogService {

}

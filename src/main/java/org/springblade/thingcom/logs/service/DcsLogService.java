package org.springblade.thingcom.logs.service;

import org.springblade.thingcom.core.mybatisPlus.IBaseService;
import org.springblade.thingcom.logs.entity.DcsLog;

import java.time.LocalDateTime;

/**
 * DCS日志服务接口
 *
 * <AUTHOR>
 * @since 2024-07-31 15:36:39
 */
public interface DcsLogService extends IBaseService<DcsLog> {

    /**
     * 检查数据合法性
     *
     * @param entity 实体对象
     * @return 异常消息，正常时为null
     */
    String validateData(DcsLog entity);
    
    /**
     * 新增DCS日志
     *
     * @param entity
     * @return
     */
    boolean saveData(DcsLog entity);

    /**
     * 修改DCS日志
     *
     * @param entity
     * @return
     */
    boolean updateData(DcsLog entity);

    /**
     * 根据时间删除DCS日志
     *
     * @param time
     */
    void deleteByTime(LocalDateTime time);
}

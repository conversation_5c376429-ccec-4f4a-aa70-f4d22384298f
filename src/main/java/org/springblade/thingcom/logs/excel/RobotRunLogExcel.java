package org.springblade.thingcom.logs.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @ClassName RobotRunLogExcel
 * @Description: 机器人运行日志excel类
 * @Author: zgq
 * @Date: 2024/4/26 16:14
 * @Version: 1.0
 **/
@Data
@HeadRowHeight(52)
@ContentRowHeight(18)
public class RobotRunLogExcel implements java.io.Serializable {
    private static final long serialVersionUID = 8938035200177037937L;

    /**
     * 机器人分区
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "机器人分区")
    private String robotRegionalNumber;

    /**
     * 机器人id
     */
    @ExcelIgnore
    private Long robotId;

    /**
     * 机器人名称
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "机器人名称")
    private String robotName;

    /**
     * 日志类型
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "日志类型")
    private String type;

    /**
     * 日志内容
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "日志内容")
    private String content;

    /**
     * 电池状态
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "电池状态")
    private String batteryStatus;

    /**
     * 电池电压，单位 V
     */
    @ColumnWidth(15)
    @ExcelProperty(value = "电压(V)")
    private BigDecimal batteryVoltage;

    /**
     * 电池温度，单位 ℃
     */
    @ColumnWidth(15)
    @ExcelProperty(value = "温度(℃)")
    private BigDecimal batteryTemperature;

    /**
     * 电池剩余电量 %
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "剩余电量")
    private BigDecimal batteryRemainingCapacity;

    /**
     * 电池电流，单位 mA
     */
    @ColumnWidth(15)
    @ExcelProperty(value = "电流(mA)")
    private BigDecimal batteryElectricity;

    /**
     * 数据上报时间
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "时间")
    private LocalDateTime time;
}
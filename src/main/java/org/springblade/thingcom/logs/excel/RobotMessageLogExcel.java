package org.springblade.thingcom.logs.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 机器人报文日志excel类
 *
 * <AUTHOR>
 * @since 2024-04-25 16:24:36
 */
@Data
@HeadRowHeight(52)
@ContentRowHeight(18)
public class RobotMessageLogExcel implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 时间
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "时间")
    private LocalDateTime time;

    /**
     * 日志内容
     */
    @ColumnWidth(130)
    @ExcelProperty(value = "日志内容")
    private String content;
}

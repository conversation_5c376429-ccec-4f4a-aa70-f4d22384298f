package org.springblade.thingcom.logs.mapper;

import org.springblade.thingcom.core.mybatisPlus.IBaseMapper;
import org.springblade.thingcom.logs.entity.ThirdLog;

import java.time.LocalDateTime;

/**
 * 第三方日志数据库访问层
 *
 * <AUTHOR>
 * @since 2024-08-01 15:12:39
 */
public interface ThirdLogMapper extends IBaseMapper<ThirdLog> {

    /**
     * 根据时间删除
     *
     * @param time 时间
     */
    void deleteByTime(LocalDateTime time);
}

package org.springblade.thingcom.logs.wrapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.thingcom.logs.dto.ThirdLogDTO;
import org.springblade.thingcom.logs.entity.ThirdLog;
import org.springblade.thingcom.logs.vo.ThirdLogVO;

import java.util.Objects;

/**
 * 第三方日志包装类
 *
 * <AUTHOR>
 * @since 2024-08-01 15:12:39
 */
public class ThirdLogWrapper extends BaseEntityWrapper<ThirdLog, ThirdLogVO> {

    public static ThirdLogWrapper build() {
        return new ThirdLogWrapper();
    }

    /**
     * 返回视图层所需的字段
     *
     * @param entity
     * @return ThirdLogVO
     */
    @Override
    public ThirdLogVO entityVO(ThirdLog entity) {
        if (Objects.isNull(entity)) {
            return null;
        }
        ThirdLogVO vo = BeanUtil.copy(entity, ThirdLogVO.class);
        return vo;
    }

    /**
     * 设置查询参数
     *
     * @param dto
     * @return LambdaQueryWrapper<ThirdLog>
     */
    public LambdaQueryWrapper<ThirdLog> setQueryParam(ThirdLogDTO dto) {
        LambdaQueryWrapper<ThirdLog> lqw = new LambdaQueryWrapper<>();
        return lqw;
    }
}

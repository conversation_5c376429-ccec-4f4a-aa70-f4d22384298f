package org.springblade.thingcom.logs.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.thingcom.logs.entity.DcsLog;
import org.springblade.thingcom.logs.vo.DcsLogVO;

/**
 * DCS日志包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-07-31 15:36:39
 */
public class DcsLogWrapper extends BaseEntityWrapper<DcsLog, DcsLogVO> {
       
//  private static final DcsLogService dcsLogService;
//
//	static {
//		dcsLogService = SpringUtil.getBean(DcsLogService.class);
//	}

	public static DcsLogWrapper build() {
		return new DcsLogWrapper();
	}

	@Override
	public DcsLogVO entityVO(DcsLog entity) {
	    if (null == entity) {
	        return null;
	    }
		DcsLogVO vo = BeanUtil.copy(entity, DcsLogVO.class);
		return vo;
	}
}

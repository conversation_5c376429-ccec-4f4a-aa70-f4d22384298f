package org.springblade.thingcom.logs.wrapper;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.Func;
import org.springblade.thingcom.communication.enums.BatteryStatusEnum;
import org.springblade.thingcom.data.enums.StatusAnalysisTypeEnum;
import org.springblade.thingcom.device.entity.RobotRegional;
import org.springblade.thingcom.logs.dto.RobotRunLogDTO;
import org.springblade.thingcom.logs.entity.RobotRunLog;
import org.springblade.thingcom.logs.excel.RobotRunLogExcel;
import org.springblade.thingcom.logs.vo.RobotRunLogVO;
import org.springblade.thingcom.translate.utils.TranslateUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 机器人运行日志包装类
 *
 * <AUTHOR>
 * @since 2024-04-20 14:04:19
 */
public class RobotRunLogWrapper extends BaseEntityWrapper<RobotRunLog, RobotRunLogVO> {
    /**
     * 机器人区域实时数据集
     * key: 机器人id
     */
    private final Map<Long, RobotRegional> robotRegionalMap;

    public RobotRunLogWrapper() {
        this.robotRegionalMap = new HashMap<>();
    }

    public RobotRunLogWrapper(Map<Long, RobotRegional> robotRegionalMap) {
        this.robotRegionalMap = robotRegionalMap;
    }

    public static RobotRunLogWrapper build(Map<Long, RobotRegional> robotRegionalMap) {
        return new RobotRunLogWrapper(robotRegionalMap);
    }

    /**
     * 返回视图层所需的字段
     *
     * @param entity
     * @return RobotRunLogVO
     */
    @Override
    public RobotRunLogVO entityVO(RobotRunLog entity) {
        if (Objects.isNull(entity)) {
            return null;
        }
        RobotRunLogVO vo = BeanUtil.copyProperties(entity, RobotRunLogVO.class);
        // 日志类型
        if (Objects.nonNull(entity.getType())) {
            vo.setTypeName(StatusAnalysisTypeEnum.translationValue(entity.getType()));
        }
        // 电池状态
        if (Objects.nonNull(entity.getBatteryStatus())) {
            vo.setBatteryStatusName(BatteryStatusEnum.translationValue(entity.getBatteryStatus()));
        }
        return vo;
    }

    public RobotRunLogVO entityVoExt(Map<String, Object> map) {
        if (null == map) {
            return null;
        }
        RobotRunLogVO vo = BeanUtil.toBean(map, RobotRunLogVO.class);
        // 日志类型
        if (Objects.nonNull(vo.getType())) {
            vo.setTypeName(StatusAnalysisTypeEnum.translationValue(vo.getType()));
        }
        // 电池状态
        if (Objects.nonNull(vo.getBatteryStatus())) {
            vo.setBatteryStatusName(BatteryStatusEnum.translationValue(vo.getBatteryStatus()));
        }
        // 机器人分区
        if (Objects.nonNull(vo.getRobotId())) {
            RobotRegional robotRegional = robotRegionalMap.get(vo.getRobotId());
            if (Objects.nonNull(robotRegional)) {
                vo.setRobotRegionalId(robotRegional.getId());
                vo.setRobotRegionalNumber(robotRegional.getRegionalNumber());
            }
        }
        return vo;
    }

    public List<RobotRunLogVO> listVoExt(List<Map<String, Object>> list) {
        return list.stream().map(this::entityVoExt).collect(Collectors.toList());
    }

    public IPage<RobotRunLogVO> pageVoExt(IPage<Map<String, Object>> pages) {
        List<RobotRunLogVO> records = this.listVoExt(pages.getRecords());
        IPage<RobotRunLogVO> pageVo = new Page(pages.getCurrent(), pages.getSize(), pages.getTotal());
        pageVo.setRecords(records);
        return pageVo;
    }

    public List<RobotRunLogExcel> listToExcel(List<Map<String, Object>> mapList, boolean translate) {
        return mapList.stream()
                .map(map -> {
                    RobotRunLogExcel excel = BeanUtil.toBean(map, RobotRunLogExcel.class);
                    // 日志类型
                    if (Objects.nonNull(excel.getType())) {
                        // translate为真 常量名翻译
                        if (translate) {
                            excel.setType(TranslateUtil.chineseToEnglish
                                    (StatusAnalysisTypeEnum.translationValue(Func.toInt(excel.getType()))));
                        } else {
                            excel.setType(StatusAnalysisTypeEnum.translationValue(Func.toInt(excel.getType())));
                        }
                    }
                    // 电池状态
                    if (Objects.nonNull(excel.getBatteryStatus())) {
                        // translate为真 常量名翻译
                        if (translate) {
                            excel.setType(TranslateUtil.chineseToEnglish
                                    (BatteryStatusEnum.translationValue(Func.toInt(excel.getBatteryStatus()))));
                        } else {
                            excel.setBatteryStatus(BatteryStatusEnum.translationValue(Func.toInt(excel.getBatteryStatus())));
                        }
                    }
                    // 日志内容
                    if (Objects.nonNull(excel.getContent())) {
                        if (translate) {
                            excel.setContent(TranslateUtil.chineseToEnglish
                                    (excel.getContent()));
                        }
                    }
                    // 机器人分区
                    if (Objects.nonNull(excel.getRobotId())) {
                        RobotRegional robotRegional = robotRegionalMap.get(excel.getRobotId());
                        if (Objects.nonNull(robotRegional)) {
                            excel.setRobotRegionalNumber(robotRegional.getRegionalNumber());
                        }
                    }
                    return excel;
                }).collect(Collectors.toList());
    }

    /**
     * 设置查询参数
     *
     * @param dto
     * @return LambdaQueryWrapper<RobotRunLog>
     */
    public LambdaQueryWrapper<RobotRunLog> setQueryParam(RobotRunLogDTO dto) {
        LambdaQueryWrapper<RobotRunLog> lqw = new LambdaQueryWrapper<>();
        // 起始日期
        if (Objects.nonNull(dto.getBeginDate()) && Objects.nonNull(dto.getEndDate())) {
            lqw.between(RobotRunLog::getTime, dto.getBeginDate(), dto.getEndDate());
        }
        // 日志类型
        lqw.eq(Objects.nonNull(dto.getType()), RobotRunLog::getType, dto.getType());
        // 机器人id
        if (StrUtil.isNotBlank(dto.getRobotIds())) {
            lqw.in(RobotRunLog::getRobotId, Func.toLongList(dto.getRobotIds()));
        }
        // 时间倒序
        lqw.orderByDesc(RobotRunLog::getTime);
        return lqw;
    }

}
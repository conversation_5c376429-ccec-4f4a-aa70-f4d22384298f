package org.springblade.thingcom.logs.vo;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.EqualsAndHashCode;
import org.springblade.thingcom.logs.entity.ThirdLog;

/**
 * 第三方日志出参类
 *
 * <AUTHOR>
 * @since 2024-08-01 15:12:39
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties({"deleted", "updateTime", "updateId"})
public class ThirdLogVO extends ThirdLog {
    private static final long serialVersionUID = 1L;

}

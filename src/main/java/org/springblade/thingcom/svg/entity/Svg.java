package org.springblade.thingcom.svg.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springblade.thingcom.core.mybatisPlus.entity.BaseEntity;

import java.io.Serializable;

/**
 * svg实体类
 *
 * <AUTHOR>
 * @since 2024-11-08 18:23:06
 */
@Data
@TableName("tb_svg")
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class Svg extends BaseEntity<Svg> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 内容
     */
    private String content;

    /**
     * 绑定的所有分区的id数组，用逗号隔开
     */
    private String dataKeyArray;

}
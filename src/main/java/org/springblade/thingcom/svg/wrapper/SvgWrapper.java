package org.springblade.thingcom.svg.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.thingcom.svg.entity.Svg;
import org.springblade.thingcom.svg.vo.SvgVO;

import java.util.Objects;

/**
 * svg包装类
 *
 * <AUTHOR>
 * @since 2024-11-08 18:23:07
 */
public class SvgWrapper extends BaseEntityWrapper<Svg, SvgVO> {

    public static SvgWrapper build() {
        return new SvgWrapper();
    }

    /**
     * 返回视图层所需的字段
     *
     * @param entity
     * @return SvgVO
     */
    @Override
    public SvgVO entityVO(Svg entity) {
        if (Objects.isNull(entity)) {
            return null;
        }
        SvgVO vo = BeanUtil.copy(entity, SvgVO.class);
        return vo;
    }

}
